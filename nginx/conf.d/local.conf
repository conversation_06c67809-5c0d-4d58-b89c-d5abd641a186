#limit_req_zone $binary_remote_addr zone=one:20m rate=60r/m;


#events { worker_connections 1024; }
# first we declare our upstream server, which is our Gunicorn application
upstream api {
    # docker will automatically resolve this to the correct address
    # because we use the same name as the service: "web"
    server api:8000;
}
upstream spa {
    server spa:80;
    #server frontend:8080;
}


server {
    #client_body_timeout 5s;
    #client_header_timeout 5s;
    client_body_timeout 12;
    client_header_timeout 12;
    #keepalive_timeout 15;
    #send_timeout 10;
    #limit_conn conn_limit_per_ip 10;
    #limit_req zone=req_limit_per_ip burst=10 nodelay;    
    #limit_req zone=one;
    #listen 443 ssl;
    listen 80;
    #ssl on;
    server_name localhost #vapardev.azurefd.net;
    large_client_header_buffers 8 64k;
    #ssl_password_file /etc/ssl/certs/serverInt.pem;
    
    #ssl_certificate    /etc/letsencrypt/live/vapardev.azurefd.net/fullchain.pem;
    #ssl_certificate_key    /etc/letsencrypt/live/vapardev.azurefd.net/privkey.pem;
    
    log_subrequest on;
    # if ($request_method !~ ^(GET|HEAD|POST)$ ) {
    # return 403;
    # }
    location /api/ {
      proxy_pass http://api;
      proxy_http_version 1.1;
      proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection 'upgrade';
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_cache_bypass $http_upgrade;
      #add_header 'Access-Control-Allow-Origin' '*';
      add_header 'Access-Control-Allow-Credentials' 'true';
      add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS';
      add_header 'Access-Control-Allow-Headers' 'Access-Control-Allow-Orgin,XMLHttpRequest,Accept,Authorization,Cache-Control,Content-Type,DNT,If-Modified-Since,Keep-Alive,Origin,User-Agent,X-Mx-ReqToken,X-Requested-With,Access-Control-Request-Headers,Access-Control-Request-Method';
    }

    location / {
      proxy_read_timeout 300s;
      proxy_connect_timeout 75s;
      proxy_pass http://spa;
      proxy_http_version 1.1;
      proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection 'upgrade';
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_cache_bypass $http_upgrade;
      #add_header' Access-Control-Allow-Origin' '*';
      add_header 'Access-Control-Allow-Credentials' 'true';
      add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS';
      add_header 'Access-Control-Allow-Headers' 'Access-Control-Allow-Orgin,XMLHttpRequest,Accept,Authorization,Cache-Control,Content-Type,DNT,If-Modified-Since,Keep-Alive,Origin,User-Agent,X-Mx-ReqToken,X-Requested-With';
      
    }
    location ~ /.well-known/acme-challenge {
      allow all;
      root /var/www/html;
    }
    
  
}
    
