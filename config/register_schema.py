from typing import Type

from pydantic import BaseModel

_ADDITIONAL_SCHEMA_COMPONENTS = {}


def pydantic_to_schema(cls: Type[BaseModel]) -> dict:
    """
    Use a pydantic model type as part of the OpenAPI schema.
    """
    schema = cls.model_json_schema(ref_template="#/components/schemas/{model}")
    if "$defs" in schema:
        _ADDITIONAL_SCHEMA_COMPONENTS.update(schema.pop("$defs"))
    return schema


def custom_components_postprocessing_hook(result, generator, request, public):
    """
    https://drf-spectacular.readthedocs.io/en/latest/customization.html#step-6-postprocessing-hooks
    """

    result["components"]["schemas"].update(_ADDITIONAL_SCHEMA_COMPONENTS)
    return result
