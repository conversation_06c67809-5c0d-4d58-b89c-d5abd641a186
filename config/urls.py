"""vapar URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from django.contrib import admin
from django.contrib.staticfiles.urls import staticfiles_urlpatterns
from drf_spectacular.views import SpectacularSwaggerView, SpectacularRedocView, SpectacularAPIView
from django.urls import include, path
from django.conf import settings
from rest_framework_simplejwt import views as jwt_views

from api.base.urls import urlpatterns as base_url_patterns
from api.dashboard.urls import urlpatterns as dashboard_url_patterns
from api.defects.urls import urlpatterns as defect_url_patterns
from api.inspections.urls import asset_urls as asset_url_patterns
from api.inspections.urls import external_share_links as external_share_link_url_patterns
from api.inspections.urls import urlpatterns as inspections_url_patterns
from api.organisations.urls import urlpatterns as organisations_url_patterns
from api.recommendations.urls import urlpatterns as recommendations_url_patterns
from api.external.urls import urlpatterns as externalapi_url_patterns
from api.users.urls import urlpatterns as user_url_patterns
from api.exports.urls import urlpatterns as export_url_patterns
from api.imports.urls import urlpatterns as import_url_patterns
from api.users.views import (
    LoginView,
    RegisterView,
    ResetPasswordConfirmView,
    ResetPasswordView,
    VerifyView,
    activate_user,
    validate_reset_password_link,
)


apipatterns = [
    path("admin", admin.site.urls),
    path("auth/login", LoginView.as_view(), name="login"),
    path("auth/register", RegisterView.as_view(), name="register"),
    path("auth/token/refresh", jwt_views.TokenRefreshView.as_view(), name="token_refresh"),
    path("auth/token/verify", VerifyView.as_view(), name="token_verify"),
    path("auth/activate/<uidb64>/<token>/", activate_user, name="activate"),
    path("auth/reset-password", ResetPasswordView.as_view(), name="reset_password"),
    path("auth/reset-password/confirm", ResetPasswordConfirmView.as_view(), name="reset_password_confirm"),
    path("auth/reset-password/<uidb64>/<token>/", validate_reset_password_link, name="activate"),
    path("", include(base_url_patterns), name="base"),
    path("", include(user_url_patterns), name="users"),
    path("", include(dashboard_url_patterns), name="dashboard"),
    path("", include(defect_url_patterns), name="defects"),
    path("", include(inspections_url_patterns), name="inspections"),
    path("", include(organisations_url_patterns), name="organisations"),
    path("", include(recommendations_url_patterns), name="repairs"),
    path("", include(externalapi_url_patterns), name="external"),
    path("", include(asset_url_patterns), name="assets"),
    path("", include(export_url_patterns), name="exports"),
    path("", include(import_url_patterns), name="imports"),
]

urlpatterns = [
    path("", include(external_share_link_url_patterns)),
    path("api/v3/", include(apipatterns)),
    path("", SpectacularSwaggerView.as_view(url_name="schema"), name="schema-swagger-ui"),
    path("redoc/", SpectacularRedocView.as_view(url_name="schema"), name="schema-redoc"),
    path("json/", SpectacularAPIView.as_view(), name="schema"),
]

if settings.DEBUG:
    urlpatterns.append(path("api-auth/", include("rest_framework.urls")))
    urlpatterns.append(path("__debug__/", include("debug_toolbar.urls")))
    urlpatterns += staticfiles_urlpatterns()
