# VAPAR API

An API for the VAPAR Solutions. Provides the ability for applications and services within the VAPAR Solutions to interact with the VAPAR data.

## Dependencies
- Docker
- Pip
- Python 3.11
- IDE
- GDAL
- Weasyprint
- Redis (redis.service must be enabled/started)
- Python-dotenv

## Environment Variables
Any environment variables should be placed in a `.env` file in the root directory.

## Install Pip Dependencies
Ensure BITBUCKET_VAPAR_REPO_TOKEN is set in your `.env` file, then install the pip dependencies via `pip install -r requirements.txt`.

## Run the Application
The application is setup to run locally with python or alternatively with Docker

### Docker
The Docker network contains the necessary services and applications to run the VAPAR Frontend if desired. To run the API in Docker on localhost:8000 
````
docker-compose -f docker-compose.yml up
````
To shutdown the application
````
docker-compose -f docker-compose.yml down --remove-orphans
````

### Local
Start the Local API Backend using the python script
````
python src/manage.py runserver 0.0.0.0:8000
````

#### Troubleshooting
When running via the python script please ensure <PERSON><PERSON><PERSON> is present
````
brew install gdal --HEAD
````
````
brew install gdal
````

By default DOCKER has the path set for GDAL, for running the python script if not on a Mac merely add the path in an environment variable.
````
GDAL_LIBRARY_PATH
````
````
GEOS_LIBRARY_PATH
````

To install Weasyprint on MacOS (for other operating systems see: https://doc.courtbouillon.org/weasyprint/stable/first_steps.html#installation)
````
brew install weasyprint
sudo ln -s /opt/homebrew/opt/glib/lib/libgobject-2.0.0.dylib /usr/local/lib/gobject-2.0
sudo ln -s /opt/homebrew/opt/pango/lib/libpango-1.0.dylib /usr/local/lib/pango-1.0
sudo ln -s /opt/homebrew/opt/harfbuzz/lib/libharfbuzz.dylib /usr/local/lib/harfbuzz
sudo ln -s /opt/homebrew/opt/fontconfig/lib/libfontconfig.1.dylib /usr/local/lib/fontconfig-1
sudo ln -s /opt/homebrew/opt/pango/lib/libpangoft2-1.0.dylib /usr/local/lib/pangoft2-1.0
````

## Swagger
Swagger is viewable on `localhost:8000` when `DEBUG=True` is in your `.env` file.

## Endpoints
Endpoints for the API should be based on OpenAPI specifications. API entry can be found at `api/v2`.
````
GET users/
````
````
POST users/
````
````
PATCH users/1
````
````
GET users/1
````