version: '3.6'

services:
    api:
        #build: .
        #command: sh -c "gunicorn --workers=4 --timeout=300 config.wsgi:application -b 0.0.0.0:8000 --limit-request-line 0"
        image: itauedevcrvapar.azurecr.io/vapar-api:latest
        ports:
            - "8000:8000"
        expose:
            - 8000
        depends_on:
            - redis
        environment:
            - DB_HOST=vapardevdb.postgres.database.azure.com
            - DB_NAME=ix
            - DB_USER=dev@vapardevdb
            - DB_PASS=pa55vapar!team
            - DB_PORT=5432
    #spa:
    #    image: itauedevcrvapar.azurecr.io/vapar-frontend:3.0.0.rc8
    #    restart: "no"
    #    environment:
    #        - REACT_APP_PROXY_HOST=local-backend
    #    depends_on:
    #        - api
    redis:
        image: "redis:alpine"

volumes:
    web-root:
    dbdata:
    
        
