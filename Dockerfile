FROM python:3.10-slim-bullseye

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

ARG DB_HOST
ARG DB_NAME
ARG DB_USER
ARG DB_PORT
ARG BITBUCKET_VAPAR_REPO_TOKEN

# Set work directory
WORKDIR /app

# Copy files early for better caching
COPY . .

# Install system dependencies in one layer
RUN set -ex && \
    apt-get update -y && \
    apt-get upgrade -y && \
    apt-get install -y --no-install-recommends \
    openjdk-11-jre-headless \
    default-jre \
    pango1.0-tests \
    gdal-bin \
    libgdk-pixbuf2.0-0 \
    libcairo2-dev \
    libpango1.0-0 \
    binutils \
    libproj-dev \
    postgresql-client \
    libpangocairo-1.0-0 \
    shared-mime-info \
    python3-pip \
    python3-cffi \
    python3-brotli \
    libharfbuzz0b \
    libpangoft2-1.0-0 \
    git && \
    apt-get update --fix-missing && \
    rm -rf /var/lib/apt/lists/*

# Install Python dependencies
RUN pip install --upgrade pip && \
    pip install pipenv psycopg2-binary drf-extra-fields django-treebeard azure-storage-blob gunicorn django-cors-headers && \
    pip install -r requirements.txt

EXPOSE 8000

# Add any static environment variables needed by Django or your settings file here:
ENV DJANGO_SETTINGS_MODULE=config.settings

CMD ["sh", "-e", "entrypoint.sh"]