from rest_framework.generics import ListAP<PERSON><PERSON>iew
from djangorestframework_camel_case.parser import CamelCaseJ<PERSON>NParser
from djangorestframework_camel_case.render import Camel<PERSON><PERSON><PERSON><PERSON><PERSON>enderer
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Case, When, Value, IntegerField

from api.base.models import Header
from api.base.serializers import HeaderSerializer
from api.common.permissions import IsStandardUser

SKIPPED_HEADERS = ["opposite of direction"]


class HeaderList(ListAPIView):
    permission_classes = [IsStandardUser]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    queryset = Header.objects.all().order_by("name")
    serializer_class = HeaderSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ["name"]

    header_order = (
        "AssetID",
        "LengthSurveyed",
        "Date",
        "LocationStreet",
        "UpstreamNode",
        "DownstreamNode",
        "Direction",
        "Material",
        "HeightDiameter",
        "StructuralGrade",
        "ServiceGrade",
        "GeneralRemarks",
    )

    def get(self, request, *args, **kwargs):
        is_filterable = request.query_params.get("is_filterable", None)
        is_editable = request.query_params.get("is_editable", None)

        if is_filterable is not None:
            self.queryset = self.queryset.filter(is_filterable=is_filterable)

        if is_editable is not None:
            self.queryset = self.queryset.filter(is_editable=is_editable)

        preserved = Case(
            *[When(name=header, then=Value(pos)) for pos, header in enumerate(self.header_order)],
            output_field=IntegerField(),
        )
        self.queryset = self.queryset.annotate(sort_order=preserved).order_by("sort_order")

        return super().get(request, *args, **kwargs)
