import uuid
from django.db import models


class Header(models.Model):
    MAX_NAME_LENGTH = 100

    class HeaderType(models.TextChoices):
        ASSET = "asset", "Asset"
        INSPECTION = "inspection", "Inspection"

    uuid = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=MAX_NAME_LENGTH, unique=True)
    type = models.CharField(choices=HeaderType.choices, max_length=11)
    mapped_mpl_field = models.CharField(max_length=100)
    created_at = models.DateTimeField(auto_now_add=True, blank=False)
    display_name = models.CharField(max_length=100, blank=True, null=True)
    is_editable = models.BooleanField(default=False)
    is_filterable = models.BooleanField(default=False)
    data_type = models.CharField(max_length=100, blank=True, null=True)
    options = models.JSONField(blank=True, null=True)

    class Meta:
        db_table = "inspections_header"
