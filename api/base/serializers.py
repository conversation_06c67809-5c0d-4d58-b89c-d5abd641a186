from drf_spectacular.types import OpenApiTypes
from drf_spectacular.utils import extend_schema_field
from rest_framework import serializers

from api.base.models import Header


class HeaderSerializer(serializers.ModelSerializer):
    class Meta:
        model = Header
        fields = ["uuid", "name", "display_name", "is_filterable", "data_type", "type", "options", "mapped_mpl_field"]
        read_only_fields = ["uuid", "created_at", "type"]


@extend_schema_field(OpenApiTypes.STR)
class SewerDataField(serializers.Field):
    def to_representation(self, value):
        return "Sewer" if value else "Stormwater"

    def to_internal_value(self, data):
        if data in ("Sewer", True):
            return True

        if data in ("Stormwater", False):
            return False

        raise serializers.ValidationError("Invalid value for sewer_data field")
