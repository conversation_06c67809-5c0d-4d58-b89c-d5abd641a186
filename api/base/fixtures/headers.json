[{"model": "base.header", "pk": "04453617-9aa3-4b49-a90e-cb383ede3b52", "fields": {"name": "ClientDefined15", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-05-08T02:36:16.844Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "078cb335-8a88-4c48-9ff9-4455c5fabca6", "fields": {"name": "Standard", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:34.723Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "0abd6186-a53c-47fb-9931-294a05ce84cd", "fields": {"name": "DepthAtStartNode", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:29.605Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "0ddabd9a-a8a7-4e16-80c0-dd2b824437d4", "fields": {"name": "Expected<PERSON><PERSON>th", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:30.488Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "11b219d8-ae79-4905-b1ce-3d0cd12412d2", "fields": {"name": "DownEasting", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:29.834Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "13da5c27-84bf-45ce-95f2-418b74796831", "fields": {"name": "GeneralRemarks", "type": "inspection", "mapped_mpl_field": "inspection_notes", "created_at": "2023-06-08T01:54:30.720Z", "display_name": "Inspection Notes", "is_editable": true, "is_filterable": false, "data_type": "string", "options": null}}, {"model": "base.header", "pk": "1450ed30-5926-4e04-bdbc-1261a9c24efb", "fields": {"name": "Client", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:28.469Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "14fe8eaa-060b-42b5-b172-fa938fa2c301", "fields": {"name": "SurveyCompany", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:35.300Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "184c5759-9d80-4207-be0f-c3f6b90be55c", "fields": {"name": "HeightDiameter", "type": "asset", "mapped_mpl_field": "diameter", "created_at": "2023-06-08T01:54:30.843Z", "display_name": "Diameter", "is_editable": true, "is_filterable": true, "data_type": "number", "options": null}}, {"model": "base.header", "pk": "1b2573b0-df4a-4f9b-937f-b7394af581da", "fields": {"name": "PurposeOfInspection", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:34.115Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "1c029f63-70e4-4511-ad3b-39c787572666", "fields": {"name": "FlowControlMeasures", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:30.658Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "1e2775a9-8bc7-45e1-b0c0-0eaf835e920d", "fields": {"name": "ContractorsJobRef", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:29.264Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "1ebb8de0-500a-43dd-833a-fe0212ba0025", "fields": {"name": "InspectionCompletionStatus", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:30.899Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "1f912bf8-8afc-4f0d-886c-2dfe8ad4f8db", "fields": {"name": "LiningMaterial", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:31.292Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "2082238f-572a-4552-9823-34fcb5331896", "fields": {"name": "PipeSegmentReference", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:33.662Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "2316063f-9608-47df-abba-66ae8bcf8fa7", "fields": {"name": "ServicePeakScore", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:34.473Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "253243a9-ef6a-4ba6-8d48-54e164b20cda", "fields": {"name": "Criticality", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:29.322Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "28161956-6810-4781-917c-e9f2094539bf", "fields": {"name": "Node3GridRefX", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:32.980Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "28b4efa5-1ec1-4f69-8ea7-f5c655d53ac1", "fields": {"name": "TypeOfDrainSewer", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:35.804Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "291a0da2-a377-4e83-9fa4-366289e9c9d4", "fields": {"name": "SheetNumber", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:34.668Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "2c76e908-5b32-4bb6-bead-e8f1fa195ce8", "fields": {"name": "LocationStreet", "type": "asset", "mapped_mpl_field": "name", "created_at": "2023-06-08T01:54:31.572Z", "display_name": "Location", "is_editable": true, "is_filterable": true, "data_type": "string", "options": null}}, {"model": "base.header", "pk": "2e2574db-5beb-4e9d-892d-be28f1fe0fc6", "fields": {"name": "CircLocStartLat", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:28.412Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "2ecd3507-9dc7-47d2-98bc-08c7f8386600", "fields": {"name": "DivisionDistrict", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:29.776Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "30635b32-65f7-424f-a945-c765df0e7d83", "fields": {"name": "PreliminaryServicePeakGrade", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:33.891Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "316a6643-7550-4472-9815-94075c862647", "fields": {"name": "ReferencePoint", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:34.171Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "31839d43-fa88-4b3a-8c2e-ff140e37ac9b", "fields": {"name": "ClientDefined4", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:28.751Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "35d6efa1-f3cd-4e7f-80e2-417602f74b2c", "fields": {"name": "MHVerticalDatum", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:36.451Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "37c7dc06-3c44-40db-bb23-b22833d251df", "fields": {"name": "PipeJointLength", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:33.606Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "3b2ead02-7e71-42a8-af25-693e70d9ba04", "fields": {"name": "Temperature", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:35.402Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "3d38d2e8-8410-4191-a3c0-fef476404c4f", "fields": {"name": "DownElevation", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:29.897Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "3e140d9a-b8e1-4f95-9c6b-ee252407af43", "fields": {"name": "UpRimToInvert", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:36.221Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "46c5e176-f46b-49f1-b9ae-31614d5d58ee", "fields": {"name": "Precleaned", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:33.830Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "4ce2fd7b-dd42-49b0-bd51-5185d7e522eb", "fields": {"name": "GPS Accuracy", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:30.782Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "4ef9b123-9582-4c23-9eb1-967dda2bc501", "fields": {"name": "LiningType", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:31.348Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "4f83bd6b-8e8f-4952-94e7-505eba80f635", "fields": {"name": "ClientDefined16", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-05-08T02:36:16.959Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "50e6399d-7a21-4afd-9ef0-791ad0de7d61", "fields": {"name": "MapDatum", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:31.914Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "5138310c-4bc3-4a84-822c-f10235c971de", "fields": {"name": "StartNodeGridRefY", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:34.892Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "51eee565-03bd-43a5-9c26-ba4b04a97c85", "fields": {"name": "LocationStreetDownstreamNode", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:31.629Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "51f47f5a-87a4-48ae-8cba-391cbce70478", "fields": {"name": "MHCoordinateSys", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:32.286Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "54874ee9-4121-4fc8-a10e-066e5cbac0d8", "fields": {"name": "YearRenewed", "type": "asset", "mapped_mpl_field": "repair_completed_date", "created_at": "2023-06-08T01:54:37.236Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": "date", "options": null}}, {"model": "base.header", "pk": "5569781a-faf2-4c54-bd81-d2dd65257b14", "fields": {"name": "<PERSON><PERSON><PERSON>", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:37.041Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "5569b600-b02e-4af9-810c-10b83d56b7b5", "fields": {"name": "DrawingNumber", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:30.429Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "55ce492c-51be-49f5-b410-982669da0b52", "fields": {"name": "DownstreamNodeType", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:30.311Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "569c7e4a-dad0-4ba2-8f7e-5d7a5b29f367", "fields": {"name": "ClientDefined7", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:28.925Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "577e6be3-61b8-4226-b2f6-2ca2fd042f58", "fields": {"name": "UpstreamNode", "type": "asset", "mapped_mpl_field": "upstream_node", "created_at": "2023-06-08T01:54:36.278Z", "display_name": "Upstream Node", "is_editable": true, "is_filterable": true, "data_type": "string", "options": null}}, {"model": "base.header", "pk": "586fe0bf-0fe9-4504-a3aa-726ba26e5942", "fields": {"name": "ParallelLine", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:33.419Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "58a9f101-0387-4847-9f27-6185b77061dc", "fields": {"name": "ClientDefined9", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:29.039Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "58aa11be-5ec6-4fa1-b160-8bb6885a7439", "fields": {"name": "StartNodeGridRefX", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:34.834Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "59cd0410-9127-47ea-bc03-f1e846468f3c", "fields": {"name": "Direction", "type": "inspection", "mapped_mpl_field": "direction", "created_at": "2023-06-08T01:54:29.719Z", "display_name": "Direction", "is_editable": true, "is_filterable": true, "data_type": "options", "options": ["Upstream", "Downstream", "Unknown"]}}, {"model": "base.header", "pk": "5df0c89c-8e70-4c5d-be64-e80b57339deb", "fields": {"name": "VideoVolumeRef", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:36.925Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "601eb87e-69ad-4c15-9d2c-79a41e0f6d55", "fields": {"name": "SurchargeEvidence", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:35.239Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "6054551f-be85-432f-a486-f47b4b30333a", "fields": {"name": "StartNodeGridRef", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:34.779Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "60d324a5-ca56-4f53-88d7-fb68b8944dc4", "fields": {"name": "YearConstructed", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:37.180Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "60fa6adb-9111-498c-82c4-67b5b5ff2ec6", "fields": {"name": "NameOfConduitSys", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:32.460Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "62ef07ca-fc5f-4a65-bf3d-51e2f25de746", "fields": {"name": "<PERSON><PERSON><PERSON>", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:34.585Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "63947516-527b-4feb-bf0c-614a2a052db6", "fields": {"name": "ClientDefined17", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-05-08T02:36:17.085Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "656d863c-e398-450d-b462-1ac4e93139cb", "fields": {"name": "JointingMethod", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:31.070Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "667eb269-fbf5-4ac9-ab0a-a2391305a8b9", "fields": {"name": "Weather", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:36.985Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "68b9f14e-5952-49ab-ac91-007e467b2787", "fields": {"name": "Material", "type": "asset", "mapped_mpl_field": "material", "created_at": "2023-06-08T01:54:32.026Z", "display_name": "Material", "is_editable": true, "is_filterable": true, "data_type": "string", "options": null}}, {"model": "base.header", "pk": "69db29d1-c1a1-4852-96b6-2fe06573d25b", "fields": {"name": "ClientDefined11", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-05-08T02:36:16.371Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "6a0a2b04-9d45-4198-a02c-bd3923f9eb2f", "fields": {"name": "TidalInfluence", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:35.458Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "6b52d1b8-8dea-4a8c-8b28-7f1d64a45bc5", "fields": {"name": "NameOfSurveyor", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:32.632Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "6bb0917f-df68-4990-872d-1053718c81c3", "fields": {"name": "PONumber", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:33.774Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "6dc17ef6-1830-4d5b-a95d-938a1d0c447f", "fields": {"name": "Node1GridRefX", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:32.688Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "6e0b99fa-193a-417a-98a5-d350ed545125", "fields": {"name": "PhotographicVolume", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:33.550Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "6f399782-06c0-447e-8d7b-46561086d080", "fields": {"name": "DepthAtDownstreamNode", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:29.492Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "732192bc-ef6d-4e38-ab2d-fcebcdf789a3", "fields": {"name": "FinishNodeGridRef", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:30.543Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "733d2e1b-88ec-4618-958b-9d13892c5829", "fields": {"name": "LongLocStartLat", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:31.859Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "775a5775-c35f-4922-bb7b-e3382e315448", "fields": {"name": "MapGridZone", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:31.970Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "78e11417-7d59-49b7-8d18-dbd7be4ae842", "fields": {"name": "ClientDefined12", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-05-08T02:36:16.506Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "78fe4754-7bf7-4c57-832b-aeb6d1afce89", "fields": {"name": "TotalStructuralScore", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:35.658Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "7a4e53ec-ab40-4a2a-903b-a817d402b2f8", "fields": {"name": "PreliminaryStructuralPeakGrade", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:33.947Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "7f3942c5-355c-45cd-a92b-ffbefcdb5cdf", "fields": {"name": "MediaLabel", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:32.138Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "7f9a2ab0-a7ad-4393-83d9-b004680471b4", "fields": {"name": "DownstreamNode", "type": "asset", "mapped_mpl_field": "downstream_node", "created_at": "2023-06-08T01:54:30.255Z", "display_name": "Downstream Node", "is_editable": true, "is_filterable": true, "data_type": "string", "options": null}}, {"model": "base.header", "pk": "7fefc6c5-d21b-476c-a085-bf694d4b4500", "fields": {"name": "Node3Ref", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:33.099Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "809a12e9-d6d8-41a0-93c3-c060d909c98b", "fields": {"name": "ClientDefined1", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:28.526Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "80f0c697-3167-498c-8599-fd77ec0b24d7", "fields": {"name": "OriginalCodingSystem", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:33.354Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "81e4ae6f-229a-458a-911d-16a670acd3b3", "fields": {"name": "StructuralMeanScore", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:35.105Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "832f823f-5f0e-41bd-91a4-93be65552c3b", "fields": {"name": "ClientDefined3", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:28.694Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "85b0161d-816e-481d-9082-8b2e011788bb", "fields": {"name": "StructuralGrade", "type": "inspection", "mapped_mpl_field": "condition_rating", "created_at": "2023-06-08T01:54:35.048Z", "display_name": "Structural Grade", "is_editable": false, "is_filterable": true, "data_type": "number", "options": null}}, {"model": "base.header", "pk": "882a5f44-4d2d-4c09-8fc0-2c48f5bd1538", "fields": {"name": "StructuralPeakScore", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:35.164Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "887754f5-7de7-45ca-9705-f1f8711c47b8", "fields": {"name": "Node1GridRefY", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:32.744Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "89f5a275-f7c4-40d7-ab3e-a952b5e1a913", "fields": {"name": "TranslatedStandard", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:35.748Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "8a48b1c0-088f-4db8-824f-f5eb742f44e3", "fields": {"name": "VideoImageFormat", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:36.725Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "8c4f4f6a-356a-49c6-9e15-e90c7e590e64", "fields": {"name": "UpNorthing", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:36.109Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "8e8a50c9-5c5e-4838-a371-ee51f7532286", "fields": {"name": "SetupLocation", "type": "inspection", "mapped_mpl_field": "opposite of direction", "created_at": "2023-06-08T01:54:34.529Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": "string", "options": null}}, {"model": "base.header", "pk": "8eb92eff-07b2-41d4-83c7-88630758ea66", "fields": {"name": "DownRimToInvert", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:30.199Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "8f2490a7-3089-4fef-8d24-ad38e24258f9", "fields": {"name": "ClientDefined10", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:28.582Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "8fec4535-12bb-497f-92ba-a2301c52a262", "fields": {"name": "PressureValue", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:34.003Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "90cd0717-a462-4d63-8bfb-2b18ba9c717c", "fields": {"name": "LocationRisk", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:31.516Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "9227dbbf-d623-40ae-8544-f6b592bcb9d7", "fields": {"name": "UpElevation", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:35.914Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "95fcc131-9947-4f0c-ac3f-289df540e555", "fields": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:28.297Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "96d1a467-9c00-4902-8d67-b8e6bbb89c14", "fields": {"name": "VideoImageStorage", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:36.870Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "9e1d89cb-dbef-4c26-83eb-ef3d21c916f5", "fields": {"name": "NameOfPipeSystem", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:32.517Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "9f01ca1f-4f64-4f96-8470-931d298691aa", "fields": {"name": "UpEasting", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:35.860Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "9fb2a9d5-ca89-4f05-92b3-e16341455e66", "fields": {"name": "VideoImageFilename", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:36.599Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "a37cd83e-08f2-4a34-9ce2-868b949f6dcb", "fields": {"name": "Node1Ref", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:32.808Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "a80da5e4-5fe0-4e2f-84a3-eabe24234912", "fields": {"name": "ClientDefined18", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-05-08T02:36:17.200Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "a96767ae-b8e1-46ad-9616-733da4dcf680", "fields": {"name": "LocationStreetUpstreamNode", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:31.687Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "a9d55a06-9c9c-48af-8b16-2fedcd095f2f", "fields": {"name": "NameOfReviewer", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:32.572Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "aa664bad-1088-432d-926a-b286c81931a5", "fields": {"name": "StartNodeRef", "type": "inspection", "mapped_mpl_field": "start_node", "created_at": "2023-06-08T01:54:34.967Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": "string", "options": null}}, {"model": "base.header", "pk": "abf85997-2c41-479e-9b37-10f2289dbdd9", "fields": {"name": "UpstreamNodeType", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:36.335Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "ace6021c-890d-4b6d-a2a3-7f6ec1855658", "fields": {"name": "Node2GridRefY", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:32.923Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "ada7c8c2-3901-42bd-896d-146e0bd5e439", "fields": {"name": "DownGradeToInvert", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:29.953Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "aeb13168-2b5a-4a21-828f-c2c14479b08b", "fields": {"name": "MethodOfInspection", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:32.194Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "b113ef34-c622-4478-b0df-2eb00f22d1c2", "fields": {"name": "Node2GridRefX", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:32.866Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "b1c8c021-579d-4782-bccd-1573595610a0", "fields": {"name": "Node3GridRefY", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:33.036Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "b1eee34d-767f-48a0-9439-35c82a6052e1", "fields": {"name": "ClientsJobRef", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:29.096Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "b49cc030-318e-4e0a-b207-ad9c029eef2f", "fields": {"name": "Project", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:34.058Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "ba61f12e-367a-4161-b3a3-59f353bd2902", "fields": {"name": "ClientDefined2", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:28.637Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "bb504a1a-005f-4654-b472-022db3564086", "fields": {"name": "UpGradeToInvert", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:35.969Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "bb705029-6500-4112-a6bd-8c0c6193ed0b", "fields": {"name": "Date", "type": "inspection", "mapped_mpl_field": "date_captured", "created_at": "2023-06-08T01:54:29.380Z", "display_name": "Date Captured", "is_editable": true, "is_filterable": true, "data_type": "date", "options": null}}, {"model": "base.header", "pk": "bc39399c-cda9-4390-8fb6-9b64c15d9a29", "fields": {"name": "ReverseSetup", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:34.227Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "bc88fe45-f754-4332-a085-22f97b600ff4", "fields": {"name": "LocationType", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:31.798Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "be75d367-8ac8-457a-a8ff-2c0221f8cb5c", "fields": {"name": "ClientDefined19", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-05-08T02:36:17.313Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "bef2a86d-c43e-4fc4-94b4-808420302548", "fields": {"name": "ClientDefined6", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:28.863Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "c04fc37d-fa16-48dc-b1f4-a1f2cea3ec72", "fields": {"name": "ClientDefined13", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-05-08T02:36:16.619Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "c1ddbad9-0682-4a29-9abe-1a88d785efe1", "fields": {"name": "DownRimToGrade", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:30.134Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "c4a3b164-03d2-4c35-99d5-89a3aeefa543", "fields": {"name": "ClientDefined8", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:28.981Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "c68a7bd7-7447-4337-b2d8-5c58e09e4ecc", "fields": {"name": "UpRimToGrade", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:36.164Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "ced86c20-4b65-4e44-830b-36de0e0d9c1b", "fields": {"name": "ServiceMeanScore", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:34.408Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "cee6ece4-7cf2-484e-a4e7-aa70231e2599", "fields": {"name": "UpNodeCoordinate", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:36.051Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "d0ec0576-dd2f-44ce-bca7-9eca42b2c8f4", "fields": {"name": "VideoImageLocationSystem", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:36.785Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "d12d393b-97ca-4a60-82b3-0ee70234a0b0", "fields": {"name": "LocationDetails", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:31.461Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "d3613be4-b984-4e3f-97d3-dc2e6707d5e3", "fields": {"name": "LengthSurveyed", "type": "inspection", "mapped_mpl_field": "chainage", "created_at": "2023-06-08T01:54:31.237Z", "display_name": "Length", "is_editable": true, "is_filterable": true, "data_type": "number", "options": null}}, {"model": "base.header", "pk": "d38594f1-49a3-460c-b7eb-f98fa43045f7", "fields": {"name": "LateralInspStart", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:31.180Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "d9bc4b3a-7802-4401-8ce1-5b1b17e48a85", "fields": {"name": "ClientDefined5", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:28.807Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "db309101-854a-4b34-aeb1-bff23f62fc31", "fields": {"name": "InspectionStage", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:30.954Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "dc27cce9-929e-4980-891c-7e69e6e230a4", "fields": {"name": "NameOfCoder", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:32.370Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "dd77724a-d313-4dbf-84eb-ed583bddbcf3", "fields": {"name": "NoOfStructuralDefects", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:33.298Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "dd958525-95b7-4855-8e1e-728ffe0ea2c6", "fields": {"name": "FinishNodeRef", "type": "inspection", "mapped_mpl_field": "end_node", "created_at": "2023-06-08T01:54:30.602Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": "string", "options": null}}, {"model": "base.header", "pk": "e0b41c70-229c-4aa3-af46-223b031d3b07", "fields": {"name": "CodingDate", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:29.152Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "e0f4668f-7861-4e7e-9fd5-742b59fbb450", "fields": {"name": "ReviewerCertificateNumber", "type": "inspection", "mapped_mpl_field": "reviewed_by", "created_at": "2023-06-08T01:54:34.282Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": "string", "options": null}}, {"model": "base.header", "pk": "e16c52ae-56e8-43a9-bda0-f82798293016", "fields": {"name": "Time", "type": "inspection", "mapped_mpl_field": "review_timestamp", "created_at": "2023-06-08T01:54:35.603Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": "date", "options": null}}, {"model": "base.header", "pk": "e26942f7-d18e-4e19-91d7-0be761a9e334", "fields": {"name": "PipeUnitLength", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:33.717Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "e59e14f0-5bbe-4455-af91-734134102fa7", "fields": {"name": "DateCleaned", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:29.436Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "eb4c549e-5ca0-4e3b-957b-43867e410709", "fields": {"name": "LocationCode", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:31.403Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "eb52f9d9-e499-4ac6-8b4a-63988ab64f87", "fields": {"name": "ClientDefined20", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-05-08T02:36:17.426Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "eb5388dd-f2bb-4a8d-9e7a-f8ca3d4ef53c", "fields": {"name": "UseOfDrainSewer", "type": "asset", "mapped_mpl_field": "pipe_type", "created_at": "2023-06-08T01:54:36.396Z", "display_name": null, "is_editable": true, "is_filterable": false, "data_type": "options", "options": ["SS", "SW"]}}, {"model": "base.header", "pk": "f0acb5b3-1c68-4282-8f6d-956c0e8b1586", "fields": {"name": "CertificateNumber", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:28.356Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "f0d35326-2dde-49f5-a1b2-5fa36b702495", "fields": {"name": "DepthAtFinishNode", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:29.548Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "f16aa8a5-286d-4507-8e8a-b35c61fac4ca", "fields": {"name": "ConsequenceOfFailure", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:29.208Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "f5c6995b-d7bf-4346-829c-ff8e395e5315", "fields": {"name": "DepthAtUpstreamNode", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:29.662Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "f702ad48-c9d4-4fdb-9268-5f0edfd334e7", "fields": {"name": "DownNorthing", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:30.078Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "f72c6fd4-7d72-47b4-8c62-30af4e21aae9", "fields": {"name": "NoOfServiceDefects", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:33.181Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "f733f544-b660-4c2f-bdaa-6694f30d1ca1", "fields": {"name": "LocationTown", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:31.743Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "f7e2642d-09a6-48fb-a2b6-7e9c12382b4f", "fields": {"name": "AssetID", "type": "asset", "mapped_mpl_field": "asset_id", "created_at": "2023-06-08T01:54:28.221Z", "display_name": "Asset ID", "is_editable": true, "is_filterable": true, "data_type": "string", "options": null}}, {"model": "base.header", "pk": "f9879149-82cc-471d-b3a0-68eb0e83bd4a", "fields": {"name": "ClientDefined14", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-05-08T02:36:16.732Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "fb4fefec-bd51-42ac-ad90-ec5ca2011c3a", "fields": {"name": "ServiceGrade", "type": "inspection", "mapped_mpl_field": "service_condition_rating", "created_at": "2023-06-08T01:54:34.346Z", "display_name": "Service Grade", "is_editable": false, "is_filterable": true, "data_type": "number", "options": null}}, {"model": "base.header", "pk": "fb6f7061-3956-427e-8026-f8936d37454f", "fields": {"name": "DrainageArea", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:30.366Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "fbefac8e-2b38-448a-9607-73737fec849d", "fields": {"name": "IsImperial", "type": "inspection", "mapped_mpl_field": "chainage_unit", "created_at": "2023-06-08T01:54:31.011Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": "string", "options": null}}, {"model": "base.header", "pk": "fef33581-6cd1-4550-8d10-8f372f2a9491", "fields": {"name": "PhotographicStorageFormat", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:33.494Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "ff28a77e-9bdd-474c-b570-2ea998fd67c6", "fields": {"name": "DownNodeCoordinate", "type": "asset", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:30.011Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}, {"model": "base.header", "pk": "ff3f87cc-c289-4ae3-bbe2-b57ed1947c54", "fields": {"name": "LandOwner", "type": "inspection", "mapped_mpl_field": "", "created_at": "2023-06-08T01:54:31.125Z", "display_name": null, "is_editable": false, "is_filterable": false, "data_type": null, "options": null}}]