# Generated by Django 4.1.2 on 2024-03-14 04:44

from django.db import migrations, models
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Header",
            fields=[
                (
                    "uuid",
                    models.UUI<PERSON>ield(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.<PERSON>r<PERSON><PERSON>(max_length=100)),
                (
                    "type",
                    models.<PERSON>r<PERSON>ield(
                        choices=[("asset", "Asset"), ("inspection", "Inspection")],
                        max_length=11,
                    ),
                ),
                ("mapped_mpl_field", models.<PERSON><PERSON><PERSON><PERSON>(max_length=100)),
                ("created_at", models.DateTime<PERSON>ield(auto_now_add=True)),
                (
                    "display_name",
                    models.Char<PERSON>ield(blank=True, max_length=100, null=True),
                ),
                ("is_editable", models.BooleanField(default=False)),
                ("is_filterable", models.<PERSON><PERSON>an<PERSON>ield(default=False)),
                ("data_type", models.Char<PERSON><PERSON>(blank=True, max_length=100, null=True)),
                ("options", models.<PERSON><PERSON><PERSON><PERSON>(blank=True, null=True)),
            ],
            options={
                "db_table": "inspections_header",
            },
        ),
    ]
