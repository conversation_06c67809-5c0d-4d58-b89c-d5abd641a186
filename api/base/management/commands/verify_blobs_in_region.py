import csv
import urllib.parse
from collections import Counter
from pathlib import Path

from django.conf import settings
from django.core.management import BaseCommand
from tqdm import tqdm

from api.common.storage import get_platform_blob_client
from api.inspections.models import FileList, VideoFrames
from api.organisations.models import Organisations


class Command(BaseCommand):
    help = "Verify the existence of a sample of platform blob files in a region. Requires the azcopy tool."

    def add_arguments(self, parser):
        parser.add_argument(
            "type", type=str, help="Type of files to check. Either 'video' or 'frame'", choices=["video", "frame"]
        )
        parser.add_argument("baseline_region", type=str, help="")
        parser.add_argument("target_region", type=str, help="")
        parser.add_argument("--samples_per_org", type=int, default=100, help="Max number of files to check per org")
        parser.add_argument("--outfile", type=Path, help="File to write results to", default=Path("missing_files.csv"))

    def handle(self, *args, **options):
        type_of_files = options["type"]
        baseline_region = options["baseline_region"]
        target_region = options["target_region"]
        samples_per_org = options["samples_per_org"]
        outfile = options["outfile"]

        if not outfile.is_absolute():
            outfile = settings.BASE_DIR / outfile

        if type_of_files == "video":
            self.sample_video_files(baseline_region, target_region, samples_per_org, outfile)
        elif type_of_files == "frame":
            self.sample_frame_files(baseline_region, target_region, samples_per_org, outfile)

    def sample_video_files(self, baseline_region: str, target_region: str, samples_per_org: int, outfile: Path):
        orgs_qs = Organisations.objects.filter(country=target_region)
        orgs_count = orgs_qs.count()

        org_id_to_n_checked = Counter()
        org_id_to_n_missing = Counter()

        with outfile.open("w") as f:
            csv_writer = csv.DictWriter(f, fieldnames=["org_id", "org_name", "file_url", "file_id"])
            csv_writer.writeheader()
            with tqdm(orgs_qs, total=orgs_count) as org_progress:
                for org in org_progress:
                    org_progress.set_description(f"Org: {org.short_name}")
                    files_qs = (
                        FileList.objects.filter(target_org=org)
                        .exclude(url__startswith="https://")  # Non azure blob files
                        .exclude(url__isnull=True)
                        .exclude(url="")
                        .order_by("?")[:samples_per_org]  # Randomly select at most samples_per_org files
                    )
                    n_files = files_qs.count()
                    with tqdm(files_qs, total=n_files, leave=False) as file_progress:
                        for file in file_progress:
                            file_progress.set_description(f"File: {file.id}")
                            container, blob_name_encoded = file.url.split("/", 1)
                            blob_name = urllib.parse.unquote(blob_name_encoded)
                            blob = get_platform_blob_client(
                                container_name=container, blob_name=blob_name, region=target_region
                            )
                            if blob.exists():
                                org_id_to_n_checked[org.id] += 1
                            else:
                                baseline_blob = get_platform_blob_client(
                                    container_name=container, blob_name=blob_name, region=baseline_region
                                )
                                if baseline_blob.exists():
                                    csv_writer.writerow(
                                        {
                                            "org_id": org.id,
                                            "org_name": org.short_name,
                                            "file_url": file.url,
                                            "file_id": file.id,
                                        }
                                    )
                                    file_progress.write(f"Missing file: {blob_name}")
                                    org_id_to_n_checked[org.id] += 1
                                    org_id_to_n_missing[org.id] += 1
                                else:
                                    file_progress.write(f"File not found in baseline region: {blob_name}")

                    org_progress.write(
                        f"Checked {org_id_to_n_checked[org.id]} random files for {org.short_name}: {org_id_to_n_missing[org.id]} missing"
                    )

        total_checked = sum(org_id_to_n_checked.values())
        total_missing = sum(org_id_to_n_missing.values())
        self.stdout.write(f"Results written to {outfile}")
        self.stdout.write(f"{total_missing} missing files out of {total_checked} checked files")

    def sample_frame_files(self, baseline_region: str, target_region: str, samples_per_org: int, outfile: Path):
        orgs_qs = Organisations.objects.filter(country=target_region)
        orgs_count = orgs_qs.count()

        org_id_to_n_checked = Counter()
        org_id_to_n_missing = Counter()

        with outfile.open("w") as f:
            csv_writer = csv.DictWriter(f, fieldnames=["org_id", "org_name", "frame_url", "frame_id"])
            csv_writer.writeheader()
            with tqdm(orgs_qs, total=orgs_count) as org_progress:
                for org in org_progress:
                    org_progress.set_description(f"Org: {org.short_name}")
                    files_qs = (
                        VideoFrames.objects.filter(parent_video__target_org=org)
                        .exclude(image_location__startswith="https://")  # Non azure blob files
                        .exclude(image_location__isnull=True)
                        .exclude(image_location="")
                        .order_by("?")[:samples_per_org]  # Randomly select at most samples_per_org frames
                    )
                    n_files = files_qs.count()
                    with tqdm(files_qs, total=n_files, leave=False) as file_progress:
                        for frame in file_progress:
                            file_progress.set_description(f"Frame: {frame.id}")
                            container, blob_name_encoded = frame.image_location.split("/", 1)
                            blob_name = urllib.parse.unquote(blob_name_encoded)
                            blob = get_platform_blob_client(
                                container_name=container, blob_name=blob_name, region=target_region
                            )
                            if blob.exists():
                                org_id_to_n_checked[org.id] += 1
                            else:
                                baseline_blob = get_platform_blob_client(
                                    container_name=container, blob_name=blob_name, region=baseline_region
                                )
                                if baseline_blob.exists():
                                    csv_writer.writerow(
                                        {
                                            "org_id": org.id,
                                            "org_name": org.short_name,
                                            "frame_url": frame.image_location,
                                            "frame_id": frame.id,
                                        }
                                    )
                                    file_progress.write(f"Missing frame: {blob_name}")
                                    org_id_to_n_checked[org.id] += 1
                                    org_id_to_n_missing[org.id] += 1
                                else:
                                    file_progress.write(f"Frame not found in baseline region: {blob_name}")

                    org_progress.write(
                        f"Checked {org_id_to_n_checked[org.id]} random frames for {org.short_name}: {org_id_to_n_missing[org.id]} missing"
                    )
