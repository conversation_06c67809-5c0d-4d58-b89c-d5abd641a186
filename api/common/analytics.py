from functools import cache

from django.conf import settings
from vapar.clients.events import EventClient, NoopEventClient, AbstractEventClient
from vapar.config.settings import EventClientSettings


@cache
def _get_cached_event_client(name: str, use_event_analytics: bool) -> AbstractEventClient:
    if use_event_analytics:
        return EventClient(EventClientSettings(name=name))
    else:
        return NoopEventClient()


def get_event_client(name: str) -> AbstractEventClient:
    """
    Get the event client to use for the current environment.
    """
    return _get_cached_event_client(name, settings.USE_EVENTHUB_ANALYTICS)
