import re

REGEX_PATTERN = re.compile("([a-z0-9])([A-Z])")


def camel_to_snake(camel_str: str) -> str:
    """
    Converts a camel case string to snake case

    :param camel_str: the string to be converted
    """

    snake_str = REGEX_PATTERN.sub(r"\1_\2", camel_str)
    return snake_str.lower()


def convert_string_to_float(string: str) -> float:
    """
    Converts a string to a float

    :param string: the string to be converted
    """
    numbers = re.findall(r"[-+]?\d*\.\d+|\d+", string)
    return float("".join(numbers)) if numbers else None
