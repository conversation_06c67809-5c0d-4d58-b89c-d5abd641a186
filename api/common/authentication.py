from drf_spectacular.extensions import OpenApiAuthenticationExtension
from rest_framework.authentication import BaseAuthentication
from rest_framework.exceptions import AuthenticationFailed

from api.common.permissions import HasServiceApi<PERSON>ey
from api.users.models import ServiceUserApiKey


class ApiKeyAuthentication(BaseAuthentication):
    """
    Custom authentication for service users
    """

    def authenticate(self, request):
        key_str = HasServiceApiKey().get_key(request)
        if not key_str:
            return None
        try:
            user = ServiceUserApiKey.objects.get_from_key(key_str).user
            if not user.is_active or not user.is_service_user:
                raise AuthenticationFailed("Unauthorized user")
            return user, None
        except ServiceUserApiKey.DoesNotExist:
            raise AuthenticationFailed("Invalid API key")


class ApiKeyAuthenticationScheme(OpenApiAuthenticationExtension):
    target_class = "api.common.authentication.ApiKeyAuthentication"
    name = "<PERSON><PERSON><PERSON><PERSON>"

    def get_security_definition(self, auto_schema):
        return {
            "type": "api<PERSON><PERSON>",
            "in": "header",
            "name": "X-Api-Key",
        }
