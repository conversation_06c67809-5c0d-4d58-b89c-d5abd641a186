from typing import Any, Optional
import uuid


class CustomError:
    uuid: str = str(uuid.uuid4())
    title: str = ""
    message: str = ""
    severity: str = ""
    metadata: Any = None
    code: str = ""

    def __init__(self, title="", message="", severity="error", code=None, metadata=None):
        self.title = title
        self.message = message
        self.severity = severity
        self.metadata = metadata
        self.code = code

    def serialize(self):
        return {
            "uuid": self.uuid,
            "title": self.title,
            "message": self.message,
            "severity": self.severity,
            "code": self.code,
            "metadata": self.metadata,
        }


class CustomValidationError:
    uuid: str = str(uuid.uuid4())
    title: str = ""
    message: str = ""
    field_name: str = ""
    metadata: Any = None
    entity: str = ""

    def __init__(self, title, message, field_name=None, metadata=None, entity=None):
        self.title = title
        self.message = message
        self.field_name = field_name
        self.metadata = metadata
        self.entity = entity

    def serialize(self):
        return {
            "uuid": self.uuid,
            "title": self.title,
            "message": self.message,
            "field_name": self.field_name,
            "metadata": self.metadata,
            "entity": self.entity,
        }

    @classmethod
    def generate_missing(cls, header_name: str, entity: str = "inspection", custom_message: Optional[str] = None):
        message = custom_message or f"'{header_name}' value missing"
        return cls(
            title=header_name,
            message=message,
            field_name=header_name,
            entity=entity,
        ).serialize()
