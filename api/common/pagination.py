from rest_framework.pagination import PageNumberPagination


class StandardResultsSetPagination(PageNumberPagination):
    page_size = 25
    page_size_query_param = "page_size"
    max_page_size = 1000


class StatusCountPagination(StandardResultsSetPagination):
    """
    Attach a statusCounts field to the response schema, alongside the paginated results.
    """

    def get_paginated_response_schema(self, schema):
        resp_schema = super().get_paginated_response_schema(schema)
        resp_schema["properties"]["status_counts"] = {
            "type": "object",
            "additionalProperties": {"type": "integer"},
        }
        return resp_schema
