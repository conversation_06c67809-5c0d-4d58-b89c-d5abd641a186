asset_table = [
    "ObjectTable",
    "id",
    "id_flag",
    "location_photo",
    "location_sketch",
    "us_node_id",
    "us_node_id_flag",
    "ds_node_id",
    "ds_node_id_flag",
    "link_suffix",
    "link_suffix_flag",
    "plr",
    "plr_flag",
    "connection_pipe_id",
    "connection_pipe_id_flag",
    "direction",
    "direction_flag",
    "current",
    "current_flag",
    "pipe_not_mapped",
    "pipe_not_mapped_flag",
    "pipe_not_surveyed",
    "pipe_not_surveyed_flag",
    "splitsurveyref",
    "splitsurveyref_flag",
    "splitsurvey",
    "splitsurvey_flag",
    "standard",
    "standard_flag",
    "scoring_method",
    "scoring_method_flag",
    "orig_coding",
    "orig_coding_flag",
    "when_surveyed",
    "when_surveyed_flag",
    "cleaning_activity",
    "cleaning_activity_flag",
    "strategic",
    "strategic_flag",
    "vehicle_reference",
    "vehicle_reference_flag",
    "pressure_nozzle_type",
    "pressure_nozzle_type_flag",
    "client_present",
    "client_present_flag",
    "driver_name",
    "driver_name_flag",
    "pressure_unit_ref",
    "pressure_unit_ref_flag",
    "cleaning_checked",
    "cleaning_checked_flag",
    "landfill_ref",
    "landfill_ref_flag",
    "cleaning_access_finish",
    "cleaning_access_finish_flag",
    "cleaning_access_start",
    "cleaning_access_start_flag",
    "location",
    "location_flag",
    "approval_level",
    "approval_level_flag",
    "approval_status",
    "approval_status_flag",
    "flow_control",
    "flow_control_flag",
    "preclean_choice",
    "preclean_choice_flag",
    "location_details",
    "location_details_flag",
    "road_name",
    "road_name_flag",
    "place_name",
    "place_name_flag",
    "district",
    "district_flag",
    "sewer_name",
    "sewer_name_flag",
    "division",
    "division_flag",
    "catchment",
    "catchment_flag",
    "category_code",
    "category_code_flag",
    "surveyed_length",
    "surveyed_length_flag",
    "anticipated_length",
    "anticipated_length_flag",
    "total_length",
    "total_length_flag",
    "pipe_length",
    "pipe_length_flag",
    "pipe_unit_length",
    "pipe_unit_length_flag",
    "pipe_type",
    "pipe_type_flag",
    "start_manhole",
    "start_manhole_flag",
    "start_cover",
    "start_cover_flag",
    "start_depth",
    "start_depth_flag",
    "start_invert",
    "start_invert_flag",
    "long_location",
    "long_location_flag",
    "longitudinal_reference_point",
    "longitudinal_reference_point_flag",
    "rim_to_invert_U",
    "rim_to_invert_U_flag",
    "grade_to_invert_U",
    "grade_to_invert_U_flag",
    "finish_manhole",
    "finish_manhole_flag",
    "finish_cover",
    "finish_cover_flag",
    "finish_depth",
    "finish_depth_flag",
    "finish_invert",
    "finish_invert_flag",
    "rim_to_invert_D",
    "rim_to_invert_D_flag",
    "grade_to_invert_D",
    "grade_to_invert_D_flag",
    "connector_type",
    "connector_type_flag",
    "material",
    "material_flag",
    "road_material",
    "road_material_flag",
    "wall_thickness",
    "wall_thickness_flag",
    "lining_type",
    "lining_type_flag",
    "lining",
    "lining_flag",
    "coating_method",
    "coating_method_flag",
    "purpose",
    "purpose_flag",
    "shape",
    "shape_flag",
    "size_1",
    "size_1_flag",
    "access_point",
    "access_point_flag",
    "size_2",
    "size_2_flag",
    "x",
    "x_flag",
    "y",
    "y_flag",
    "elevation",
    "elevation_flag",
    "us_x",
    "us_x_flag",
    "jointing_method",
    "jointing_method_flag",
    "us_y",
    "us_y_flag",
    "us_elevation",
    "us_elevation_flag",
    "ds_x",
    "ds_x_flag",
    "lateral_node_x",
    "lateral_node_x_flag",
    "ds_y",
    "ds_y_flag",
    "lateral_node_y",
    "lateral_node_y_flag",
    "video_format",
    "video_format_flag",
    "ds_elevation",
    "ds_elevation_flag",
    "coordinate_system",
    "coordinate_system_flag",
    "vertical_datum",
    "vertical_datum_flag",
    "gps_accuracy",
    "gps_accuracy_flag",
    "hard_wired_service_grade",
    "hard_wired_service_grade_flag",
    "hard_wired_structural_grade",
    "hard_wired_structural_grade_flag",
    "likelihood_score",
    "likelihood_score_flag",
    "mean_score",
    "mean_score_flag",
    "peak_score",
    "peak_score_flag",
    "work_package",
    "work_package_flag",
    "total_score",
    "total_score_flag",
    "service_mean_score",
    "service_mean_score_flag",
    "service_peak_score",
    "service_peak_score_flag",
    "service_total_score",
    "service_total_score_flag",
    "service_override_grade",
    "service_override_grade_flag",
    "structural_override_grade",
    "structural_override_grade_flag",
    "scoring_window_size",
    "scoring_window_size_flag",
    "structural_grade_1_percent",
    "structural_grade_1_percent_flag",
    "structural_grade_2_percent",
    "structural_grade_2_percent_flag",
    "structural_grade_3_percent",
    "structural_grade_3_percent_flag",
    "structural_grade_4_percent",
    "structural_grade_4_percent_flag",
    "structural_grade_5_percent",
    "structural_grade_5_percent_flag",
    "pacp_overall_rating",
    "pacp_overall_rating_flag",
    "pacp_overall_quick_rating",
    "pacp_overall_quick_rating_flag",
    "pacp_overall_index_rating",
    "pacp_overall_index_rating_flag",
    "pacp_struct_rating",
    "pacp_struct_rating_flag",
    "pacp_struct_quick_rating",
    "pacp_struct_quick_rating_flag",
    "pacp_struct_index_rating",
    "pacp_struct_index_rating_flag",
    "pacp_oandm_rating",
    "pacp_oandm_rating_flag",
    "pacp_oandm_quick_rating",
    "pacp_oandm_quick_rating_flag",
    "pacp_oandm_index_rating",
    "pacp_oandm_index_rating_flag",
    "structural_rating",
    "structural_rating_flag",
    "CH2MHill_score",
    "CH2MHill_score_flag",
    "cleaning_pressure",
    "cleaning_pressure_flag",
    "CH2MHill_struct_score",
    "CH2MHill_struct_score_flag",
    "CH2MHill_maint_score",
    "CH2MHill_maint_score_flag",
    "CH2MHill_grade",
    "CH2MHill_grade_flag",
    "CH2MHill_struct_grade",
    "CH2MHill_struct_grade_flag",
    "CH2MHill_maint_grade",
    "CH2MHill_maint_grade_flag",
    "use",
    "use_flag",
    "preclean",
    "preclean_flag",
    "date_cleaned",
    "date_cleaned_flag",
    "gas_detected",
    "gas_detected_flag",
    "weather",
    "weather_flag",
    "pressure_value",
    "pressure_value_flag",
    "temperature",
    "temperature_flag",
    "year_laid",
    "year_laid_flag",
    "year_renewed",
    "year_renewed_flag",
    "contract_no",
    "contract_no_flag",
    "owner",
    "owner_flag",
    "customer",
    "customer_flag",
    "job_number",
    "job_number_flag",
    "survey_stage",
    "survey_stage_flag",
    "method",
    "method_flag",
    "inspection_tech_used_cctv",
    "inspection_tech_used_cctv_flag",
    "inspection_tech_used_laser",
    "inspection_tech_used_laser_flag",
    "inspection_tech_used_sonar",
    "inspection_tech_used_sonar_flag",
    "inspection_tech_used_sidewall",
    "inspection_tech_used_sidewall_flag",
    "inspection_tech_used_zoom",
    "inspection_tech_used_zoom_flag",
    "inspection_tech_used_other",
    "inspection_tech_used_other_flag",
    "inspection_status",
    "inspection_status_flag",
    "consequence_of_failure",
    "consequence_of_failure_flag",
    "surveyed_by",
    "surveyed_by_flag",
    "video_recorder",
    "video_recorder_flag",
    "video_image_loc",
    "video_image_loc_flag",
    "video_file_in",
    "video_file_in_flag",
    "vt_no",
    "vt_no_flag",
    "photo_storage",
    "photo_storage_flag",
    "photo_reference",
    "photo_reference_flag",
    "index",
    "index_flag",
    "actual_cost",
    "actual_cost_flag",
    "comments",
    "comments_flag",
    "estimated_cost",
    "estimated_cost_flag",
    "reverse_setup",
    "reverse_setup_flag",
    "actual_duration",
    "actual_duration_flag",
    "sheet_number",
    "sheet_number_flag",
    "estimated_duration",
    "estimated_duration_flag",
    "priority",
    "priority_flag",
    "schedule_number",
    "schedule_number_flag",
    "uid",
    "date_planned",
    "date_planned_flag",
    "camera_operator",
    "camera_operator_flag",
    "certificate_number",
    "certificate_number_flag",
    "reviewed_by",
    "reviewed_by_flag",
    "reviewer_certificate_number",
    "reviewer_certificate_number_flag",
    "start_asset_type",
    "start_asset_type_flag",
    "finish_asset_type",
    "finish_asset_type_flag",
    "parallel_line",
    "parallel_line_flag",
    "pipe_status",
    "pipe_status_flag",
    "video_start_time",
    "video_start_time_flag",
    "video_end_time",
    "video_end_time_flag",
    "repeat_period",
    "repeat_period_flag",
    "team_leader",
    "team_leader_flag",
    "contractor",
    "contractor_flag",
    "completed",
    "completed_flag",
    "date_completed",
    "date_completed_flag",
    "closed",
    "closed_flag",
    "date_closed",
    "date_closed_flag",
    "project",
    "project_flag",
    "date_started",
    "date_started_flag",
    "date_opened",
    "date_opened_flag",
    "task_status",
    "task_status_flag",
    "task_phase",
    "task_phase_flag",
    "estimated_completion_date",
    "estimated_completion_date_flag",
    "repeat_schedule_number",
    "repeat_schedule_number_flag",
    "location_photo_ref",
    "location_photo_ref_flag",
    "location_photo_filename",
    "location_photo_filename_flag",
    "lateral_clock_loc",
    "lateral_clock_loc_flag",
    "lateral_node_ref",
    "lateral_node_ref_flag",
    "lateral_start_point",
    "lateral_start_point_flag",
    "location_sketch_ref",
    "location_sketch_ref_flag",
    "location_sketch_filename",
    "location_sketch_filename_flag",
    "location_risk_factor",
    "location_risk_factor_flag",
    "tidal_influence",
    "tidal_influence_flag",
    "point_array",
    "parent_cctv_survey_id",
    "parent_cctv_survey_id_flag",
    "lateral_segment_reference",
    "lateral_segment_reference_flag",
    "property_line",
    "property_line_flag",
    "clean_out",
    "clean_out_flag",
    "clean_out_rim_invert",
    "clean_out_rim_invert_flag",
    "tap_location",
    "tap_location_flag",
    "building_address",
    "building_address_flag",
    "barcode",
    "barcode_flag",
    "user_text_1",
    "user_text_1_flag",
    "user_text_2",
    "user_text_2_flag",
    "user_text_3",
    "user_text_3_flag",
    "user_text_4",
    "user_text_4_flag",
    "user_text_5",
    "user_text_5_flag",
    "user_text_6",
    "user_text_6_flag",
    "user_text_7",
    "user_text_7_flag",
    "user_text_8",
    "user_text_8_flag",
    "user_text_9",
    "user_text_9_flag",
    "user_text_10",
    "user_text_10_flag",
    "user_text_11",
    "user_text_11_flag",
    "user_text_12",
    "user_text_12_flag",
    "user_text_13",
    "user_text_13_flag",
    "user_text_14",
    "user_text_14_flag",
    "user_text_15",
    "user_text_15_flag",
    "user_text_16",
    "user_text_16_flag",
    "user_text_17",
    "user_text_17_flag",
    "user_text_18",
    "user_text_18_flag",
    "user_text_19",
    "user_text_19_flag",
    "user_text_20",
    "user_text_20_flag",
    "user_text_21",
    "user_text_21_flag",
    "user_text_22",
    "user_text_22_flag",
    "user_text_23",
    "user_text_23_flag",
    "user_text_24",
    "user_text_24_flag",
    "user_text_25",
    "user_text_25_flag",
    "user_text_26",
    "user_text_26_flag",
    "user_text_27",
    "user_text_27_flag",
    "user_text_28",
    "user_text_28_flag",
    "user_text_29",
    "user_text_29_flag",
    "user_text_30",
    "user_text_30_flag",
    "user_text_31",
    "user_text_31_flag",
    "user_text_32",
    "user_text_32_flag",
    "user_text_33",
    "user_text_33_flag",
    "user_text_34",
    "user_text_34_flag",
    "user_text_35",
    "user_text_35_flag",
    "user_text_36",
    "user_text_36_flag",
    "user_text_37",
    "user_text_37_flag",
    "user_text_38",
    "user_text_38_flag",
    "user_text_39",
    "user_text_39_flag",
    "user_text_40",
    "user_text_40_flag",
    "user_number_1",
    "user_number_1_flag",
    "user_number_2",
    "user_number_2_flag",
    "user_number_3",
    "user_number_3_flag",
    "user_number_4",
    "user_number_4_flag",
    "user_number_5",
    "user_number_5_flag",
    "user_number_6",
    "user_number_6_flag",
    "user_number_7",
    "user_number_7_flag",
    "user_number_8",
    "user_number_8_flag",
    "user_number_9",
    "user_number_9_flag",
    "user_number_10",
    "user_number_10_flag",
    "user_number_11",
    "user_number_11_flag",
    "user_number_12",
    "user_number_12_flag",
    "user_number_13",
    "user_number_13_flag",
    "user_number_14",
    "user_number_14_flag",
    "user_number_15",
    "user_number_15_flag",
    "user_number_16",
    "user_number_16_flag",
    "user_number_17",
    "user_number_17_flag",
    "user_number_18",
    "user_number_18_flag",
    "user_number_19",
    "user_number_19_flag",
    "user_number_20",
    "user_number_20_flag",
    "user_number_21",
    "user_number_21_flag",
    "user_number_22",
    "user_number_22_flag",
    "user_number_23",
    "user_number_23_flag",
    "user_number_24",
    "user_number_24_flag",
    "user_number_25",
    "user_number_25_flag",
    "user_number_26",
    "user_number_26_flag",
    "user_number_27",
    "user_number_27_flag",
    "user_number_28",
    "user_number_28_flag",
    "user_number_29",
    "user_number_29_flag",
    "user_number_30",
    "user_number_30_flag",
    "user_number_31",
    "user_number_31_flag",
    "user_number_32",
    "user_number_32_flag",
    "user_number_33",
    "user_number_33_flag",
    "user_number_34",
    "user_number_34_flag",
    "user_number_35",
    "user_number_35_flag",
    "user_number_36",
    "user_number_36_flag",
    "user_number_37",
    "user_number_37_flag",
    "user_number_38",
    "user_number_38_flag",
    "user_number_39",
    "user_number_39_flag",
    "user_number_40",
    "user_number_40_flag",
    "user_date_1",
    "user_date_1_flag",
    "user_date_2",
    "user_date_2_flag",
    "user_date_3",
    "user_date_3_flag",
    "user_date_4",
    "user_date_4_flag",
    "user_date_5",
    "user_date_5_flag",
    "notes",
    "notes_flag",
    "approval_history",
    "special_instructions",
    "special_instructions_flag",
    "mobile_uid",
]

defect_table = [
    "ObjectTable",
    "id",
    "video_no",
    "photo_no",
    "distance",
    "cd",
    "code",
    "video_file",
    "diameter",
    "Joint",
    "clock_at",
    "clock_to",
    "percentage",
    "intrusion",
    "remarks",
    "characterisation1",
    "characterisation2",
    "detail_image",
    "video_no2",
    "service_score",
    "structural_score",
    "characterisation3",
]

material_dict = {
    "ac": "AC",
    "aconcrete": "AC",
    "asbestoscement": "AC",
    "brick": "BR",
    "brickwork": "BR",
    "butifriflekelay": "VC",
    "castiron": "CI",
    "castirontypeunknown": "CI",
    "cc": "CO",
    "chestoscement": "AC",
    "clay": "VC",
    "clayvitrified": "VC",
    "co": "CO",
    "combined": "X",
    "con": "CO",
    "conc": "CO",
    "concete": "CO",
    "concreat": "CO",
    "concreate": "CO",
    "concreete": "CO",
    "concrete": "CO",
    "concretee": "CO",
    "concretepipe": "CO",
    "concretepreca": "CO",
    "concretereinforced": "RC",
    "concretesegments": "CO",
    "concreteunreinforced": "CO",
    "concretevc": "CO",
    "concrte": "CO",
    "cong": "CO",
    "curedinplacepipe": "X",
    "detailmissing": "Z",
    "earthenware": "VC",
    "eartherware": "VC",
    "epoxy": "EP",
    "fibrecement": "FC",
    "fibrereinforcedcement": "FC",
    "fiedclay": "VC",
    "fifiedclay": "VC",
    "gg": "X",
    "go": "X",
    "grilledclay": "VC",
    "hape": "PE",
    "hdpe": "PE",
    "hope": "PE",
    "icc": "X",
    "ifiedclay": "VC",
    "iriblog": "XP",
    "ivc": "X",
    "lined": "X",
    "llined": "X",
    "masonryuncoursedorrough": "MAR",
    "mildsteelcementlined": "CL",
    "": "Z",
    "opvc": "PVC",
    "other": "Z",
    "p": "XP",
    "pitc": "PF",
    "pitchfibre": "PF",
    "plastic": "XP",
    "poinylchlor": "PVC",
    "polyethylene": "PE",
    "polypropylene": "PP",
    "polyvinylchlor": "PVC",
    "polyvinylchlorid": "PVC",
    "polyvinylchloride": "PVC",
    "pvc": "PVC",
    "pvcelasticlsed": "PVC",
    "pvcexliner": "PVC",
    "pvcplasticised": "PVC",
    "pvcplatisicsed": "PVC",
    "pvcunplasticised": "PVC",
    "pyc": "PVC",
    "rc": "RC",
    "reinforceconcrete": "RC",
    "reinforcedconcrete": "RC",
    "reinforcedconcretepipe": "RC",
    "reinforcesd": "RC",
    "renforcedcement": "RC",
    "ribloc": "XP",
    "riblock": "XP",
    "riblocpvc": "XP",
    "riblocvc": "XP",
    "riblog": "XP",
    "riblogpvc": "XP",
    "sbestoscement": "AC",
    "sitriffedclay": "VC",
    "spiralwoundliner": "XP",
    "steel": "ST",
    "strifiedclay": "VC",
    "toifiedclay": "VC",
    "trifiedclay": "VC",
    "unidentifiedtypeofironorsteel": "XI",
    "unidentifiedtypeofplastics": "XP",
    "unidentifiedtypeorironorsteel": "XI",
    "unknown": "X",
    "upvc": "PVC",
    "upvcsplu": "PVC",
    "vc": "VC",
    "vcp": "VC",
    "vg": "VC",
    "vic": "VC",
    "viscalledclay": "VC",
    "vitrifiedclay": "VC",
    "vitrifiedclayiceallclayware": "VC",
    "vitrifiedclayieallclayware": "VC",
    "vitrifiedclayiveallclayware": "VC",
    "vitrifiedclaypipe": "VC",
    "vitrifiedclaypipeea": "VC",
    "vitrifiedclaypipeecircular": "VC",
    "vitrifiedclaypipeiea": "VC",
    "vitrifiedclaypipeieallclayware": "VC",
    "vitrifiedclaypipeiveallclay": "VC",
    "vitrifiedclays": "VC",
    "vitrifiedelay": "VC",
    "vitrifiedlay": "VC",
    "vitrifiericlay": "VC",
    "vitriifiedclay": "VC",
    "zzz": "Z",
}
