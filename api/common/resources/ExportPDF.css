body {
    font-size: 8px;
}

@page {
    size: A4;
    margin: 10mm;
}

#vaparlogo {
    width: 150px;
}

#orglogo {
    width: auto;
    height: auto;
    max-width: 150px;
    max-height: 60px;
    float: right
}

table {
    border-collapse: collapse;
    width: 100%;
}

table,
tr,
td,
th {
    border: 1px solid black;
}

#details1 td {
    width: 14.29%;
    text-align: center;
}

#details2 {
    margin-top: 8px;
}

#details2 td {
    position: relative;
    width: 33.33%;
}

#details2 span {
    position: absolute;
    left: 65px;
}

#details3 td {
    position: relative;
    width: 50%;
}

#details3 span {
    position: absolute;
    left: 110px;
}

#pictureCell>div {
    position: relative;
    height: 620px;
}

#startNode,
#endNode {
    position: absolute;
    border: 1px solid black;
    width: 30px;
    height: 30px;
    left: 30px;
    background-color: grey;
    border-radius: 50%;
}

#startNode {
    top: 20px;
}

#endNode {
    top: 560px;
}

#abandonedNode {
    top: 560px;
    position: absolute;
    left: 30px;
    width: 25px;
    height: 5px;
    transform: rotate(20deg);
    border-top: 3px solid red;
    border-bottom: 3px solid red;
}

#midNode {
    position: absolute;
    border: 1px solid black;
    width: 10px;
    height: 508px;
    left: 40px;
    top: 51px;
}

.horiz {
    position: absolute;
    left: 40px;
    width: 12px;
    border-top: 1px solid black;
}

.obliq {
    position: absolute;
    left: 51px;
    width: 50px;
    border-top: 1px solid black;
}

.underlin {
    position: absolute;
    left: 100px;
    width: 23px;
    border-top: 1px solid black;
}

.frameString {
    position: absolute;
    left: 100px;
}

.inner {
    width: 33%;
    float: right;
    text-align: center;
}

.frameSpan {
    position: absolute;
    left: 120px;
    white-space: nowrap;
}

.flow_no_dir {
    display: none;
}

.flow_up {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
}

.flow_down {
    position: absolute;
    top: 50%;
    transform: translateY(-50%) scaleY(-1);
}

.frame_image {
    width: auto;
    height: auto;
    max-width: 280px;
    max-height: 280px;
}

.frame_cell0 {
    position: absolute;
    left: 25px;
    top: 25px;
}

.frame_cell1 {
    position: absolute;
    left: 325px;
    top: 25px;
}

.frame_cell2 {
    position: absolute;
    left: 25px;
    top: 325px;
}

.frame_cell3 {
    position: absolute;
    left: 325px;
    top: 325px;
}

.column {
    float: left;
    width: 25%;
}

/* Clear floats after the columns */
.row:after {
    content: "";
    display: table;
    clear: both;
}