from api.organisations.models import AssetOwners


def get_linked_orgs(organisation):
    linked_orgs = []
    if organisation.is_contractor:
        asset_owners = AssetOwners.contractor.through.objects.filter(contractors__org=organisation)
        for asset_owner in asset_owners:
            linked_orgs.append(asset_owner.assetowners.org)
    elif organisation.is_asset_owner:
        contractors = AssetOwners.contractor.through.objects.filter(assetowners__org=organisation)
        for contractor in contractors:
            linked_orgs.append(contractor.contractors.org)
    return linked_orgs
