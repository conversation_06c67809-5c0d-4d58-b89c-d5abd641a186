from django.core.mail import EmailMultiAlternatives
from django.utils.encoding import force_bytes
from django.utils.http import urlsafe_base64_encode
from django.template.loader import render_to_string
from django.conf import settings
from yarl import URL

from api.users.token_generator import account_activation_token, reset_password_token  # type: ignore


def send_vapar_email(email_details):
    subject = email_details.get("subject", None)
    text_content = email_details.get("text_content", None)
    from_email = email_details.get("from_email", None)
    to = email_details.get("to", None)
    html_content = email_details.get("html_content", None)

    msg = EmailMultiAlternatives(subject, text_content, from_email, to)
    msg.attach_alternative(html_content, "text/html")
    msg.send()


def send_activation_email(request, user):
    uid = urlsafe_base64_encode(force_bytes(user["id"]))
    token = account_activation_token.make_token(user)
    base_activation_url = URL(settings.VAPAR_ACTIVATION_URL)
    activation_url_obj = (base_activation_url / uid / token).update_query(base_activation_url.query)
    activation_url = str(activation_url_obj)
    template_file_name = "activation_email.html"

    html_content = render_to_string(
        template_file_name, {"activation_url": activation_url, "logo_url": settings.LOGO_URL}
    )

    build_email = {
        "subject": "Welcome to VAPAR Solutions - verify your account",
        "from_email": "<<EMAIL>>",
        "to": [user["email"]],
        "text_content": f"You're receiving this email because you need to finish the activation process on VAPAR Solutions. Please go to the following page to activate account: {activation_url}",
        "html_content": html_content,
    }

    send_vapar_email(build_email)


def send_reset_password_email(request, user):
    uid = urlsafe_base64_encode(force_bytes(user["id"]))
    token = reset_password_token.make_token(user)
    base_reset_url = URL(settings.VAPAR_RESET_URL)
    # Note that the trailing slash is necessary for the request to be routed correctly
    reset_password_url_obj = (base_reset_url / uid / token / "").update_query(base_reset_url.query)
    reset_password_link = str(reset_password_url_obj)

    build_email = {
        "subject": "Password reset on VAPAR Solutions",
        "from_email": "<<EMAIL>>",
        "to": [user["email"]],
        "text_content": f"You're receiving this email because you requested a password reset for your user account at VAPAR Solutions. Please go to the following page and choose a new password: {reset_password_link}",
        "html_content": (
            f"<p>You're receiving this email because you requested a password reset for your user account at VAPAR Solutions.</p>"
            f"<p>Please go to the following page and choose a new password: <a href='{reset_password_link}'>Reset Password</a></p>"
            f"<p>Your username, in case you've forgotten: {user['email']}</p>"
            f"<p>Thanks for using our site.</p>"
            f"<p>The VAPAR Solutions team</p>"
        ),
    }

    send_vapar_email(build_email)
