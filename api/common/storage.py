from datetime import datetime, timedelta, timezone
from typing import Optional
from urllib.parse import urljoin

from azure.storage.blob import (
    BlobClient,
    ContainerSasPermissions,
    generate_container_sas,
    ContainerClient,
)
from django.conf import settings


def _get_processing_conn_str(region: str = "default") -> str:
    return settings.PROCESSING_STORAGE_ACCOUNT_REGION_CONN_STRS.get(
        region, settings.PROCESSING_STORAGE_ACCOUNT_REGION_CONN_STRS["default"]
    )


def get_platform_conn_str(region: str = "default") -> str:
    """
    Get the connection string for the platform storage account in a given region. Returns the default  region if there
    isn't one set for the given region.
    """
    return settings.PLATFORM_STORAGE_ACCOUNT_REGION_CONN_STRS.get(
        region, settings.PLATFORM_STORAGE_ACCOUNT_REGION_CONN_STRS["default"]
    )


def get_processing_storage_region_base_url(region: str = "default") -> str:
    """
    Get the base url (without container name) for the storage account used for processing in a given region.
    Returns the default region if there isn't one set for the given region.
    """

    return settings.PROCESSING_BLOB_STORAGE_REGION_URLS.get(
        region, settings.PROCESSING_BLOB_STORAGE_REGION_URLS["default"]
    )


def get_platform_storage_region_base_url(region: str = "default") -> str:
    """Get the base url (without container name) for the storage account used for processing in a given region."""

    return settings.PLATFORM_BLOB_STORAGE_REGION_URLS.get(region, settings.PLATFORM_BLOB_STORAGE_REGION_URLS["default"])


def should_use_processing_storage(container_name: str, write_access: bool) -> bool:
    """
    Return whether a given container should be accessed in the processing storage account.
    This is for any cases where we may have to access either account using the same interface.
    """

    return container_name == settings.BLOB_STORAGE_VIDEOS_CONTAINER and write_access


def _get_platform_container_client(container_name: str, region: str) -> ContainerClient:
    container_client = ContainerClient.from_connection_string(
        conn_str=get_platform_conn_str(region),
        container_name=container_name,
    )
    return container_client


def _get_container_sas_token(container: ContainerClient, write_access: bool = False, download: bool = False) -> str:
    """
    Generate a SAS token for a given container.
    """

    permission = ContainerSasPermissions(read=True, write=write_access)
    sas_token = generate_container_sas(
        container.account_name,
        container.container_name,
        account_key=container.credential.account_key,
        permission=permission,
        protocol="https",
        expiry=datetime.now(tz=timezone.utc) + timedelta(days=7),
        start=datetime.now(tz=timezone.utc) - timedelta(minutes=1),
        content_type="application/octet-stream" if download else None,
    )
    return sas_token


def get_platform_storage_sas_token(
    container_name: str, region: str = "default", write_access: bool = False, download: bool = False
) -> str:
    """
    Generate a SAS token for a given container in the platform storage account.
    """
    container_client = _get_platform_container_client(container_name=container_name, region=region)
    sas_token = _get_container_sas_token(container_client, write_access, download)
    return sas_token


def get_processing_storage_sas_token(container_name: str, region: str = "default", write_access: bool = False) -> str:
    """
    Generate a SAS token for a given container in the processing storage account.
    """

    container_client = ContainerClient.from_connection_string(
        conn_str=_get_processing_conn_str(region),
        container_name=container_name,
    )
    sas_token = _get_container_sas_token(container_client, write_access)
    return sas_token


def get_processing_blob_url_with_sas(
    blob_path: str,
    region: str = "default",
    write_access: bool = False,
    sas_token: Optional[str] = None,
) -> str:
    """
    Given a "container_name/blob_name" path, return a full URL with a SAS token for that blob.
    """

    container_name = blob_path.split("/", 1)[0]
    if sas_token is None:
        sas_token = get_processing_storage_sas_token(container_name, region, write_access)
    base_url = get_processing_storage_region_base_url(region)
    url = urljoin(base_url, blob_path)
    return f"{url}?{sas_token}"


def get_platform_blob_url_with_sas(
    blob_path: str, region: str = "default", write_access: bool = False, sas_token: str | None = None
) -> str:
    """
    Given a "container_name/blob_name" path, return a full URL with a SAS token for that blob.

    :param blob_path: The path to the blob in the format "container_name/blob_name".
    :param region: Which regional storage account to use.
    :param write_access: Whether to generate a SAS token with write permissions.
    :param sas_token: An optional SAS token to use instead of generating a new one.

    :return: The full URL to the blob with the SAS token appended.
    """

    container_name = blob_path.split("/", 1)[0]
    if sas_token is None:
        sas_token = get_platform_storage_sas_token(container_name, region=region, write_access=write_access)
    # if write_access:
    #     url = urljoin(get_platform_storage_region_base_url(region), blob_path)
    # else:
    #     url = urljoin(settings.CDN_DOMAIN, blob_path)
    # NOTE: We'll have to give up CDN until we can source from two separate storage accounts
    url = urljoin(get_platform_storage_region_base_url(region), blob_path)
    return f"{url}?{sas_token}"


def put_to_platform_blob_storage(
    file,
    blob_name: str,
    container_name: str,
    overwrite: bool = False,
    region: str = "default",
    metadata: dict[str, str] | None = None,
):
    """
    Upload a file to platform blob storage at the given container and blob name.

    :param file: The readable file object to upload.
    :param blob_name: The name of the blob to create or overwrite.
    :param container_name: The name of the container to upload to.
    :param overwrite: Whether to overwrite the blob if it already exists.
    :param region: Which regional storage account to use.
    :param metadata: Optional metadata key-value pairs to attach to the blob.
    """

    container_client = _get_platform_container_client(container_name=container_name, region=region)
    container_client.upload_blob(name=blob_name, data=file, overwrite=overwrite, metadata=metadata)


def get_platform_blob_client(blob_name: str, container_name: str, region: str = "default") -> BlobClient:
    """
    Get a BlobClient for a given blob name + container name in the platform storage account.
    """

    return BlobClient.from_connection_string(
        conn_str=get_platform_conn_str(region),
        container_name=container_name,
        blob_name=blob_name,
    )


def get_processing_blob_client(blob_name: str, container_name: str, region: str = "default") -> BlobClient:
    return BlobClient.from_connection_string(
        conn_str=_get_processing_conn_str(region),
        container_name=container_name,
        blob_name=blob_name,
    )


def delete_blob_storage_file(blob_name: str, container_name: str, region: str = "default"):
    container_client = _get_platform_container_client(container_name=container_name, region=region)
    container_client.delete_blob(blob=blob_name)
