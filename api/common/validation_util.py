def is_float(num):
    try:
        float(num)
        return True
    except ValueError:
        return False


def is_boolean(string):
    return string.lower() in ["true", "false", "1", "0"]


def is_string(str):
    # TODO logic to be decided
    return True


def is_option(value: str, options: str | list[str] | None):
    if options is None:
        return False
    if isinstance(options, str):
        options = options.replace(", ", ",").split(",")

    return value in options
