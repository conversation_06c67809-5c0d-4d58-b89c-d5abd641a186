from enum import Enum
from typing import Any

from django.db.models import Case, When, F, Value, Q, <PERSON>loatField, BooleanField, Field, CharField
from django.db.models.functions import Cast


class UnitEnum(str, Enum):
    METRE = "m"
    FT = "ft"


class StatusEnum(str, Enum):
    PLANNED = "Planned"
    UPLOADED = "Uploaded"
    REVIEWED = "Reviewed"
    DECISION_MADE = "Decision"
    REPAIR_PLAN = "Repair Plan"
    ACTIONED = "Actioned"
    COMPLETE = "Complete"
    ARCHIVED = "Archived"

    @classmethod
    def get_status_list(cls):
        return [status.value for status in cls]


class PipeTypeEnum(str, Enum):
    SEWER = "SS"
    STORMWATER = "SW"


class OperatorEnum(str, Enum):
    CT = "contains"
    SW = "starts with"
    IN = "comma separated list"
    GT = "greater than"
    LT = "less than"
    GE = "greater than or equal to"
    LE = "less than or equal to"
    EQ = "equals"

    def as_lookup_suffix(self):
        match self:
            case OperatorEnum.CT:
                return "__icontains"
            case OperatorEnum.SW:
                return "__startswith"
            case OperatorEnum.IN:
                return "__in"
            case OperatorEnum.GT:
                return "__gt"
            case OperatorEnum.GE:
                return "__gte"
            case OperatorEnum.LT:
                return "__lt"
            case OperatorEnum.LE:
                return "__lte"
            case _:  # EQ
                return ""


class QueryFilterDataTypeEnum(str, Enum):
    """
    The data type of the field to be filtered on
    """

    STRING = "string"
    NUMBER = "number"
    BOOLEAN = "boolean"
    DATE = "date"
    OPTIONS = "options"

    def build_type_cast_expression(self, field_name: str, default: Any | None = None) -> Case | F:
        """
        Because values can be arbitrary strings, we need to cast them to the correct type for comparison
        """

        if self == QueryFilterDataTypeEnum.NUMBER:  # Note, only works if the source field is a string
            return Case(
                When(
                    Q(**{f"{field_name}__regex": r"^\-?\d+(\.\d+)?$"}),
                    then=Cast(F(field_name), FloatField()),
                ),
                default=Value(default),
                output_field=FloatField(),
            )
        else:
            return F(field_name)  # Leave as original type

    def as_db_field_type(self) -> Field:
        match self:
            case QueryFilterDataTypeEnum.STRING:
                return CharField()
            case QueryFilterDataTypeEnum.NUMBER:
                return FloatField()
            case QueryFilterDataTypeEnum.BOOLEAN:
                return BooleanField()
            case QueryFilterDataTypeEnum.DATE:
                return CharField()
            case QueryFilterDataTypeEnum.OPTIONS:
                return CharField()

    def get_converted_value_list(
        self, values: list[str], operator: OperatorEnum
    ) -> str | float | list[str | float | None] | None:
        """
        Convert the list of target values for a filter to the correct type for comparison
        """

        if operator == OperatorEnum.IN:
            return [self._get_converted_value(v) for v in values]
        else:
            return self._get_converted_value(values[0])

    def _get_converted_value(self, value: str) -> str | float | None:
        try:
            if self == QueryFilterDataTypeEnum.NUMBER:
                return float(value)
            else:
                return value
        except ValueError:
            return None


class UserLevelEnum(str, Enum):
    STANDARD = "standard"
    UPLOAD_ONLY = "upload only"


class OrganisationTypeEnum(str, Enum):
    ASSET_OWNER = "Asset_Owner"
    CONTRACTOR = "Contractor"


class OrgDisplayGroupEnum(int, Enum):
    MY_ORGANISATION = 1
    ASSET_OWNERS = 2
    CONTRACTORS = 3

    @classmethod
    def _missing_(cls):
        return len(cls) + 1

    def __str__(self):
        return self.name.replace("_", " ").title()


class ProcessingRetryableEnum(str, Enum):
    AVAILABLE = "Available"
    MAX_RETRIES_EXCEEDED = "Max Retries Exceeded"
    BAD_STATE = "Bad State"


class RepairPlanActorEnum(str, Enum):
    VAPAR = "vapar"
    CONTRACTOR = "contractor"
    OWNER = "owner"

    @classmethod
    def as_choices(cls) -> list[tuple[str, str]]:
        return [(party.value, party.name) for party in cls]


class RepairPlanItemTypeEnum(str, Enum):
    PATCH = "Patch"
    CLEANING = "Cleaning"
    LINING = "Lining"
    ROOT_REMOVAL = "Root Removal"
    DIG_UP = "Dig Up"
    NO_ACTION = "No Action"
    CUSTOM = "Custom"
    OTHER = "Other"

    @classmethod
    def as_choices(cls) -> list[tuple[str, str]]:
        return [(t.value, t.name) for t in cls]


class CustomRepairItemDataTypeEnum(str, Enum):
    TEXT = "text"
    NUMBER = "number"
    BOOLEAN = "boolean"

    @classmethod
    def as_choices(cls) -> list[tuple[str, str]]:
        return [(t.value, t.name) for t in cls]

    def as_type(self, value: str) -> str | float | bool:
        if self == CustomRepairItemDataTypeEnum.NUMBER:
            return float(value)
        elif self == CustomRepairItemDataTypeEnum.BOOLEAN:
            if value in ["true", "false"]:
                return value == "true"
            raise ValueError
        else:
            return value

    def to_str_value(self, value: str | float | bool) -> str:
        if self == CustomRepairItemDataTypeEnum.BOOLEAN:
            return "true" if value else "false"
        return str(value)

    def as_field_name(self) -> str:
        if self == CustomRepairItemDataTypeEnum.NUMBER:
            return "value_number"
        elif self == CustomRepairItemDataTypeEnum.BOOLEAN:
            return "value_bool"
        else:
            return "value_text"
