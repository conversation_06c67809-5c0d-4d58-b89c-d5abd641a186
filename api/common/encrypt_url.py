from django.conf import settings
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives import hashes
from cryptography.fernet import Fernet
import base64

from yarl import URL


def getmyshacode(num):
    import hashlib

    sentence = str(num)
    result = hashlib.sha256(sentence.encode())
    return result.hexdigest()


def encrypt(frame_pk, organization_pk):
    password = settings.IMAGE_SECRET_KEY
    salt = settings.SALT
    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,
        salt=salt,
        iterations=100000,
    )
    key = base64.urlsafe_b64encode(kdf.derive(password))
    f = Fernet(key)
    msg = str(frame_pk) + ":" + str(organization_pk)
    msg = bytes(msg, encoding="utf-8")
    token = f.encrypt(msg)
    token = token.decode("utf-8")
    return token


def build_encrypted_url(base_url: str, encrypted_component: str) -> str:
    """
    Build the full url with an encrypted component from a base url (that may contain query params)
    """
    base = URL(base_url)
    encrypted_fragment_query_params = URL(encrypted_component).query
    full_url = (base / encrypted_component).update_query(base.query).update_query(encrypted_fragment_query_params)
    return str(full_url)
