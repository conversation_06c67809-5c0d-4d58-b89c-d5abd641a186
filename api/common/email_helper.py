from django.core.mail import EmailMultiAlternatives

from api.common.storage import get_platform_storage_region_base_url


def send_email_to_client(subject, text, container, filename, sas_read_token, user_email):
    from_email = "<EMAIL>"
    to = [user_email]
    text_content = (
        "Your "
        + text
        + " is ready. Please be noted that the link is only valid for an hour from the time you receive this email. Please click the link in below."
    )
    link_url = get_platform_storage_region_base_url() + container + "/" + filename + "?" + sas_read_token
    html_content = (
        "<p>Your export file is ready. <strong>Please note that this link is only valid for an hour from the time you receive this email.</strong> Please click the link below.</p>"
        '<a href="' + link_url + '" target="_blank" >Download Link</a><br>'
        "<p>Thanks for using our service!</p>"
        "<strong>VAPAR</strong>"
    )
    msg = EmailMultiAlternatives(subject, text_content, from_email, to=to)
    msg.attach_alternative(html_content, "text/html")
    msg.send()
