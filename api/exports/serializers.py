from django.utils import timezone
from pydantic import BaseModel, ValidationError
from rest_framework import serializers, exceptions
from rest_framework.generics import get_object_or_404

from api.exports.models import Export, AnyExportPayload, ExportOutput
from api.common.storage import get_platform_blob_url_with_sas

from vapar.constants.exports import ExportStatus


class ExportPayloadEnvelope(BaseModel):
    """Wrapper type used as the request body for the export create endpoint."""

    payload: AnyExportPayload


class ExportSerializer(serializers.ModelSerializer):
    file_display_names = serializers.SerializerMethodField(read_only=True)

    def get_file_display_names(self, obj: Export) -> list[str | None]:
        return [output.file_display_name for output in obj.outputs.all()]

    class Meta:
        model = Export
        fields = [
            "id",
            "target_org",
            "type",
            "format",
            "status",
            "status_reason",
            "created_at",
            "updated_at",
            "completed_at",
            "created_by",
            "is_hidden",
            "is_initiated_by_user",
            "file_display_names",
        ]

    def validate(self, attrs):
        try:
            return ExportPayloadEnvelope.model_validate(attrs).model_dump(mode="json", by_alias=True)
        except ValidationError as e:
            raise exceptions.ValidationError({"errors": e.errors()})

    def to_internal_value(self, data):
        return self.validate(data)

    def create(self, validated_data: dict):
        is_initiated_by_user = self.context.get("is_initiated_by_user", True)

        parsed_payload = ExportPayloadEnvelope.model_validate(validated_data).payload.root
        export_type = parsed_payload.type
        export_format = parsed_payload.format
        return Export.objects.create(
            type=export_type,
            format=export_format,
            target_org=self.context["request"].organisation,
            created_by=self.context["request"].user,
            payload=validated_data["payload"],
            is_initiated_by_user=is_initiated_by_user,
        )


class ExportUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Export
        fields = [
            "id",
            "target_org",
            "type",
            "format",
            "status",
            "status_reason",
            "created_at",
            "updated_at",
            "completed_at",
            "created_by",
            "is_hidden",
        ]
        read_only_fields = [
            "id",
            "target_org",
            "type",
            "format",
            "created_at",
            "updated_at",
            "completed_at",
            "created_by",
        ]

    def validate(self, data):
        super().validate(data)
        export_id = self.context["view"].kwargs.get("uuid")
        obj = get_object_or_404(Export, id=export_id, is_hidden=False)
        if data.get("status_reason") and data.get("status"):
            if data["status"] != ExportStatus.FAILED:
                raise exceptions.ValidationError("status_reason may only be given if given status is failed")
        elif data.get("status_reason") and obj.status != ExportStatus.FAILED:
            raise exceptions.ValidationError("status_reason may only be given if existing status is failed")
        elif data.get("status") == ExportStatus.COMPLETED and obj.status != ExportStatus.COMPLETED:
            data["completed_at"] = timezone.now()
        return data


class ExportOutputSerializer(serializers.ModelSerializer):
    sas_url = serializers.SerializerMethodField(read_only=True)

    def get_sas_url(self, obj: ExportOutput) -> str:
        region = obj.export.target_org.country if obj.export and obj.export.target_org else "default"
        return get_platform_blob_url_with_sas(blob_path=obj.blob_url, region=region)

    class Meta:
        model = ExportOutput
        fields = [
            "id",
            "export",
            "filename",
            "blob_url",
            "sas_url",
            "extension",
            "mime_type",
            "file_size",
            "file_display_name",
            "created_by",
            "created_at",
            "updated_at",
        ]


class ExportBulkUpdateStatusSerializer(serializers.ModelSerializer):
    class Meta:
        model = Export
        fields = [
            "status",
            "status_reason",
        ]
