# Generated by Django 4.1.2 on 2024-08-30 06:25

from django.db import migrations, models
import vapar.constants.exports


class Migration(migrations.Migration):

    dependencies = [
        ("exports", "0002_export_status_reason_alter_export_completed_at_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="export",
            name="is_hidden",
            field=models.BooleanField(default=False),
        ),
        migrations.AlterField(
            model_name="export",
            name="format",
            field=models.CharField(
                choices=[
                    ("CSV", "CSV"),
                    ("XML", "XML"),
                    ("PDF", "PDF"),
                    ("ZIP", "ZIP"),
                    ("MDB", "MDB"),
                ],
                max_length=3,
            ),
        ),
        migrations.AlterField(
            model_name="export",
            name="status",
            field=models.CharField(
                choices=[
                    ("PE", "PENDING"),
                    ("PR", "PROCESSING"),
                    ("CO", "COMPLETED"),
                    ("DL", "DOWNLOADED"),
                    ("FA", "FAILED"),
                ],
                default=vapar.constants.exports.ExportStatus["PENDING"],
                max_length=2,
            ),
        ),
        migrations.AlterField(
            model_name="export",
            name="status_reason",
            field=models.CharField(
                choices=[
                    ("GE", "GENERIC_ERROR"),
                    ("II", "INVALID_INSPECTION_ID"),
                    ("MD", "MISSING_DATA"),
                ],
                default=None,
                max_length=2,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="export",
            name="type",
            field=models.CharField(
                choices=[
                    ("BI", "BULK_INSPECTION_PDF"),
                    ("AS", "ASSET"),
                    ("DF", "DEFECT"),
                    ("IP", "INSPECTION_PLAN"),
                    ("PR", "PREDICTOR"),
                    ("IA", "INFO_ASSET"),
                    ("PM", "PACP7_MDB"),
                ],
                max_length=2,
            ),
        ),
    ]
