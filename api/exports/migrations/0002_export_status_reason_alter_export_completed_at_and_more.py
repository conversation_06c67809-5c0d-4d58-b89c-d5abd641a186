# Generated by Django 4.1.2 on 2024-06-05 00:02

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("exports", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="export",
            name="status_reason",
            field=models.CharField(
                choices=[
                    ("GE", "Generic Error"),
                    ("II", "Invalid Inspection ID"),
                    ("MD", "Missing Data"),
                ],
                default=None,
                max_length=2,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="export",
            name="completed_at",
            field=models.DateTimeField(default=None, null=True),
        ),
        migrations.AlterField(
            model_name="exportoutput",
            name="file_size",
            field=models.PositiveBigIntegerField(),
        ),
        migrations.AlterField(
            model_name="exportoutput",
            name="filename",
            field=models.CharField(max_length=1000),
        ),
    ]
