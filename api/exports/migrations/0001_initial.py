# Generated by Django 4.1.2 on 2024-06-03 04:59

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("organisations", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Export",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("completed_at", models.DateTimeField(null=True)),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("BI", "Bulk Inspection PDF Report"),
                            ("AS", "Asset"),
                            ("DF", "Defect"),
                            ("IP", "Inspection Plan"),
                            ("PR", "Predictor"),
                            ("IA", "InfoAsset"),
                            ("PM", "PACP7 MDB"),
                        ],
                        max_length=2,
                    ),
                ),
                (
                    "format",
                    models.Char<PERSON>ield(
                        choices=[
                            ("CSV", "Csv"),
                            ("XML", "Xml"),
                            ("PDF", "Pdf"),
                            ("ZIP", "Zip"),
                            ("MDB", "Mdb"),
                        ],
                        max_length=3,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("PE", "Pending"),
                            ("PR", "Processing"),
                            ("CO", "Completed"),
                            ("FA", "Failed"),
                        ],
                        default="PE",
                        max_length=2,
                    ),
                ),
                ("payload", models.JSONField(editable=False)),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="exports",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "target_org",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="exports",
                        to="organisations.organisations",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="ExportOutput",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("filename", models.CharField(max_length=1000, unique=True)),
                ("blob_url", models.CharField(max_length=1000)),
                ("extension", models.CharField(max_length=10)),
                ("mime_type", models.CharField(max_length=255)),
                ("file_size", models.BigIntegerField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="export_outputs",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "export",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="outputs",
                        to="exports.export",
                    ),
                ),
            ],
        ),
    ]
