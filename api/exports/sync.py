import time
import datetime

from django.conf import settings
from django.utils import timezone
from vapar.constants.exports import ExportStatus
from vapar.core.exports import ExportRequestQueueMessage

from api.exports.models import Export
from api.exports.queue_trigger import enqueue_export_message
from api.organisations.models import Organisations

POLL_INTERVAL_SECS = 2


def _noop_sleep(_: int):
    pass


def run_exports_synchronously(
    exports: list[Export],
    org: Organisations,
    max_poll_time_secs: int = 60,
) -> tuple[list[Export], list[Export]]:
    """
    Enqueue multiple exports for processing and wait for all of them to complete synchronously.

    :param exports: The exports to process
    :param org: The organisation that the exports belong to.
    :param max_poll_time_secs: The maximum time to wait for all the exports to complete.
        Any exports that have not completed by this time will be considered failed.

    :return: Two lists - the first list contains the exports that completed successfully, the second list
        contains the exports that either failed or timed out.
    """

    for exp in exports:
        exp.status = ExportStatus.PENDING
        exp.save()
        enqueue_export_message(
            payload=ExportRequestQueueMessage(
                export_id=exp.id,
                created_at=exp.created_at,
                target_org_id=exp.target_org_id,
            ),
            org=org,
        )

    completed_list = []
    failed_list = []
    pending_map = {exp.id: exp for exp in exports}

    sleep_fn = _noop_sleep if settings.DISABLE_SYNC_EXPORT_SLEEP else time.sleep

    start_time = timezone.now()
    cutoff_time = start_time + datetime.timedelta(seconds=max_poll_time_secs)
    while timezone.now() < cutoff_time:
        sleep_fn(POLL_INTERVAL_SECS)

        # Refresh
        pending_map = {exp.id: exp for exp in Export.objects.filter(id__in=pending_map.keys())}
        for exp in list(pending_map.values()):
            if exp.status in (ExportStatus.COMPLETED, ExportStatus.DOWNLOADED):
                completed_list.append(exp)
                del pending_map[exp.id]
            elif exp.status == ExportStatus.FAILED:
                failed_list.append(exp)
                del pending_map[exp.id]

        if not pending_map:
            return completed_list, failed_list

    pending_or_failed = list(pending_map.values()) + failed_list
    return completed_list, pending_or_failed
