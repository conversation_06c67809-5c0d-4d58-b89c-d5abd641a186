from uuid import uuid4

from django.db import models

from vapar.constants.exports import ExportType, ExportStatus, ExportFormat, ExportStatusReason
from vapar.core.exports import AnyExportPayload


class Export(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    target_org = models.ForeignKey(
        "organisations.Organisations",
        on_delete=models.SET_NULL,
        null=True,
        db_index=True,
        related_name="exports",
    )
    created_by = models.ForeignKey(
        "users.CustomUser",
        on_delete=models.SET_NULL,
        null=True,
        related_name="exports",
    )
    created_at = models.DateTimeField(auto_now_add=True, editable=False)
    updated_at = models.DateTimeField(auto_now=True)
    completed_at = models.DateTimeField(null=True, default=None)

    # Used for clear button on the FE
    is_hidden = models.BooleanField(default=False)

    # Whether the export was directly requested by a user or is a side effect of another action
    is_initiated_by_user = models.BooleanField(default=True)

    type = models.CharField(max_length=2, choices=ExportType.as_choices())
    format = models.CharField(max_length=3, choices=ExportFormat.as_choices())
    status = models.CharField(max_length=2, choices=ExportStatus.as_choices(), default=ExportStatus.PENDING)
    status_reason = models.CharField(max_length=2, choices=ExportStatusReason.as_choices(), null=True, default=None)

    payload = models.JSONField(editable=False)

    @property
    def parsed_payload(self) -> "AnyExportPayload":
        return AnyExportPayload.model_validate(self.payload)

    def save(self, *args, **kwargs):
        AnyExportPayload.model_validate(self.payload)  # Check that the payload is a valid AnyExportPayload
        super().save(*args, **kwargs)


class ExportOutput(models.Model):
    MAX_FILE_URL_LENGTH = 1_000

    id = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    export = models.ForeignKey(
        "exports.Export",
        on_delete=models.SET_NULL,
        null=True,
        db_index=True,
        related_name="outputs",
    )
    filename = models.CharField(max_length=MAX_FILE_URL_LENGTH)  # The final segment + extension of the blob_url
    blob_url = models.CharField(max_length=MAX_FILE_URL_LENGTH)  # "container/filepath"
    extension = models.CharField(max_length=10)
    mime_type = models.CharField(max_length=255)
    file_size = models.PositiveBigIntegerField()
    file_display_name = models.CharField(max_length=255, null=True, default=None)
    created_by = models.ForeignKey(
        "users.CustomUser", on_delete=models.SET_NULL, null=True, related_name="export_outputs"
    )
    created_at = models.DateTimeField(auto_now_add=True, editable=False)
    updated_at = models.DateTimeField(auto_now=True)
