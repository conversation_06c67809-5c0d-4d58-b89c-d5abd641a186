from django.urls import path

from .views import (
    ExportListCreateView,
    ExportRetrieveUpdateView,
    ExportPayloadRetrieveView,
    ExportOutputListCreateView,
    ExportBulkUpdateStatusView,
)

urlpatterns = [
    path("exports", ExportListCreateView.as_view(), name="exports-list-create"),
    path("exports/<uuid:uuid>", ExportRetrieveUpdateView.as_view(), name="exports-retrieve-update"),
    path("exports/<uuid:uuid>/payload", ExportPayloadRetrieveView.as_view(), name="exports-payload-retrieve"),
    path("exports/<uuid:uuid>/outputs", ExportOutputListCreateView.as_view(), name="export-outputs-list-create"),
    path("exports/status", ExportBulkUpdateStatusView.as_view(), name="exports-status-bulk-update"),
]
