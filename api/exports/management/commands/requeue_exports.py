from datetime import timedelta

from django.core.management import BaseCommand
from django.utils import timezone
from tqdm import tqdm
from vapar.constants.exports import ExportStatus
from vapar.core.exports import ExportRequestQueueMessage

from api.exports.models import Export
from api.exports.queue_trigger import enqueue_export_message


class Command(BaseCommand):
    help = """
    Requeue exports that have been stuck in a pending or processing state.
    """

    def add_arguments(self, parser):
        parser.add_argument(
            "--org_ids",
            nargs="+",
            type=int,
            help="Only requeue exports for these orgs. Default is all orgs",
        )
        parser.add_argument(
            "--updated_mins_ago",
            type=int,
            default=60,
            help="Only requeue exports that have been stuck for this many minutes",
        )

    def handle(self, *args, **options):
        org_ids = options.get("org_ids")
        updated_mins_ago = options["updated_mins_ago"]

        exports_qs = Export.objects.filter(
            status__in=[ExportStatus.PENDING, ExportStatus.PROCESSING],
            updated_at__lte=timezone.now() - timedelta(minutes=updated_mins_ago),
            is_hidden=False,
        ).select_related("target_org")

        if org_ids:
            exports_qs = exports_qs.filter(target_org_id__in=org_ids)

        exports = list(exports_qs)
        n_updated = exports_qs.update(status=ExportStatus.PENDING, updated_at=timezone.now())

        for export in tqdm(exports, desc="Requeuing exports", total=len(exports)):
            enqueue_export_message(
                payload=ExportRequestQueueMessage(
                    export_id=export.id,
                    target_org_id=export.target_org_id,
                    created_at=timezone.now(),
                ),
                org=export.target_org,
            )

        self.stdout.write(self.style.SUCCESS(f"Requeued {n_updated} exports"))
