from django.db.models import Count
from django.utils import timezone
from django_filters.rest_framework import DjangoFilterBackend
from djangorestframework_camel_case.parser import CamelCaseJ<PERSON>NParser
from djangorestframework_camel_case.render import Camel<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from drf_spectacular.types import OpenApiTypes
from drf_spectacular.utils import extend_schema, OpenApiRequest, OpenApiParameter
from rest_framework import generics, status
from rest_framework.filters import OrderingFilter
from rest_framework.renderers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.response import Response
from vapar.constants.exports import ExportStatus

from vapar.core.exports import ExportRequestQueueMessage

from api.common.enums import StatusEnum
from api.common.pagination import StatusCountPagination
from api.exports.models import Export, AnyExportPayload, ExportOutput
from api.exports.queue_trigger import enqueue_export_message
from api.exports.serializers import (
    ExportPayloadEnvelope,
    ExportSerializer,
    ExportUpdateSerializer,
    ExportOutputSerializer,
    ExportBulkUpdateStatusSerializer,
)
from api.actions.models import AuditList
from api.common.permissions import (
    IsAuthenticated,
    IsStandardUser,
    IsOrgScopedRequest,
    IsServiceUser,
    RelatesToExportBelongingToOrg,
)
from api.inspections import schemas as inspection_schemas
from api.inspections.utilities.validate_inspections import (
    get_inspections_for_validation,
    validate_inspections_for_export,
)


class ExportListCreateView(generics.ListCreateAPIView):
    permission_classes = [IsAuthenticated, IsOrgScopedRequest, IsStandardUser]
    serializer_class = ExportSerializer
    queryset = Export.objects.filter(is_hidden=False).prefetch_related("outputs")
    pagination_class = StatusCountPagination
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ["status", "type", "format", "status_reason", "is_initiated_by_user"]
    ordering_fields = ["created_at", "completed_at"]
    ordering = ["-created_at"]

    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    def get_queryset(self):
        qs = self.queryset.filter(target_org=self.request.organisation, created_by=self.request.user)

        include_system_initiated = self.request.query_params.get("include_system_initiated", "false").lower() == "true"
        if not include_system_initiated:
            qs = qs.filter(is_initiated_by_user=True)
        return qs

    @extend_schema(
        request=OpenApiRequest(ExportPayloadEnvelope),
        responses={
            status.HTTP_412_PRECONDITION_FAILED: inspection_schemas.bulk_validation_response_schema,
            status.HTTP_201_CREATED: ExportSerializer,
        },
    )
    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        # Asset owners should not be allowed to export contractor uploads pre-review
        inspections_to_validate = []
        inspections_qs = get_inspections_for_validation(serializer.validated_data["payload"]["inspectionIds"])
        for i in inspections_qs:
            if i.status != StatusEnum.UPLOADED.value and request.organisation == i.file.target_org:
                inspections_to_validate.append(i)
            elif request.organisation == i.file.upload_org:
                inspections_to_validate.append(i)

        if not inspections_to_validate:
            return Response(
                "Requesting organisation does not have permissions to export any of the given inspections",
                status.HTTP_403_FORBIDDEN,
            )

        serializer.validated_data["payload"]["inspectionIds"] = [str(i.uuid) for i in inspections_to_validate]

        # perform validation if required
        if serializer.validated_data["payload"]["runValidation"]:
            validation = validate_inspections_for_export(inspections_to_validate)
            if not validation["passed"]:
                return Response(validation, status.HTTP_412_PRECONDITION_FAILED)

        export = serializer.save()

        enqueue_export_message(
            payload=ExportRequestQueueMessage(
                export_id=export.id,
                target_org_id=export.target_org_id,
                created_at=export.created_at,
            ),
            org=request.organisation,
        )

        AuditList.objects.create(
            event_type="Export",
            table="Export",
            column="Multi",
            description=f"Export created with id {export.id} for organisation {export.target_org_id}",
            date_of_modification=timezone.now(),
            user=request.user,
        )

        return Response(self.get_serializer(instance=export).data, status=status.HTTP_201_CREATED)

    @extend_schema(
        parameters=[
            OpenApiParameter(
                "include_system_initiated",
                OpenApiTypes.BOOL,
                description="Include exports that were not initiated by a user",
                required=False,
                default=False,
            )
        ]
    )
    def get(self, request, *args, **kwargs):
        """List the exports created by the authenticated user."""

        queryset = self.filter_queryset(self.get_queryset())

        page = self.paginate_queryset(queryset)
        serializer = self.get_serializer(page, many=True)
        resp = self.get_paginated_response(serializer.data)

        counts_qs = queryset.order_by().values("status").annotate(count=Count("status"))
        counts = {s.value: 0 for s in ExportStatus}
        counts.update({item["status"]: item["count"] for item in counts_qs})

        resp.data["status_counts"] = counts

        return resp


class ExportRetrieveUpdateView(generics.RetrieveUpdateAPIView):
    """
    Retrieve summary information about an export.
    """

    permission_classes = [IsAuthenticated, IsOrgScopedRequest]
    serializer_class = ExportSerializer
    queryset = Export.objects.filter(is_hidden=False)

    lookup_url_kwarg = "uuid"

    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    http_method_names = ["get", "patch"]

    def get_queryset(self):
        return self.queryset.filter(target_org=self.request.organisation)

    def get_serializer_class(self):
        serializer_class = self.serializer_class
        if self.request.method == "GET":
            serializer_class = ExportSerializer
        elif self.request.method == "PATCH":
            serializer_class = ExportUpdateSerializer
        return serializer_class


class ExportPayloadRetrieveView(generics.RetrieveAPIView):
    permission_classes = [IsAuthenticated, IsOrgScopedRequest]
    queryset = Export.objects.filter(is_hidden=False)

    lookup_url_kwarg = "uuid"

    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [JSONRenderer]

    def get_queryset(self):
        return self.queryset.filter(target_org=self.request.organisation)

    @extend_schema(responses={status.HTTP_200_OK: AnyExportPayload})
    def get(self, request, *args, **kwargs):
        instance: Export = self.get_object()
        return Response(instance.parsed_payload.model_dump(by_alias=True))


class ExportOutputListCreateView(generics.ListCreateAPIView):
    permission_classes = [IsAuthenticated, IsOrgScopedRequest, RelatesToExportBelongingToOrg]
    serializer_class = ExportOutputSerializer
    queryset = (
        ExportOutput.objects.filter(export__is_hidden=False)
        .prefetch_related("export", "export__target_org")
        .order_by("updated_at")
    )
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    http_method_names = ["get", "post"]

    def get_permissions(self):
        permission_classes = self.permission_classes
        if self.request.method == "GET":
            permission_classes = [IsAuthenticated, IsStandardUser, IsOrgScopedRequest, RelatesToExportBelongingToOrg]
        elif self.request.method == "POST":
            permission_classes = [IsAuthenticated, IsServiceUser, IsOrgScopedRequest, RelatesToExportBelongingToOrg]
        return [permission() for permission in permission_classes]

    def get_queryset(self):
        return self.queryset.filter(export=self.kwargs.get("uuid"), export__target_org=self.request.organisation)

    def create(self, request, *args, **kwargs):
        request.data["export"] = self.kwargs.get("uuid")  # Always use the export id from the URL
        return super().create(request, *args, **kwargs)


class ExportBulkUpdateStatusView(generics.UpdateAPIView):
    permission_classes = [IsAuthenticated, IsServiceUser]
    serializer_class = ExportBulkUpdateStatusSerializer
    queryset = Export.objects.all()

    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    http_method_names = ["patch"]

    filter_backends = [DjangoFilterBackend]
    filterset_fields = {
        "status": ["exact"],
        "status_reason": ["exact"],
        "updated_at": ["lte", "gte"],
    }

    def update(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)

        queryset = self.filter_queryset(self.get_queryset())
        queryset.update(**serializer.validated_data, updated_at=timezone.now())
        return Response(status=status.HTTP_204_NO_CONTENT)

    @extend_schema(
        request=OpenApiRequest(ExportBulkUpdateStatusSerializer),
        responses={status.HTTP_204_NO_CONTENT: None},
        parameters=[
            OpenApiParameter("status", OpenApiTypes.STR, description="Update only exports with this status"),
            OpenApiParameter(
                "status_reason", OpenApiTypes.STR, description="Update only exports with this status reason"
            ),
            OpenApiParameter(
                "updated_at__lte",
                OpenApiTypes.DATETIME,
                description="Update only exports last updated at or before this time",
            ),
            OpenApiParameter(
                "updated_at__gte",
                OpenApiTypes.DATETIME,
                description="Update only exports last updated at or after this time",
            ),
        ],
    )
    def patch(self, request, *args, **kwargs):
        """Bulk update the status of exports."""
        return super().patch(request, *args, **kwargs)
