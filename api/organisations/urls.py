from django.urls import path

from .views import OrganisationDetail, OrganisationList, LinkContractorAssetOwner, OrganisationLogo

urlpatterns = [
    path("organisations/<int:id>", OrganisationDetail.as_view(), name="organisations"),
    path("organisations", OrganisationList.as_view(), name="organisations"),
    path("organisations/<int:id>/upload-logo", OrganisationLogo.as_view(), name="organisations"),
    path(
        "organisations/contractor/<int:contractor_id>/asset-owner/<int:asset_owner_id>/link",
        LinkContractorAssetOwner.as_view(),
        name="link-contractor-asset-owner",
    ),
]
