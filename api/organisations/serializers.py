import json
from drf_spectacular.utils import extend_schema_field
from rest_framework import serializers
from django.core.cache import caches

from .models import Organisations

from api.base.serializers import SewerDataField
from api.common.linked_organisations import get_linked_orgs
from api.common.storage import get_platform_storage_sas_token, get_platform_blob_url_with_sas
from django_countries.serializer_fields import Country<PERSON>ield
from django.conf import settings


sas_cache = caches["default"]


class BaseOrganisationSerializer(serializers.ModelSerializer):
    country = serializers.SerializerMethodField()
    logo_path = serializers.SerializerMethodField(read_only=True)
    standard_display_name = serializers.SerializerMethodField(read_only=True)
    sewer_data = SewerDataField()

    def get_country(self, data) -> str:
        return data.country.code

    def get_logo_path(self, data) -> str | None:
        if not data.logo_path:
            return None
        container = settings.BLOB_STORAGE_LOGOS_CONTAINER
        if (sas_token := sas_cache.get(container)) is None:
            sas_token = get_platform_storage_sas_token(container, region=data.country.code)
            sas_cache.set(container, sas_token, settings.DEFAULT_SAS_CACHE_TIMEOUT)

        sas_url = get_platform_blob_url_with_sas(data.logo_path, sas_token=sas_token)
        return sas_url

    def get_standard_display_name(self, data) -> str:
        return data.standard_key.display_name

    class Meta:
        model = Organisations
        fields = [
            "id",
            "full_name",
            "short_name",
            "org_type",
            "country",
            "standard_key",
            "standard_display_name",
            "email_domain",
            "logo_path",
            "sewer_data",
            "subscription_type",
            "frame_sample_rate",
        ]


class OrganisationSerializer(BaseOrganisationSerializer):
    country = CountryField()

    def create(self, validated_data):
        # get the default repair param from the standard
        standard = validated_data["standard_key"]
        validated_data["repair_param"] = json.dumps(standard.default_repair_param)
        return super().create(validated_data)

    class Meta:
        model = Organisations
        fields = BaseOrganisationSerializer.Meta.fields


class EditOrganisationSerializer(OrganisationSerializer):
    linked_organisations = serializers.SerializerMethodField(read_only=True)

    @extend_schema_field(BaseOrganisationSerializer(many=True))
    def get_linked_organisations(self, data):
        return BaseOrganisationSerializer(get_linked_orgs(data), many=True).data

    def update(self, instance: Organisations, validated_data: dict) -> Organisations:
        if new_org_type := validated_data.get("org_type"):
            instance.change_type(new_org_type)

        instance = super().update(instance, validated_data)
        return instance

    class Meta(OrganisationSerializer.Meta):
        fields = [
            "id",
            "full_name",
            "country",
            "email_domain",
            "logo_path",
            "org_type",
            "short_name",
            "sewer_data",
            "standard_key",
            "standard_display_name",
            "linked_organisations",
        ]
        extra_kwargs = {"id": {"read_only": False}}
