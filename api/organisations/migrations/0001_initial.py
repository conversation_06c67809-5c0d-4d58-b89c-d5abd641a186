# Generated by Django 4.1.2 on 2024-03-14 04:44

from django.db import migrations, models
import django.db.models.deletion
import django_countries.fields


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("defects", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Organisations",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("full_name", models.CharField(blank=True, max_length=200)),
                ("short_name", models.CharField(blank=True, max_length=8)),
                ("email_domain", models.CharField(blank=True, max_length=50)),
                ("sewer_data", models.BooleanField(default=True)),
                ("subscription_type", models.IntegerField(default=3)),
                ("manual_qa_required", models.BooleanField(default=False)),
                (
                    "org_type",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("Asset_Owner", "Asset_Owner"),
                            ("Contractor", "Contractor"),
                        ],
                        default="Asset_Owner",
                        max_length=20,
                        null=True,
                    ),
                ),
                (
                    "org_can_upload",
                    models.BooleanField(blank=True, default=True, null=True),
                ),
                (
                    "country",
                    django_countries.fields.CountryField(
                        blank=True, default="AU", max_length=2, null=True
                    ),
                ),
                ("logo_path", models.CharField(blank=True, max_length=1000, null=True)),
                ("require_tfa", models.BooleanField(default=False)),
                (
                    "repair_param",
                    models.TextField(
                        blank=True,
                        default={
                            "Debris build up class": 15,
                            "Debris build up length m": 1,
                            "Debris single instance class": 20,
                            "Maximum distance for patch": 30,
                            "Maximum number of patches in total": 3,
                            "Maximum number of patches over distance": 2,
                            "Minimum roots class": 15,
                            "Patch if score over length >=": 50,
                            "Patch length for scoring m": 1,
                        },
                        null=True,
                    ),
                ),
                (
                    "risk_doc_url",
                    models.CharField(blank=True, max_length=1000, null=True),
                ),
                ("use_azure_functions", models.BooleanField(default=True)),
                (
                    "standard_key",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="defects.standard",
                    ),
                ),
            ],
            options={
                "db_table": "service_organisations",
            },
        ),
        migrations.CreateModel(
            name="Contractors",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "org",
                    models.OneToOneField(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="organisations.organisations",
                    ),
                ),
            ],
            options={
                "db_table": "service_contractors",
            },
        ),
        migrations.CreateModel(
            name="AssetOwners",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "contractor",
                    models.ManyToManyField(blank=True, to="organisations.contractors"),
                ),
                (
                    "org",
                    models.OneToOneField(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="organisations.organisations",
                    ),
                ),
            ],
            options={
                "db_table": "service_assetowners",
            },
        ),
    ]
