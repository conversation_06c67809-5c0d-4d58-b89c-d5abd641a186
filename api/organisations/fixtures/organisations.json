[{"model": "organisations.organisations", "pk": 1, "fields": {"full_name": "Eurobodalla", "short_name": "Eurobod", "email_domain": "esc.nsw.gov.au", "sewer_data": true, "standard_key": 1, "subscription_type": 3, "manual_qa_required": false, "org_type": "Asset_Owner", "org_can_upload": true, "country": "AU", "logo_path": null, "require_tfa": false, "repair_param": "{\n        \"Minimum roots class\":15,\n        \"Debris build up class\":15,\n        \"Debris build up length m\": 1,\n        \"Debris single instance class\": 20,\n        \"Patch if score over length >=\": 50,\n        \"Patch length for scoring m\": 1,\n        \"Maximum number of patches over distance\": 2,\n        \"Maximum distance for patch\": 30,\n        \"Maximum number of patches in total\": 3\n        }", "risk_doc_url": null, "frame_sample_rate": 1500}}, {"model": "organisations.organisations", "pk": 104, "fields": {"full_name": "Melbourne Water", "short_name": "MW", "email_domain": "melbournewater.com.au", "sewer_data": false, "standard_key": 1, "subscription_type": 3, "manual_qa_required": false, "org_type": "Asset_Owner", "org_can_upload": true, "country": "AU", "logo_path": null, "require_tfa": false, "repair_param": "{\n        \"Minimum roots class\":15,\n        \"Debris build up class\":15,\n        \"Debris build up length m\": 1,\n        \"Debris single instance class\": 20,\n        \"Patch if score over length >=\": 50,\n        \"Patch length for scoring m\": 1,\n        \"Maximum number of patches over distance\": 2,\n        \"Maximum distance for patch\": 30,\n        \"Maximum number of patches in total\": 3\n        }", "risk_doc_url": null, "frame_sample_rate": 1500}}, {"model": "organisations.organisations", "pk": 106, "fields": {"full_name": "Wannon Water", "short_name": "<PERSON><PERSON>", "email_domain": "wannonwater.com.au", "sewer_data": true, "standard_key": 1, "subscription_type": 3, "manual_qa_required": false, "org_type": "Asset_Owner", "org_can_upload": true, "country": "AU", "logo_path": null, "require_tfa": false, "repair_param": "{\n        \"Minimum roots class\":15,\n        \"Debris build up class\":15,\n        \"Debris build up length m\": 1,\n        \"Debris single instance class\": 20,\n        \"Patch if score over length >=\": 50,\n        \"Patch length for scoring m\": 1,\n        \"Maximum number of patches over distance\": 2,\n        \"Maximum distance for patch\": 30,\n        \"Maximum number of patches in total\": 3\n        }", "risk_doc_url": null, "frame_sample_rate": 1500}}, {"model": "organisations.organisations", "pk": 130, "fields": {"full_name": "United Utilities", "short_name": "UUPLC", "email_domain": "uuplc.co.uk", "sewer_data": true, "standard_key": 2, "subscription_type": 3, "manual_qa_required": false, "org_type": "Asset_Owner", "org_can_upload": true, "country": "GB", "logo_path": null, "require_tfa": false, "repair_param": "{\n        \"Minimum roots class\":15,\n        \"Debris build up class\":15,\n        \"Debris build up length m\": 1,\n        \"Debris single instance class\": 20,\n        \"Patch if score over length >=\": 50,\n        \"Patch length for scoring m\": 1,\n        \"Maximum number of patches over distance\": 2,\n        \"Maximum distance for patch\": 30,\n        \"Maximum number of patches in total\": 3\n        }", "risk_doc_url": "orgfiles/Risk Matrix Template - United Utilities - 220727.pdf", "frame_sample_rate": 1500}}, {"model": "organisations.organisations", "pk": 133, "fields": {"full_name": "City of Ryde", "short_name": "<PERSON><PERSON><PERSON>", "email_domain": "ryde.nsw.gov.au", "sewer_data": false, "standard_key": 1, "subscription_type": 3, "manual_qa_required": false, "org_type": "Asset_Owner", "org_can_upload": true, "country": "AU", "logo_path": null, "require_tfa": false, "repair_param": "{\n        \"Minimum roots class\":15,\n        \"Debris build up class\":15,\n        \"Debris build up length m\": 1,\n        \"Debris single instance class\": 20,\n        \"Patch if score over length >=\": 50,\n        \"Patch length for scoring m\": 1,\n        \"Maximum number of patches over distance\": 2,\n        \"Maximum distance for patch\": 30,\n        \"Maximum number of patches in total\": 3\n        }", "risk_doc_url": null, "frame_sample_rate": 1500}}, {"model": "organisations.organisations", "pk": 139, "fields": {"full_name": "Campbelltown Council", "short_name": "Ctown", "email_domain": "campbelltown.nsw.gov.au", "sewer_data": false, "standard_key": 1, "subscription_type": 3, "manual_qa_required": false, "org_type": "Asset_Owner", "org_can_upload": true, "country": "AU", "logo_path": null, "require_tfa": false, "repair_param": "{\n        \"Minimum roots class\":15,\n        \"Debris build up class\":15,\n        \"Debris build up length m\": 1,\n        \"Debris single instance class\": 20,\n        \"Patch if score over length >=\": 50,\n        \"Patch length for scoring m\": 1,\n        \"Maximum number of patches over distance\": 2,\n        \"Maximum distance for patch\": 30,\n        \"Maximum number of patches in total\": 3\n        }", "risk_doc_url": null, "frame_sample_rate": 1500}}, {"model": "organisations.organisations", "pk": 171, "fields": {"full_name": "Richmond Valley", "short_name": "RVC", "email_domain": "richmondvalley.nsw.gov.au", "sewer_data": true, "standard_key": 1, "subscription_type": 3, "manual_qa_required": false, "org_type": "Asset_Owner", "org_can_upload": true, "country": "AU", "logo_path": null, "require_tfa": false, "repair_param": "{\n        \"Minimum roots class\":15,\n        \"Debris build up class\":15,\n        \"Debris build up length m\": 1,\n        \"Debris single instance class\": 20,\n        \"Patch if score over length >=\": 50,\n        \"Patch length for scoring m\": 1,\n        \"Maximum number of patches over distance\": 2,\n        \"Maximum distance for patch\": 30,\n        \"Maximum number of patches in total\": 3\n        }", "risk_doc_url": null, "frame_sample_rate": 1500}}]