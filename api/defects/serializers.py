from rest_framework import serializers

from .models import DefectScores, Standard, StandardHeader, StandardSubcategory
from ..base.models import Header


class DefectSerializer(serializers.ModelSerializer):
    defect_label = serializers.SerializerMethodField(read_only=True)

    def get_defect_label(self, obj) -> str | None:
        if obj.defect_description and obj.defect_code:
            if obj.defect_code not in obj.defect_description:
                return f"{obj.defect_code} - {obj.defect_description}"
            return obj.defect_description
        return None

    class Meta:
        model = DefectScores
        fields = [
            "id",
            "defect_code",
            "defect_description",
            "defect_label",
            "clock_position_required",
            "clock_spread_possible",
            "at_joint_required",
            "percentage_required",
            "start_survey",
            "end_survey",
        ]


class StandardSerializer(serializers.ModelSerializer):
    class Meta:
        model = Standard
        fields = ["id", "display_name", "name"]


class StandardSubcategorySerializer(serializers.ModelSerializer):
    standard = StandardSerializer(read_only=True, source="standard_key")
    region = serializers.SerializerMethodField()

    def get_region(self, data) -> str:
        return data.region.code

    class Meta:
        model = StandardSubcategory
        fields = ["id", "material_type", "pipe_type_sewer", "comment", "standard", "region"]


class StandardHeaderSerializer(serializers.ModelSerializer):
    options_description = serializers.SerializerMethodField(read_only=True)
    type = serializers.SerializerMethodField(read_only=True)
    mapped_field_name = serializers.SerializerMethodField(read_only=True)
    display_name = serializers.SerializerMethodField(read_only=True)

    def get_options_description(self, obj) -> list[str]:
        if obj.options_description:
            return [x.strip() for x in obj.options_description.split(",")]
        return []

    def get_type(self, obj) -> Header.HeaderType:
        return obj.header.type

    def get_mapped_field_name(self, obj) -> str | None:
        if obj.header.mapped_mpl_field and obj.header.mapped_mpl_field != obj.header.name:
            return obj.header.mapped_mpl_field
        return None

    def get_display_name(self, obj) -> str | None:
        if obj.header.display_name:
            return obj.header.display_name
        return obj.header.name

    class Meta:
        model = StandardHeader
        fields = [
            "name",
            "required",
            "description",
            "type",
            "data_type",
            "options_selections",
            "options_description",
            "mapped_field_name",
            "display_name",
        ]


class AllDefectsSerializer(serializers.ModelSerializer):
    substandard = StandardSubcategorySerializer(read_only=True, source="sub_standard")
    defect_model_name = serializers.SerializerMethodField(read_only=True)
    defect_model_id = serializers.SerializerMethodField(read_only=True)

    def get_defect_model_name(self, obj) -> str | None:
        if obj.defect_key:
            if obj.defect_key.name:
                return obj.defect_key.name
        return None

    def get_defect_model_id(self, obj) -> int | None:
        if obj.defect_key:
            return obj.defect_key.id
        return None

    class Meta:
        model = DefectScores
        fields = [
            "id",
            "substandard",
            "defect_description",
            "defect_model_name",
            "defect_model_id",
            "service_score",
            "structural_score",
            "defect_type",
            "quantity_1_default_val",
            "quantity_1_units",
            "quantity_2_default_val",
            "quantity_2_units",
            "defect_code",
            "at_joint_required",
            "material_applied",
            "characterisation_1",
            "characterisation_2",
            "clock_position_required",
            "clock_spread_possible",
            "percentage_required",
            "start_survey",
            "end_survey",
            "repair_priority",
            "repair_category",
            "fastpass_code",
            "continuous_score",
            "is_shown",
        ]
