[{"model": "defects.scoreseverity", "pk": 1, "fields": {"type": "structural", "standard": 2, "severity": "low", "min_score": 1, "max_score": 39}}, {"model": "defects.scoreseverity", "pk": 2, "fields": {"type": "service", "standard": 2, "severity": "low", "min_score": 1, "max_score": 1}}, {"model": "defects.scoreseverity", "pk": 3, "fields": {"type": "structural", "standard": 2, "severity": "medium", "min_score": 40, "max_score": 79}}, {"model": "defects.scoreseverity", "pk": 4, "fields": {"type": "service", "standard": 2, "severity": "medium", "min_score": 2, "max_score": 4}}, {"model": "defects.scoreseverity", "pk": 5, "fields": {"type": "structural", "standard": 2, "severity": "high", "min_score": 80, "max_score": 165}}, {"model": "defects.scoreseverity", "pk": 6, "fields": {"type": "service", "standard": 2, "severity": "high", "min_score": 5, "max_score": 20}}, {"model": "defects.scoreseverity", "pk": 7, "fields": {"type": "structural", "standard": 1, "severity": "low", "min_score": 1, "max_score": 9}}, {"model": "defects.scoreseverity", "pk": 8, "fields": {"type": "service", "standard": 1, "severity": "low", "min_score": 1, "max_score": 9}}, {"model": "defects.scoreseverity", "pk": 9, "fields": {"type": "structural", "standard": 1, "severity": "medium", "min_score": 10, "max_score": 59}}, {"model": "defects.scoreseverity", "pk": 10, "fields": {"type": "service", "standard": 1, "severity": "medium", "min_score": 10, "max_score": 59}}, {"model": "defects.scoreseverity", "pk": 11, "fields": {"type": "structural", "standard": 1, "severity": "high", "min_score": 60, "max_score": 165}}, {"model": "defects.scoreseverity", "pk": 12, "fields": {"type": "service", "standard": 1, "severity": "high", "min_score": 60, "max_score": 80}}, {"model": "defects.scoreseverity", "pk": 13, "fields": {"type": "structural", "standard": 4, "severity": "low", "min_score": 1, "max_score": 15}}, {"model": "defects.scoreseverity", "pk": 14, "fields": {"type": "service", "standard": 4, "severity": "low", "min_score": 1, "max_score": 7}}, {"model": "defects.scoreseverity", "pk": 15, "fields": {"type": "structural", "standard": 4, "severity": "medium", "min_score": 16, "max_score": 50}}, {"model": "defects.scoreseverity", "pk": 16, "fields": {"type": "service", "standard": 4, "severity": "medium", "min_score": 8, "max_score": 30}}, {"model": "defects.scoreseverity", "pk": 17, "fields": {"type": "structural", "standard": 4, "severity": "high", "min_score": 51, "max_score": 165}}, {"model": "defects.scoreseverity", "pk": 18, "fields": {"type": "service", "standard": 4, "severity": "high", "min_score": 31, "max_score": 80}}, {"model": "defects.scoreseverity", "pk": 19, "fields": {"type": "structural", "standard": 5, "severity": "low", "min_score": 1, "max_score": 20}}, {"model": "defects.scoreseverity", "pk": 20, "fields": {"type": "service", "standard": 5, "severity": "low", "min_score": 1, "max_score": 20}}, {"model": "defects.scoreseverity", "pk": 21, "fields": {"type": "structural", "standard": 5, "severity": "medium", "min_score": 21, "max_score": 60}}, {"model": "defects.scoreseverity", "pk": 22, "fields": {"type": "service", "standard": 5, "severity": "medium", "min_score": 21, "max_score": 60}}, {"model": "defects.scoreseverity", "pk": 23, "fields": {"type": "structural", "standard": 5, "severity": "high", "min_score": 61, "max_score": 165}}, {"model": "defects.scoreseverity", "pk": 24, "fields": {"type": "service", "standard": 5, "severity": "high", "min_score": 61, "max_score": 80}}, {"model": "defects.scoreseverity", "pk": 25, "fields": {"type": "structural", "standard": 6, "severity": "low", "min_score": 1, "max_score": 15}}, {"model": "defects.scoreseverity", "pk": 26, "fields": {"type": "service", "standard": 6, "severity": "low", "min_score": 1, "max_score": 10}}, {"model": "defects.scoreseverity", "pk": 27, "fields": {"type": "structural", "standard": 6, "severity": "medium", "min_score": 16, "max_score": 60}}, {"model": "defects.scoreseverity", "pk": 28, "fields": {"type": "service", "standard": 6, "severity": "medium", "min_score": 11, "max_score": 60}}, {"model": "defects.scoreseverity", "pk": 29, "fields": {"type": "structural", "standard": 6, "severity": "high", "min_score": 61, "max_score": 165}}, {"model": "defects.scoreseverity", "pk": 30, "fields": {"type": "service", "standard": 6, "severity": "high", "min_score": 61, "max_score": 80}}, {"model": "defects.scoreseverity", "pk": 31, "fields": {"type": "structural", "standard": 3, "severity": "low", "min_score": 1, "max_score": 2}}, {"model": "defects.scoreseverity", "pk": 32, "fields": {"type": "service", "standard": 3, "severity": "low", "min_score": 1, "max_score": 2}}, {"model": "defects.scoreseverity", "pk": 33, "fields": {"type": "structural", "standard": 3, "severity": "medium", "min_score": 3, "max_score": 4}}, {"model": "defects.scoreseverity", "pk": 34, "fields": {"type": "service", "standard": 3, "severity": "medium", "min_score": 3, "max_score": 4}}, {"model": "defects.scoreseverity", "pk": 35, "fields": {"type": "structural", "standard": 3, "severity": "high", "min_score": 5, "max_score": 5}}, {"model": "defects.scoreseverity", "pk": 36, "fields": {"type": "service", "standard": 3, "severity": "high", "min_score": 5, "max_score": 5}}, {"model": "defects.scoreseverity", "pk": 37, "fields": {"type": "structural", "standard": 2, "severity": "low", "min_score": 1, "max_score": 39}}, {"model": "defects.scoreseverity", "pk": 38, "fields": {"type": "service", "standard": 2, "severity": "low", "min_score": 1, "max_score": 1}}, {"model": "defects.scoreseverity", "pk": 39, "fields": {"type": "structural", "standard": 2, "severity": "medium", "min_score": 40, "max_score": 79}}, {"model": "defects.scoreseverity", "pk": 40, "fields": {"type": "service", "standard": 2, "severity": "medium", "min_score": 2, "max_score": 4}}, {"model": "defects.scoreseverity", "pk": 41, "fields": {"type": "structural", "standard": 2, "severity": "high", "min_score": 80, "max_score": 165}}, {"model": "defects.scoreseverity", "pk": 42, "fields": {"type": "service", "standard": 2, "severity": "high", "min_score": 5, "max_score": 20}}, {"model": "defects.scoreseverity", "pk": 43, "fields": {"type": "structural", "standard": 1, "severity": "low", "min_score": 1, "max_score": 9}}, {"model": "defects.scoreseverity", "pk": 44, "fields": {"type": "service", "standard": 1, "severity": "low", "min_score": 1, "max_score": 9}}, {"model": "defects.scoreseverity", "pk": 45, "fields": {"type": "structural", "standard": 1, "severity": "medium", "min_score": 10, "max_score": 59}}, {"model": "defects.scoreseverity", "pk": 46, "fields": {"type": "service", "standard": 1, "severity": "medium", "min_score": 10, "max_score": 59}}, {"model": "defects.scoreseverity", "pk": 47, "fields": {"type": "structural", "standard": 1, "severity": "high", "min_score": 60, "max_score": 165}}, {"model": "defects.scoreseverity", "pk": 48, "fields": {"type": "service", "standard": 1, "severity": "high", "min_score": 60, "max_score": 80}}, {"model": "defects.scoreseverity", "pk": 49, "fields": {"type": "structural", "standard": 4, "severity": "low", "min_score": 1, "max_score": 15}}, {"model": "defects.scoreseverity", "pk": 50, "fields": {"type": "service", "standard": 4, "severity": "low", "min_score": 1, "max_score": 7}}, {"model": "defects.scoreseverity", "pk": 51, "fields": {"type": "structural", "standard": 4, "severity": "medium", "min_score": 16, "max_score": 50}}, {"model": "defects.scoreseverity", "pk": 52, "fields": {"type": "service", "standard": 4, "severity": "medium", "min_score": 8, "max_score": 30}}, {"model": "defects.scoreseverity", "pk": 53, "fields": {"type": "structural", "standard": 4, "severity": "high", "min_score": 51, "max_score": 165}}, {"model": "defects.scoreseverity", "pk": 54, "fields": {"type": "service", "standard": 4, "severity": "high", "min_score": 31, "max_score": 80}}, {"model": "defects.scoreseverity", "pk": 55, "fields": {"type": "structural", "standard": 5, "severity": "low", "min_score": 1, "max_score": 20}}, {"model": "defects.scoreseverity", "pk": 56, "fields": {"type": "service", "standard": 5, "severity": "low", "min_score": 1, "max_score": 20}}, {"model": "defects.scoreseverity", "pk": 57, "fields": {"type": "structural", "standard": 5, "severity": "medium", "min_score": 21, "max_score": 60}}, {"model": "defects.scoreseverity", "pk": 58, "fields": {"type": "service", "standard": 5, "severity": "medium", "min_score": 21, "max_score": 60}}, {"model": "defects.scoreseverity", "pk": 59, "fields": {"type": "structural", "standard": 5, "severity": "high", "min_score": 61, "max_score": 165}}, {"model": "defects.scoreseverity", "pk": 60, "fields": {"type": "service", "standard": 5, "severity": "high", "min_score": 61, "max_score": 80}}, {"model": "defects.scoreseverity", "pk": 61, "fields": {"type": "structural", "standard": 6, "severity": "low", "min_score": 1, "max_score": 15}}, {"model": "defects.scoreseverity", "pk": 62, "fields": {"type": "service", "standard": 6, "severity": "low", "min_score": 1, "max_score": 10}}, {"model": "defects.scoreseverity", "pk": 63, "fields": {"type": "structural", "standard": 6, "severity": "medium", "min_score": 16, "max_score": 60}}, {"model": "defects.scoreseverity", "pk": 64, "fields": {"type": "service", "standard": 6, "severity": "medium", "min_score": 11, "max_score": 60}}, {"model": "defects.scoreseverity", "pk": 65, "fields": {"type": "structural", "standard": 6, "severity": "high", "min_score": 61, "max_score": 165}}, {"model": "defects.scoreseverity", "pk": 66, "fields": {"type": "service", "standard": 6, "severity": "high", "min_score": 61, "max_score": 80}}, {"model": "defects.scoreseverity", "pk": 67, "fields": {"type": "structural", "standard": 3, "severity": "low", "min_score": 1, "max_score": 2}}, {"model": "defects.scoreseverity", "pk": 68, "fields": {"type": "service", "standard": 3, "severity": "low", "min_score": 1, "max_score": 2}}, {"model": "defects.scoreseverity", "pk": 69, "fields": {"type": "structural", "standard": 3, "severity": "medium", "min_score": 3, "max_score": 4}}, {"model": "defects.scoreseverity", "pk": 70, "fields": {"type": "service", "standard": 3, "severity": "medium", "min_score": 3, "max_score": 4}}, {"model": "defects.scoreseverity", "pk": 71, "fields": {"type": "structural", "standard": 3, "severity": "high", "min_score": 5, "max_score": 5}}, {"model": "defects.scoreseverity", "pk": 72, "fields": {"type": "service", "standard": 3, "severity": "high", "min_score": 5, "max_score": 5}}]