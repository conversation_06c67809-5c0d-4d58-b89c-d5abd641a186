[{"model": "defects.standardheader", "pk": "004d37d0-5172-4406-b67e-1a9df292e0d2", "fields": {"header": "35d6efa1-f3cd-4e7f-80e2-417602f74b2c", "standard": 3, "name": "Vertical Datum", "code": "Vertical_Datum", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:12.947Z"}}, {"model": "defects.standardheader", "pk": "00e9e05f-bd66-4e57-b9d4-927e681a64ed", "fields": {"header": "4ef9b123-9582-4c23-9eb1-967dda2bc501", "standard": 6, "name": "Lining type", "code": "ACE", "required": false, "shown_by_default": false, "description": "Where a conduit has been lined, record the method of lining", "data_type": "string", "options_selections": "", "options_description": "Close fit lining, Cured in place lining, Lining with a continuous conduit (pipeline), e.g. a pipe string welded on the surface and pulled through the host conduit. Also called slip lining, Lining with discrete pipe units transported into and joined in the host conduit, Lining inserted during manufacture (Manufacturer’s lining), Segmental linings, Sprayed lining, Spirally wound lining, Other", "created_at": "2023-07-05T06:37:16.520Z"}}, {"model": "defects.standardheader", "pk": "010419da-b330-47f2-983a-3603b1bbe7ea", "fields": {"header": "3b2ead02-7e71-42a8-af25-693e70d9ba04", "standard": 4, "name": "Temperature", "code": "ADB", "required": false, "shown_by_default": false, "description": "Record the ambient temperature coded", "data_type": "options", "options_selections": "C, W", "options_description": "Below freezing (Cold), Above freezing (Warm)", "created_at": "2023-07-05T06:37:17.613Z"}}, {"model": "defects.standardheader", "pk": "011b5085-c0fd-44f7-8c23-df2a9030d214", "fields": {"header": "37c7dc06-3c44-40db-bb23-b22833d251df", "standard": 5, "name": "Joint Spacing", "code": "ACG", "required": true, "shown_by_default": true, "description": "Record the length (m) of the individual pipe units that comprise the pipeline. Where the pipe is continuous e.g. masonry, or PE, this field is left blank, and a remark made.", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:15.649Z"}}, {"model": "defects.standardheader", "pk": "0334733f-ec6b-4a59-bf59-07557ee9a0a8", "fields": {"header": "6a0a2b04-9d45-4198-a02c-bd3923f9eb2f", "standard": 5, "name": "Tidal influence", "code": "ADD", "required": false, "shown_by_default": false, "description": "Record tidal influence", "data_type": "options", "options_selections": "A, B", "options_description": "At or above high tide level, Below high tide level", "created_at": "2023-07-05T06:37:15.959Z"}}, {"model": "defects.standardheader", "pk": "03880545-74a1-486c-98ba-44b21165a395", "fields": {"header": "f733f544-b660-4c2f-bdaa-6694f30d1ca1", "standard": 4, "name": "Town or suburb", "code": "AAN", "required": false, "shown_by_default": false, "description": "The name of the town or suburb as specified by the asset owner", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:16.922Z"}}, {"model": "defects.standardheader", "pk": "03d424c1-8737-4de1-802f-5234a445cb41", "fields": {"header": "46c5e176-f46b-49f1-b9ae-31614d5d58ee", "standard": 2, "name": "Pre-cleaned", "code": "Precleaned", "required": false, "shown_by_default": false, "description": "", "data_type": "options", "options_selections": "Y, N, Z", "options_description": "Yes, No, Unknown", "created_at": "2023-07-05T06:37:13.956Z"}}, {"model": "defects.standardheader", "pk": "04170e8d-b502-4f8f-bb7e-176dfc4fa53c", "fields": {"header": "078cb335-8a88-4c48-9ff9-4455c5fabca6", "standard": 1, "name": "Standard", "code": "ABA", "required": false, "shown_by_default": false, "description": "The version of the standard and software used to record the data", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.941Z"}}, {"model": "defects.standardheader", "pk": "043a21d8-50fb-4409-80a2-3e46f16c51ee", "fields": {"header": "95fcc131-9947-4f0c-ac3f-289df540e555", "standard": 3, "name": "Owner", "code": "Owner", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.003Z"}}, {"model": "defects.standardheader", "pk": "049aa6b7-e259-41e1-9341-60fbecf1f6bc", "fields": {"header": "1e2775a9-8bc7-45e1-b0c0-0eaf835e920d", "standard": 5, "name": "Operators Reference", "code": "ABI", "required": false, "shown_by_default": false, "description": "The reference code or name for the inspection supplied by the operator or the operator’s company", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:15.405Z"}}, {"model": "defects.standardheader", "pk": "055d11be-f0a0-4887-95c6-d2a2c50cec2c", "fields": {"header": "667eb269-fbf5-4ac9-ab0a-a2391305a8b9", "standard": 5, "name": "Precipitation", "code": "ADA", "required": false, "shown_by_default": false, "description": "Record the precipitation", "data_type": "options", "options_selections": "N, R, S", "options_description": "No precipitation, Precipitation (rain), Melting snow or ice", "created_at": "2023-07-05T06:37:15.917Z"}}, {"model": "defects.standardheader", "pk": "0673677a-07ca-4575-ae15-c66872673759", "fields": {"header": "832f823f-5f0e-41bd-91a4-93be65552c3b", "standard": 3, "name": "Custom Field Three", "code": "Custom_Field_three", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.534Z"}}, {"model": "defects.standardheader", "pk": "067a970d-92cf-4692-a3d3-8b7d56387cd3", "fields": {"header": "95fcc131-9947-4f0c-ac3f-289df540e555", "standard": 4, "name": "Asset owner", "code": "AAM", "required": false, "shown_by_default": false, "description": "The name of the asset owner", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:16.906Z"}}, {"model": "defects.standardheader", "pk": "086d9e9a-9134-48c4-896f-eb0e9f360a26", "fields": {"header": "1c029f63-70e4-4511-ad3b-39c787572666", "standard": 6, "name": "Flow control measures", "code": "ADC", "required": false, "shown_by_default": false, "description": "Record the measures taken to deal with the flow at the time of the inspection", "data_type": "options", "options_selections": "B, N, P, Z", "options_description": "Flows have been blocked or diverted upstream, No measures taken, Flows partially blocked or diverted upstream, Other—record further details in remarks", "created_at": "2023-07-05T06:37:16.657Z"}}, {"model": "defects.standardheader", "pk": "088e7930-dc5d-4181-bd9d-97d0a771604f", "fields": {"header": "d3613be4-b984-4e3f-97d3-dc2e6707d5e3", "standard": 2, "name": "Survey Length", "code": "LengthSurveyed", "required": false, "shown_by_default": false, "description": "Length of the pipe inspected", "data_type": "number", "options_selections": [], "options_description": null, "created_at": "2023-11-09T07:01:00.882Z"}}, {"model": "defects.standardheader", "pk": "09ecd1e0-a350-4faf-b1ee-91a8207d4cc3", "fields": {"header": "184c5759-9d80-4207-be0f-c3f6b90be55c", "standard": 5, "name": "Height", "code": "ACB", "required": false, "shown_by_default": true, "description": "The height of the section in mm (##), not required where both dimensions are the same e.g. circular", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:15.578Z"}}, {"model": "defects.standardheader", "pk": "0a2e04f1-4064-4192-b32b-108b0f1334ed", "fields": {"header": "6b52d1b8-8dea-4a8c-8b28-7f1d64a45bc5", "standard": 6, "name": "Operator", "code": "ABH", "required": false, "shown_by_default": false, "description": "Record the name of the operator, company, work group or individual engaged to conduct the inspection. Record the name of the individual person conducting the inspection where appropriate.", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:16.326Z"}}, {"model": "defects.standardheader", "pk": "0a44cb43-a640-4657-89fa-810ef0f1a7af", "fields": {"header": "81e4ae6f-229a-458a-911d-16a670acd3b3", "standard": 4, "name": "Structural Mean Score", "code": "ACT", "required": false, "shown_by_default": false, "description": "Calculated mean structural score", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.516Z"}}, {"model": "defects.standardheader", "pk": "0a672232-ed3b-4462-a37b-86c5814c8191", "fields": {"header": "80f0c697-3167-498c-8599-fd77ec0b24d7", "standard": 5, "name": "Original coding system", "code": "ABB", "required": false, "shown_by_default": false, "description": "Where the coding has been translated from an earlier version or from another system, the name of the original coding system.", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:15.331Z"}}, {"model": "defects.standardheader", "pk": "0b9a10f9-dfb6-42e7-bf28-258b03b7d825", "fields": {"header": "1e2775a9-8bc7-45e1-b0c0-0eaf835e920d", "standard": 4, "name": "Operators Reference", "code": "ABI", "required": false, "shown_by_default": false, "description": "The reference code or name for the inspection supplied by the operator or the operator’s company", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.123Z"}}, {"model": "defects.standardheader", "pk": "0ba6b5c3-3696-4528-bb4f-4b0901ad018f", "fields": {"header": "30635b32-65f7-424f-a945-c765df0e7d83", "standard": 4, "name": "Preliminary Service Peak Grade", "code": "ACY", "required": false, "shown_by_default": false, "description": "Calculated peak service condition grade", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.584Z"}}, {"model": "defects.standardheader", "pk": "0ccdcdb7-2eef-4b0e-ae40-3b84dc71b69b", "fields": {"header": "5df0c89c-8e70-4c5d-be64-e80b57339deb", "standard": 2, "name": "Video Volume Reference", "code": "VideoVolumeRef", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:14.646Z"}}, {"model": "defects.standardheader", "pk": "0d481dff-3a3a-45d8-88ff-8038d3f7f8c7", "fields": {"header": "fb6f7061-3956-427e-8026-f8936d37454f", "standard": 2, "name": "Drainage Area", "code": "DrainageArea", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:14.046Z"}}, {"model": "defects.standardheader", "pk": "0e41d379-9f6e-4611-88af-783d0cce08e3", "fields": {"header": "55ce492c-51be-49f5-b410-982669da0b52", "standard": 4, "name": "Down Node Type", "code": "ACQ", "required": false, "shown_by_default": false, "description": "Record the type of node at the upstream", "data_type": "options", "options_selections": "SND, SMH, SPS, SIP, SMS, STND, STMH, STI, STO, STCP, STMS", "options_description": "Sewer node – includes: Buried Junctions, material change, bend/deviation, diameter change, Sewer Manhole, Sewer pump station, Sewer Inspection Point, Sewer Miscellaneous, Stormwater node – includes: Buried Junctions, material change, bend/deviation, diameter change, Stormwater manhole, Stormwater Inlet, Stormwater Outlet, Stormwater Catchpit, Stormwater Miscellaneous", "created_at": "2023-07-05T06:37:17.471Z"}}, {"model": "defects.standardheader", "pk": "0e8fd27c-f4f3-49ea-9f70-d27e9d133a04", "fields": {"header": "31839d43-fa88-4b3a-8c2e-ff140e37ac9b", "standard": 1, "name": "Client Defined 4", "code": "AED", "required": false, "shown_by_default": false, "description": "User defined", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:18.326Z"}}, {"model": "defects.standardheader", "pk": "1103c71b-c4c4-415c-b680-07117c961250", "fields": {"header": "1ebb8de0-500a-43dd-833a-fe0212ba0025", "standard": 4, "name": "Inspection Completion Status", "code": "ABS", "required": true, "shown_by_default": true, "description": "Record the completion status of the inspection", "data_type": "options", "options_selections": "IC, UI", "options_description": "Inspection Complete, Uncompleted Inspection", "created_at": "2023-07-05T06:37:17.215Z"}}, {"model": "defects.standardheader", "pk": "111caea1-03bd-4961-8b6c-8e0495042f36", "fields": {"header": "1c029f63-70e4-4511-ad3b-39c787572666", "standard": 3, "name": "Flow Control", "code": "Flow_Control", "required": false, "shown_by_default": false, "description": "", "data_type": "options", "options_selections": "B, D, L, N, P", "options_description": "Bypassed, De-watered Using Jetter, Lift Station, Not Controlled, Plugged", "created_at": "2023-07-05T06:37:13.158Z"}}, {"model": "defects.standardheader", "pk": "11744712-2055-48b8-8a72-dd31da61842c", "fields": {"header": "809a12e9-d6d8-41a0-93c3-c060d909c98b", "standard": 3, "name": "Custom Field One", "code": "Custom_Field_one", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.509Z"}}, {"model": "defects.standardheader", "pk": "12a12ba8-f25f-4c93-8435-f6880a4d1862", "fields": {"header": "4ef9b123-9582-4c23-9eb1-967dda2bc501", "standard": 3, "name": "Lining Method", "code": "Lining_Method", "required": false, "shown_by_default": false, "description": "", "data_type": "options", "options_selections": "CIP, FF, FP, GP, GRC, N, SC, SE, SL, SN, SP, SW, XX, ZZ", "options_description": "Cured-In-Place Pipe, Fold and Form or Deform/Reform, Formed-In-Place Liner, Grout-In-Place Liner, Glass Reinforced Cement, None, Continuous Slip Liner, Sectional Slip Liner, Spray Liner, Segmented Panel, Segmented Pi<PERSON>, Spiral Wound, Not Known, Other", "created_at": "2023-07-05T06:37:13.271Z"}}, {"model": "defects.standardheader", "pk": "12cbf75b-ba94-4155-a7ea-623310cf3973", "fields": {"header": "51f47f5a-87a4-48ae-8cba-391cbce70478", "standard": 3, "name": "MH Coordinate System", "code": "MH_Coordinate_System", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:12.933Z"}}, {"model": "defects.standardheader", "pk": "1356937d-042d-42d3-9432-c1b8e64a52eb", "fields": {"header": "59cd0410-9127-47ea-bc03-f1e846468f3c", "standard": 1, "name": "Direction of inspection", "code": "AAK", "required": false, "shown_by_default": true, "description": "Record the direction in which the camera is travelling to record images", "data_type": "options", "options_selections": "D, Q, U", "options_description": "The camera is travelling downstream—the same direction as the normal direction of flow, The direction of flow is not known (Query), The camera is travelling upstream—directly opposite the normal direction of flow", "created_at": "2023-07-05T06:37:18.002Z"}}, {"model": "defects.standardheader", "pk": "148c5e10-5576-488d-8743-dbd834ad9202", "fields": {"header": "1450ed30-5926-4e04-bdbc-1261a9c24efb", "standard": 3, "name": "Customer", "code": "Customer", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.017Z"}}, {"model": "defects.standardheader", "pk": "157d0478-f47e-4e57-9364-7a2f698614b3", "fields": {"header": "62ef07ca-fc5f-4a65-bf3d-51e2f25de746", "standard": 3, "name": "<PERSON><PERSON><PERSON>", "code": "<PERSON><PERSON><PERSON>", "required": true, "shown_by_default": true, "description": "", "data_type": "options", "options_selections": "A, B, C, E, H, O, R, S, T, U, Z", "options_description": "Arched (with flat bottom), Barrel (e.g. beer barrel shape),  Circular, Egg-Shaped, Horseshoe (i.e. inverted U with curved sidewalls), Oval (Elliptical), Rectangular, Square, Trapezoidal, U-Shaped with Flat Top (straight walls), Other", "created_at": "2023-07-05T06:37:12.786Z"}}, {"model": "defects.standardheader", "pk": "1b3b085e-393c-4079-90bf-80669c5cd4fd", "fields": {"header": "63947516-527b-4feb-bf0c-614a2a052db6", "standard": 2, "name": "Client Defined 17", "code": "ClientDefined17", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:14.971Z"}}, {"model": "defects.standardheader", "pk": "1cea5ff8-d6ed-4eef-b16e-7708374f2f38", "fields": {"header": "13da5c27-84bf-45ce-95f2-418b74796831", "standard": 1, "name": "General Remarks", "code": "ADE", "required": false, "shown_by_default": true, "description": "Record any information that cannot be included in any other way including errors in asset information, defects in the maintenance structures etc.", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:18.353Z"}}, {"model": "defects.standardheader", "pk": "1e1448e6-1aff-4ce4-91ef-198c77713130", "fields": {"header": "7a4e53ec-ab40-4a2a-903b-a817d402b2f8", "standard": 5, "name": "Preliminary Structural Peak Grade", "code": "ACX", "required": false, "shown_by_default": false, "description": "Calculated peak structural condition grade", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:15.887Z"}}, {"model": "defects.standardheader", "pk": "1f7c22d4-45b4-4ae3-9312-c0d4dd54ca88", "fields": {"header": "ff3f87cc-c289-4ae3-bbe2-b57ed1947c54", "standard": 2, "name": "Land Ownership", "code": "LandOwner", "required": false, "shown_by_default": false, "description": "", "data_type": "options", "options_selections": "PR, PU, X", "options_description": "Private, Public, Unknown", "created_at": "2023-07-05T06:37:14.107Z"}}, {"model": "defects.standardheader", "pk": "20ae5f8a-9e64-472c-8f30-921fc7e13377", "fields": {"header": "eb5388dd-f2bb-4a8d-9e7a-f8ca3d4ef53c", "standard": 2, "name": "Use of Drain/Sewer", "code": "UseOfDrainSewer", "required": false, "shown_by_default": true, "description": "", "data_type": "options", "options_selections": "C, F, S, T, W, LD, Z", "options_description": "Combined, Foul, Surface water, Trade effluent, Culverted watercourse, Subsoil or field drainage, Other", "created_at": "2023-07-05T06:37:14.312Z"}}, {"model": "defects.standardheader", "pk": "217492fc-79d5-474e-82f2-59fd34b7452e", "fields": {"header": "0ddabd9a-a8a7-4e16-80c0-dd2b824437d4", "standard": 2, "name": "Expected Length", "code": "Expected<PERSON><PERSON>th", "required": false, "shown_by_default": false, "description": "0.1 to 999.9", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:14.549Z"}}, {"model": "defects.standardheader", "pk": "219c1e7d-784f-4562-a643-3eda74da6657", "fields": {"header": "dc27cce9-929e-4980-891c-7e69e6e230a4", "standard": 1, "name": "Coder/Assessor", "code": "ABL", "required": false, "shown_by_default": false, "description": "Record the name of the person coding observations and producing report", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:18.043Z"}}, {"model": "defects.standardheader", "pk": "21aa04eb-820c-4e12-974f-f65c1c84224a", "fields": {"header": "577e6be3-61b8-4226-b2f6-2ca2fd042f58", "standard": 2, "name": "Upstream Node", "code": "UpstreamNode", "required": false, "shown_by_default": false, "description": "Upstream node of the pipe inspected", "data_type": "string", "options_selections": [], "options_description": null, "created_at": "2023-11-09T07:01:00.972Z"}}, {"model": "defects.standardheader", "pk": "22061dbe-c8b4-42fa-9911-59a746b65b31", "fields": {"header": "9e1d89cb-dbef-4c26-83eb-ef3d21c916f5", "standard": 5, "name": "Name of pipe system", "code": "AAP", "required": false, "shown_by_default": false, "description": "The name of the pipe system, or a pipe system reference as specified by the asset owner", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:15.233Z"}}, {"model": "defects.standardheader", "pk": "232672f1-cb64-45db-af32-be4b7748ad81", "fields": {"header": "4f83bd6b-8e8f-4952-94e7-505eba80f635", "standard": 2, "name": "Client Defined 16", "code": "ClientDefined16", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:14.959Z"}}, {"model": "defects.standardheader", "pk": "237ecec0-1806-4f1b-b77f-d5f1a0155d15", "fields": {"header": "8f2490a7-3089-4fef-8d24-ad38e24258f9", "standard": 2, "name": "Client Defined 10", "code": "ClientDefined10", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:14.859Z"}}, {"model": "defects.standardheader", "pk": "23ce9791-0260-45a0-83a4-b30f813f88cf", "fields": {"header": "db309101-854a-4b34-aeb1-bff23f62fc31", "standard": 2, "name": "Inspection Stage", "code": "InspectionStage", "required": false, "shown_by_default": false, "description": "", "data_type": "options", "options_selections": "A, <PERSON>, Z", "options_description": "Information provided by client, Information provided by the inspector to client, Other", "created_at": "2023-07-05T06:37:14.485Z"}}, {"model": "defects.standardheader", "pk": "24567794-23e1-42fb-b34b-597790963855", "fields": {"header": "2c76e908-5b32-4bb6-bead-e8f1fa195ce8", "standard": 5, "name": "Location", "code": "AAJ", "required": false, "shown_by_default": true, "description": "A description of the location of the pipe e.g. street name.", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:15.132Z"}}, {"model": "defects.standardheader", "pk": "249f31b2-cb3b-4786-9421-d0c328df45f6", "fields": {"header": "732192bc-ef6d-4e38-ab2d-fcebcdf789a3", "standard": 1, "name": "Finish node coordinates", "code": "AAG", "required": false, "shown_by_default": false, "description": "The grid reference (easting and northing coordinates) of the finish node", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.740Z"}}, {"model": "defects.standardheader", "pk": "25067019-7b93-4ff0-8e24-0733127a04b1", "fields": {"header": "13da5c27-84bf-45ce-95f2-418b74796831", "standard": 4, "name": "General comment", "code": "ADE", "required": true, "shown_by_default": true, "description": "Record any information that cannot be included in any other way", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.652Z"}}, {"model": "defects.standardheader", "pk": "2677b8ca-07bd-4306-95db-677f777f8c8d", "fields": {"header": "078cb335-8a88-4c48-9ff9-4455c5fabca6", "standard": 5, "name": "Standard", "code": "ABA", "required": true, "shown_by_default": false, "description": "The version of the standard used to record the data. This shall be in the form NZPIM (Gravity)—4th Edition 2019", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:15.318Z"}}, {"model": "defects.standardheader", "pk": "274cd33c-3d77-4609-a419-f570f1ce3658", "fields": {"header": "d9bc4b3a-7802-4401-8ce1-5b1b17e48a85", "standard": 3, "name": "Custom Field Five", "code": "Custom_Field_five", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.565Z"}}, {"model": "defects.standardheader", "pk": "27887c98-2e3a-4ca6-83b3-78ae1df541db", "fields": {"header": "2c76e908-5b32-4bb6-bead-e8f1fa195ce8", "standard": 4, "name": "Location", "code": "AAJ", "required": false, "shown_by_default": true, "description": "A description of the location of the pipe e.g. street name.", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:16.860Z"}}, {"model": "defects.standardheader", "pk": "27ad13e2-c98f-4624-8d6c-8af1e7ab695a", "fields": {"header": "30635b32-65f7-424f-a945-c765df0e7d83", "standard": 5, "name": "Preliminary Service Peak Grade", "code": "ACY", "required": false, "shown_by_default": false, "description": "Calculated peak service condition grade", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:15.902Z"}}, {"model": "defects.standardheader", "pk": "27f00e7d-327c-4374-8aec-5235a20c6ebf", "fields": {"header": "95fcc131-9947-4f0c-ac3f-289df540e555", "standard": 5, "name": "Asset owner", "code": "AAM", "required": false, "shown_by_default": false, "description": "The name of the asset owner", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:15.190Z"}}, {"model": "defects.standardheader", "pk": "2824d8e8-e3c8-4252-81ef-8fcba1aacb60", "fields": {"header": "832f823f-5f0e-41bd-91a4-93be65552c3b", "standard": 1, "name": "Client Defined 3", "code": "AEC", "required": false, "shown_by_default": false, "description": "User defined", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:18.312Z"}}, {"model": "defects.standardheader", "pk": "29191f4e-3659-461e-adf1-1f7ce71522b5", "fields": {"header": "aeb13168-2b5a-4a21-828f-c2c14479b08b", "standard": 2, "name": "Method of Inspection", "code": "MethodOfInspection", "required": true, "shown_by_default": true, "description": "", "data_type": "options", "options_selections": "A, B, C", "options_description": "Direct inspection (man entry), CCTV, Inspection from manhole or inspection chamber only", "created_at": "2023-07-05T06:37:13.968Z"}}, {"model": "defects.standardheader", "pk": "2922ee3a-2455-492e-9d9c-960c3ae8926a", "fields": {"header": "59cd0410-9127-47ea-bc03-f1e846468f3c", "standard": 3, "name": "Direction", "code": "Direction", "required": true, "shown_by_default": true, "description": "", "data_type": "options", "options_selections": "D, U", "options_description": "Downstream (camera pointing with flow), Upstream (camera pointing against flow)", "created_at": "2023-07-05T06:37:12.686Z"}}, {"model": "defects.standardheader", "pk": "293609dd-0e0b-4265-8a0f-78bdd8bab63f", "fields": {"header": "68b9f14e-5952-49ab-ac91-007e467b2787", "standard": 4, "name": "Material", "code": "ACD", "required": false, "shown_by_default": true, "description": "The material of the fabric of the pipe. Where the pipe has been lined the Material field shall be left blank.", "data_type": "options", "options_selections": "RC, UR, PE, PP, PVC, FPVC, GRP, FRP, AC, PFPF, GEW, EW, VC, CORS, DI, ST, CLS, CI, BK, CIS, ZC, ZS, ZP, ZZ", "options_description": "Steel Reinforced Concrete, Non-reinforced Concrete, Polyethylene, Polypropylene (including Profiled Wall Pipes), Polyvinyl Chloride, Fusible Polyvinyl Chloride, Glass Reinforced Plastic, Fibre Reinforced Plastic, Asbestos Cement, Pitch Fibre, Glazed Earthenware, Earthenware (unglazed), Vitrified Clay, Corrugated Steel Pipe, Ductile Iron, Steel (unlined), Cement lined Steel, Cast Iron, Brick and other masonry materials, Cast In-Situ concrete or Mortar, Unidentified type of concrete or cement mortar, Unidentified type of iron or steel, Unidentified type of plastics, Unidentified material", "created_at": "2023-07-05T06:37:17.303Z"}}, {"model": "defects.standardheader", "pk": "2a6f98e8-5171-44b9-aabc-753ab50c7ce8", "fields": {"header": "dc27cce9-929e-4980-891c-7e69e6e230a4", "standard": 5, "name": "Name of Coder", "code": "ABT", "required": true, "shown_by_default": true, "description": "Record the name of the person who encoded the pipe condition.", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:15.529Z"}}, {"model": "defects.standardheader", "pk": "2a81f7e4-5af0-4fae-a329-ff2dacba5ec8", "fields": {"header": "601eb87e-69ad-4c15-9d2c-79a41e0f6d55", "standard": 1, "name": "Surcharge Evidence", "code": "ABN", "required": false, "shown_by_default": false, "description": "Measure from the surface and record the apparent highest level of surcharge in the start node evidenced by accumulation of debris on step irons or walls", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:18.056Z"}}, {"model": "defects.standardheader", "pk": "2bcfad16-0deb-45f0-b742-86ac04ebac39", "fields": {"header": "184c5759-9d80-4207-be0f-c3f6b90be55c", "standard": 3, "name": "Height", "code": "Height", "required": true, "shown_by_default": true, "description": "", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:12.756Z"}}, {"model": "defects.standardheader", "pk": "2bd4cfa4-2b36-4cb8-a5a9-711d7761ed8a", "fields": {"header": "601eb87e-69ad-4c15-9d2c-79a41e0f6d55", "standard": 6, "name": "Surcharge Evidence", "code": "ABN", "required": false, "shown_by_default": false, "description": "Measure from the surface and record the apparent highest level of surcharge in the start node evidenced by accumulation of debris on step irons or walls", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:16.405Z"}}, {"model": "defects.standardheader", "pk": "2c634c83-e5f1-4b47-ac7b-82f85935abcd", "fields": {"header": "577e6be3-61b8-4226-b2f6-2ca2fd042f58", "standard": 4, "name": "Up node reference", "code": "AAD", "required": true, "shown_by_default": true, "description": "The asset ID of the upstream node", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:16.803Z"}}, {"model": "defects.standardheader", "pk": "2d6ea84e-84ca-4775-9e17-954161c62e09", "fields": {"header": "62ef07ca-fc5f-4a65-bf3d-51e2f25de746", "standard": 2, "name": "<PERSON><PERSON><PERSON>", "code": "<PERSON><PERSON><PERSON>", "required": false, "shown_by_default": false, "description": "", "data_type": "options", "options_selections": "A, B, C, E, H, O, R, T, U, K, CSC, RSC, Z", "options_description": "Arched with flat bottom, Barrel, Circular, Egg shaped, Horseshoe, Oval, Rectangular, Trapezoidal, U-shaped with flat top, Kerb block, Circular with smaller channel, Rectangular with smaller channel, Other", "created_at": "2023-07-05T06:37:14.371Z"}}, {"model": "defects.standardheader", "pk": "2dd7b8ea-9969-4dc9-9002-69848ab5ba79", "fields": {"header": "291a0da2-a377-4e83-9fa4-366289e9c9d4", "standard": 3, "name": "Sheet Number", "code": "Sheet_Number", "required": false, "shown_by_default": false, "description": "", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.111Z"}}, {"model": "defects.standardheader", "pk": "2ea35e95-7e59-4d4e-8199-d90191d48643", "fields": {"header": "d9bc4b3a-7802-4401-8ce1-5b1b17e48a85", "standard": 1, "name": "Client Defined 5", "code": "AEE", "required": false, "shown_by_default": false, "description": "User defined", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:18.338Z"}}, {"model": "defects.standardheader", "pk": "2ea5ff75-b907-476d-9950-9be3906d377c", "fields": {"header": "667eb269-fbf5-4ac9-ab0a-a2391305a8b9", "standard": 4, "name": "Precipitation", "code": "ADA", "required": false, "shown_by_default": false, "description": "Record the precipitation", "data_type": "options", "options_selections": "N, R, S", "options_description": "No precipitation, Precipitation (rain), Melting snow or ice", "created_at": "2023-07-05T06:37:17.598Z"}}, {"model": "defects.standardheader", "pk": "309168bb-e247-4fbf-a0f4-d9d897460989", "fields": {"header": "9e1d89cb-dbef-4c26-83eb-ef3d21c916f5", "standard": 1, "name": "Name of conduit system", "code": "AAP", "required": false, "shown_by_default": false, "description": "The name of the conduit system, or a conduit system reference as specified by the asset owner", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.767Z"}}, {"model": "defects.standardheader", "pk": "30ec2942-b5de-4917-8a09-6983b86aacad", "fields": {"header": "f5c6995b-d7bf-4346-829c-ff8e395e5315", "standard": 4, "name": "Depth at upstream node", "code": "ACH", "required": false, "shown_by_default": false, "description": "Record the depth of the invert of the pipe below cover level at the upstream node in m", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.378Z"}}, {"model": "defects.standardheader", "pk": "3159445a-e035-41c6-ae65-4e4187f35ea6", "fields": {"header": "60d324a5-ca56-4f53-88d7-fb68b8944dc4", "standard": 2, "name": "Year Constructed", "code": "YearConstructed", "required": false, "shown_by_default": false, "description": "CCYY-CCYY (range) or CCYY", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:14.563Z"}}, {"model": "defects.standardheader", "pk": "321fd67a-a471-4f07-b06b-798341f27206", "fields": {"header": "eb52f9d9-e499-4ac6-8b4a-63988ab64f87", "standard": 3, "name": "Custom Field twenty", "code": "Custom_Field_twenty", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.778Z"}}, {"model": "defects.standardheader", "pk": "32ee9041-cf15-451a-b5c2-076cce5a83d3", "fields": {"header": "1f912bf8-8afc-4f0d-886c-2dfe8ad4f8db", "standard": 6, "name": "Lining material", "code": "ACF", "required": false, "shown_by_default": false, "description": "Where a conduit has been lined, record the lining material", "data_type": "string", "options_selections": "", "options_description": "Cement (mortar) lining, Epoxy, Fibre reinforced plastics, Glass fibre reinforced cement, Glass fibre reinforced plastics, Polyethylene, Plastics (thermosetting) impregnated felt or woven sock, Flexible, Polyurethane, Polyurea, Polyvinylchloride, Other—record details in remarks", "created_at": "2023-07-05T06:37:16.534Z"}}, {"model": "defects.standardheader", "pk": "34caefb8-c5a0-4675-adf4-2166de96310d", "fields": {"header": "28b4efa5-1ec1-4f69-8ea7-f5c655d53ac1", "standard": 5, "name": "Operation of Pipeline", "code": "ACJ", "required": true, "shown_by_default": true, "description": "Record the operational mode of the pipeline", "data_type": "options", "options_selections": "G, P", "options_description": "Gravity (*Default), Pressure", "created_at": "2023-07-05T06:37:15.697Z"}}, {"model": "defects.standardheader", "pk": "34d037ab-fb5d-4f6a-bfaf-00a48fc68843", "fields": {"header": "31839d43-fa88-4b3a-8c2e-ff140e37ac9b", "standard": 3, "name": "Custom Field Four", "code": "Custom_Field_four", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.549Z"}}, {"model": "defects.standardheader", "pk": "3598930b-c5e2-42d0-984a-4affd6db7a4e", "fields": {"header": "c4a3b164-03d2-4c35-99d5-89a3aeefa543", "standard": 2, "name": "Client Defined 8", "code": "ClientDefined8", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:14.834Z"}}, {"model": "defects.standardheader", "pk": "359aefcb-c94f-41d4-b6ee-9ba9648d6ab8", "fields": {"header": "31839d43-fa88-4b3a-8c2e-ff140e37ac9b", "standard": 6, "name": "Client Defined 4", "code": "AED", "required": false, "shown_by_default": false, "description": "User defined", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:16.716Z"}}, {"model": "defects.standardheader", "pk": "371de3a5-1386-442f-bef5-68450b051850", "fields": {"header": "832f823f-5f0e-41bd-91a4-93be65552c3b", "standard": 6, "name": "Client Defined 3", "code": "AEC", "required": false, "shown_by_default": false, "description": "User defined", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:16.701Z"}}, {"model": "defects.standardheader", "pk": "378afa4e-2745-473b-9550-f7b4f8a5a580", "fields": {"header": "6dc17ef6-1830-4d5b-a95d-938a1d0c447f", "standard": 2, "name": "Node 1 Grid Ref X", "code": "Node1GridRefX", "required": false, "shown_by_default": false, "description": "0 to 999999", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:14.152Z"}}, {"model": "defects.standardheader", "pk": "37ea55c2-87d5-4fd0-a77f-525f5b050817", "fields": {"header": "54874ee9-4121-4fc8-a10e-066e5cbac0d8", "standard": 3, "name": "Year Renewed", "code": "Year_Renewed", "required": false, "shown_by_default": true, "description": "YYYY", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.353Z"}}, {"model": "defects.standardheader", "pk": "39398e7b-41d8-4654-8b52-60f3152e066b", "fields": {"header": "a96767ae-b8e1-46ad-9616-733da4dcf680", "standard": 4, "name": "Upstream node Location", "code": "AAT", "required": true, "shown_by_default": true, "description": "The address of the upstream node", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.011Z"}}, {"model": "defects.standardheader", "pk": "3a9c7f79-b3c5-4cd3-b32c-1a6a6ef65fab", "fields": {"header": "04453617-9aa3-4b49-a90e-cb383ede3b52", "standard": 2, "name": "Client Defined 15", "code": "ClientDefined15", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:14.944Z"}}, {"model": "defects.standardheader", "pk": "3ae63706-ab9d-4608-a313-9f3de167ffa7", "fields": {"header": "62ef07ca-fc5f-4a65-bf3d-51e2f25de746", "standard": 6, "name": "<PERSON><PERSON><PERSON>", "code": "ACA", "required": false, "shown_by_default": false, "description": "Record the shape of the cross section of the conduit", "data_type": "options", "options_selections": "A, C, E, O, R, U, X, Z", "options_description": "Arch shaped—circular soffit and flat invert with parallel sides, Circular, Oviform (egg shaped), Oval—circular invert and soffit (of equal diameter) with parallel sides, Rectangular, U shape—circular invert and flat top with parallel sides, Local section code to be specified by the asset owner and prefixed by an X, e.g. XA,XB etc, Other—a description shall be included as a general header comment (code ADE)immediately following", "created_at": "2023-07-05T06:37:16.456Z"}}, {"model": "defects.standardheader", "pk": "3b11bed5-485a-4169-8c8f-435ece6d954d", "fields": {"header": "577e6be3-61b8-4226-b2f6-2ca2fd042f58", "standard": 5, "name": "Up node reference", "code": "AAD", "required": true, "shown_by_default": true, "description": "The asset ID of the upstream node", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:15.074Z"}}, {"model": "defects.standardheader", "pk": "3b1589dd-1a8b-41af-a9e8-4d3cc2967198", "fields": {"header": "f7e2642d-09a6-48fb-a2b6-7e9c12382b4f", "standard": 6, "name": "ID Pipe", "code": "AAA", "required": true, "shown_by_default": true, "description": "The conduit reference as specified by the asset owner", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:16.156Z"}}, {"model": "defects.standardheader", "pk": "3b1fbc70-fe0b-412d-a87c-c1f18ec3e0e8", "fields": {"header": "aeb13168-2b5a-4a21-828f-c2c14479b08b", "standard": 6, "name": "Method of inspection", "code": "ABE", "required": false, "shown_by_default": false, "description": "Record the method used to access the conduit for inspection", "data_type": "options", "options_selections": "FZ, LP, M, PR, PS, SS, TV, Z", "options_description": "Inspection by means of a fixed position zoom pipeline camera, Inspection by means of a remotely controlled laser profiler passed through the conduit, Direct inspection of a conduit by a person walking through the conduit (Mann<PERSON>), Push rod, Inspection by means of a remotely controlled 3D optical pipeline scanner passed through the conduit, Inspection by means of a remotely controlled sonar scanner passed through the conduit, Inspection by means of a remotely controlled television camera passed through the conduit, Other", "created_at": "2023-07-05T06:37:16.294Z"}}, {"model": "defects.standardheader", "pk": "3c101391-0c2c-4a9a-a3bd-81009ef45d03", "fields": {"header": "55ce492c-51be-49f5-b410-982669da0b52", "standard": 5, "name": "Down Node Type", "code": "ACQ", "required": false, "shown_by_default": false, "description": "Record the type of node at the upstream", "data_type": "options", "options_selections": "SND, SMH, SPS, SIP, SMS, STND, STMH, STI, STO, STCP, STMS", "options_description": "Sewer node – includes: Buried Junctions, material change, bend/deviation, diameter change, Sewer Manhole, Sewer pump station, Sewer Inspection Point, Sewer Miscellaneous, Stormwater node – includes: Buried Junctions, material change, bend/deviation, diameter change, Stormwater manhole, Stormwater Inlet, Stormwater Outlet, Stormwater Catchpit, Stormwater Miscellaneous", "created_at": "2023-07-05T06:37:15.774Z"}}, {"model": "defects.standardheader", "pk": "3c285c4e-07f1-4dd8-ac1a-a74505e810d8", "fields": {"header": "46c5e176-f46b-49f1-b9ae-31614d5d58ee", "standard": 3, "name": "PreCleaning", "code": "PreCleaning", "required": true, "shown_by_default": true, "description": "", "data_type": "options", "options_selections": "H, L, N, X, Z", "options_description": "Heavy Cleaning, Light Cleaning, No Pre-Cleaning, Not Known, Other", "created_at": "2023-07-05T06:37:12.671Z"}}, {"model": "defects.standardheader", "pk": "3c7ddafb-4ff9-48a1-b30e-37a8b3a6eddf", "fields": {"header": "809a12e9-d6d8-41a0-93c3-c060d909c98b", "standard": 1, "name": "Client Defined 1", "code": "AEA", "required": false, "shown_by_default": false, "description": "User defined", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:18.285Z"}}, {"model": "defects.standardheader", "pk": "3c9be958-3366-4b15-900c-b1722d4e7b2f", "fields": {"header": "2c76e908-5b32-4bb6-bead-e8f1fa195ce8", "standard": 2, "name": "Location (Street Name)", "code": "LocationStreet", "required": true, "shown_by_default": true, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.854Z"}}, {"model": "defects.standardheader", "pk": "3ce51905-d6e3-4c54-ade1-2429ca38cd4b", "fields": {"header": "d38594f1-49a3-460c-b7eb-f98fa43045f7", "standard": 2, "name": "Lateral Inspection Start Point", "code": "LateralInspStart", "required": false, "shown_by_default": false, "description": "", "data_type": "options", "options_selections": "A, B", "options_description": "Connection to the main sewer, third node", "created_at": "2023-07-05T06:37:14.273Z"}}, {"model": "defects.standardheader", "pk": "3ebc5666-2f15-41e5-8a3d-3692e06e5bb4", "fields": {"header": "887754f5-7de7-45ca-9705-f1f8711c47b8", "standard": 2, "name": "Node 1 Grid Ref Y", "code": "Node1GridRefY", "required": false, "shown_by_default": false, "description": "0 to 999999", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:14.170Z"}}, {"model": "defects.standardheader", "pk": "3ecdafdd-c22b-40e3-9acf-abf1036380f8", "fields": {"header": "882a5f44-4d2d-4c09-8fc0-2c48f5bd1538", "standard": 4, "name": "Structural Peak Score", "code": "ACS", "required": false, "shown_by_default": false, "description": "Calculated peak structural score", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.498Z"}}, {"model": "defects.standardheader", "pk": "4043dd55-c376-43a5-b3b2-296a900b959e", "fields": {"header": "3e140d9a-b8e1-4f95-9c6b-ee252407af43", "standard": 3, "name": "Up Rim to Invert", "code": "Up_Rim_to_Invert", "required": false, "shown_by_default": false, "description": "", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.368Z"}}, {"model": "defects.standardheader", "pk": "407895ea-b31c-4ca0-b9d5-5e737fa50db6", "fields": {"header": "1e2775a9-8bc7-45e1-b0c0-0eaf835e920d", "standard": 1, "name": "Contractor <PERSON>", "code": "ABI", "required": false, "shown_by_default": false, "description": "The inspection company's or operator’s job reference ", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:18.015Z"}}, {"model": "defects.standardheader", "pk": "4089fe2b-47cc-4e6b-9a7c-9d428f6552dc", "fields": {"header": "b1eee34d-767f-48a0-9439-35c82a6052e1", "standard": 5, "name": "Asset owner’s Reference", "code": "ABJ", "required": false, "shown_by_default": false, "description": "The reference code or name for the inspection supplied by the asset owner", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:15.419Z"}}, {"model": "defects.standardheader", "pk": "41857ef2-495a-4bf9-9e35-4fcb465d3629", "fields": {"header": "253243a9-ef6a-4ba6-8d48-54e164b20cda", "standard": 2, "name": "Critical Drain/Sewer", "code": "CriticalDrainSewer ", "required": false, "shown_by_default": false, "description": "", "data_type": "options", "options_selections": "A, B, C, Z", "options_description": "Most critical, Med-critical, Non-critical, Not known", "created_at": "2023-07-05T06:37:14.459Z"}}, {"model": "defects.standardheader", "pk": "41b93b35-9bf6-41fd-8544-2f8e17eebae4", "fields": {"header": "4ef9b123-9582-4c23-9eb1-967dda2bc501", "standard": 1, "name": "Lining type", "code": "ACE", "required": false, "shown_by_default": false, "description": "Where a conduit has been lined, record the method of lining", "data_type": "string", "options_selections": "", "options_description": "Close fit lining, Cured in place lining, Lining with a continuous conduit (pipeline), e.g. a pipe string welded on the surface and pulled through the host conduit. Also called slip lining, Lining with discrete pipe units transported into and joined in the host conduit, Lining inserted during manufacture (Manufacturer’s lining), Segmental linings, Sprayed lining, Spirally wound lining, Other", "created_at": "2023-07-05T06:37:18.157Z"}}, {"model": "defects.standardheader", "pk": "4249b851-31ff-4a65-927d-248d6fce5102", "fields": {"header": "0ddabd9a-a8a7-4e16-80c0-dd2b824437d4", "standard": 4, "name": "Pipe <PERSON>", "code": "ABQ", "required": true, "shown_by_default": true, "description": "Record the measured length of the pipe asset (#.#).", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.189Z"}}, {"model": "defects.standardheader", "pk": "4273de6c-1085-4675-bbe5-33d38eadcd83", "fields": {"header": "59cd0410-9127-47ea-bc03-f1e846468f3c", "standard": 2, "name": "Direction", "code": "Direction", "required": false, "shown_by_default": true, "description": "", "data_type": "options", "options_selections": "D, U, Z", "options_description": "Downstream, Upstream, Not known", "created_at": "2023-07-05T06:37:13.943Z"}}, {"model": "defects.standardheader", "pk": "42edae60-e746-4eaf-afac-f568534c67a1", "fields": {"header": "6b52d1b8-8dea-4a8c-8b28-7f1d64a45bc5", "standard": 3, "name": "Surveyed By", "code": "Surveyed_By", "required": true, "shown_by_default": true, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:12.632Z"}}, {"model": "defects.standardheader", "pk": "43cb016e-dd2d-4a39-aa49-0e4613e74bb8", "fields": {"header": "ff28a77e-9bdd-474c-b570-2ea998fd67c6", "standard": 5, "name": "Down node coordinate", "code": "AAG", "required": false, "shown_by_default": false, "description": "The grid reference (coordinates) of the downstream node", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:15.115Z"}}, {"model": "defects.standardheader", "pk": "456f7e3a-959f-4371-bf05-8c564b920175", "fields": {"header": "46c5e176-f46b-49f1-b9ae-31614d5d58ee", "standard": 5, "name": "Cleaning Status", "code": "ACM", "required": true, "shown_by_default": true, "description": "Record whether the pipeline was cleaned prior to the inspection", "data_type": "options", "options_selections": "LC, HC, NC, RC", "options_description": "The pipe was light cleaned prior to the inspection, The pipe was heavy cleaned prior to the inspection, The pipe was not cleaned prior to the inspection, The pipe was root cut prior to inspection", "created_at": "2023-07-05T06:37:15.729Z"}}, {"model": "defects.standardheader", "pk": "4783b9e8-5262-433a-ab66-3762635948df", "fields": {"header": "4ef9b123-9582-4c23-9eb1-967dda2bc501", "standard": 2, "name": "Lining Type", "code": "LiningType", "required": false, "shown_by_default": false, "description": "", "data_type": "options", "options_selections": "M, SP, CIP, SEG, DP, CP, CF, SW, Z", "options_description": "Lining inserted during manufacture, Sprayed lining, Cured in place lining, Segmental linings, Lining with discrete pipes, Lining with continuous pipes, Close fit lining, Spirally wound lining, Other", "created_at": "2023-07-05T06:37:14.429Z"}}, {"model": "defects.standardheader", "pk": "48565dc9-883d-4775-9001-f76e5633a948", "fields": {"header": "5569781a-faf2-4c54-bd81-d2dd65257b14", "standard": 1, "name": "<PERSON><PERSON><PERSON>", "code": "ACC", "required": false, "shown_by_default": false, "description": "The width of the section in mm—not required where both dimensions are the same, e.g.circular", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:18.132Z"}}, {"model": "defects.standardheader", "pk": "498e21eb-88f2-49ff-b671-6d31e0986a8b", "fields": {"header": "ff3f87cc-c289-4ae3-bbe2-b57ed1947c54", "standard": 6, "name": "Land ownership", "code": "AAQ", "required": false, "shown_by_default": false, "description": "Record the ownership of the land denoted as:", "data_type": "options", "options_selections": "C, Q, T", "options_description": "Public land (Common or Crown land), Not known (Query), Private land (Torrens Title)", "created_at": "2023-07-05T06:37:16.081Z"}}, {"model": "defects.standardheader", "pk": "49d772ab-7b97-4c51-977c-208f1b4408fb", "fields": {"header": "1f912bf8-8afc-4f0d-886c-2dfe8ad4f8db", "standard": 5, "name": "Lining material", "code": "ACF", "required": true, "shown_by_default": true, "description": "Where a pipe has been lined, record the lining material", "data_type": "options", "options_selections": "CL, EP, GRP, FRP, PE, PVC, CIP", "options_description": "Cement Lining (including Sprayed Concrete or Geopolymer), Epoxy Lining, Glass Reinforced Plastic, Fibre Reinforced Plastic, Polyethylene, Polyvinyl Chloride, Cured In-Place Polyurethane or Vinyl Ester", "created_at": "2023-07-05T06:37:15.633Z"}}, {"model": "defects.standardheader", "pk": "49dcf075-0362-4cf4-acd7-7de79f228f8e", "fields": {"header": "2c76e908-5b32-4bb6-bead-e8f1fa195ce8", "standard": 1, "name": "Street", "code": "AAJ", "required": false, "shown_by_default": true, "description": "A description of the location of the conduit e.g. street name. Include a street directory reference where this is a normal method of defining location", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.890Z"}}, {"model": "defects.standardheader", "pk": "4a412141-3b93-4831-990a-60d551f50d46", "fields": {"header": "e0b41c70-229c-4aa3-af46-223b031d3b07", "standard": 5, "name": "Date of Data Entry", "code": "ABU", "required": false, "shown_by_default": false, "description": "The date of the data entry (coding is undertaken) if different to the date of inspection using the DD/MM/YYYY format.", "data_type": "date", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:15.543Z"}}, {"model": "defects.standardheader", "pk": "4a82953e-6888-412e-bc88-c88e7a3fabae", "fields": {"header": "e16c52ae-56e8-43a9-bda0-f82798293016", "standard": 4, "name": "Time of inspection", "code": "ABG", "required": true, "shown_by_default": true, "description": "The time as specified in ISO 8601 using the 24-hour hh:mm format", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.095Z"}}, {"model": "defects.standardheader", "pk": "4b2b92f5-8044-44ca-9ab4-cb82ccb7dcbf", "fields": {"header": "11b219d8-ae79-4905-b1ce-3d0cd12412d2", "standard": 3, "name": "Down Easting", "code": "Down_Easting", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:12.896Z"}}, {"model": "defects.standardheader", "pk": "4b47dc23-6933-4fae-9a7a-f67e9307823a", "fields": {"header": "f7e2642d-09a6-48fb-a2b6-7e9c12382b4f", "standard": 5, "name": "AssetID", "code": "AAA", "required": true, "shown_by_default": true, "description": "Unique asset identification number as supplied by the asset owner, or generated by the Contractor if the inspected pipe is a new asset.", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:15.061Z"}}, {"model": "defects.standardheader", "pk": "4b85b37b-765d-45eb-9115-ccdf85e530e2", "fields": {"header": "dc27cce9-929e-4980-891c-7e69e6e230a4", "standard": 6, "name": "Coder/Assessor", "code": "ABL", "required": false, "shown_by_default": false, "description": "Record the name of the person coding observations and producing report", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:16.391Z"}}, {"model": "defects.standardheader", "pk": "4c3e950d-90fa-4cda-9703-afbfa4dfd2be", "fields": {"header": "d9bc4b3a-7802-4401-8ce1-5b1b17e48a85", "standard": 6, "name": "Client Defined 5", "code": "AEE", "required": false, "shown_by_default": false, "description": "User defined", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:16.728Z"}}, {"model": "defects.standardheader", "pk": "4c41a229-7786-4734-aa55-e00e3ab446a6", "fields": {"header": "bef2a86d-c43e-4fc4-94b4-808420302548", "standard": 3, "name": "Custom Field Six", "code": "Custom_Field_six", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.579Z"}}, {"model": "defects.standardheader", "pk": "4d9896b5-12c0-4059-b0ba-71ff5a14824c", "fields": {"header": "5569781a-faf2-4c54-bd81-d2dd65257b14", "standard": 6, "name": "<PERSON><PERSON><PERSON>", "code": "ACC", "required": false, "shown_by_default": false, "description": "The width of the section in mm—not required where both dimensions are the same, e.g.circular", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:16.486Z"}}, {"model": "defects.standardheader", "pk": "4f6b638b-d778-4adc-9415-ee005ad710fe", "fields": {"header": "f0d35326-2dde-49f5-a1b2-5fa36b702495", "standard": 2, "name": "Depth at Finish Node", "code": "DepthAtFinishNode", "required": false, "shown_by_default": false, "description": "0 to 99.99", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:14.199Z"}}, {"model": "defects.standardheader", "pk": "4ffae799-3a6e-46e8-91cb-8fa872bf143e", "fields": {"header": "3d38d2e8-8410-4191-a3c0-fef476404c4f", "standard": 3, "name": "Down Elevation", "code": "Down_Elevation", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:12.910Z"}}, {"model": "defects.standardheader", "pk": "50d7adbd-d136-4861-9ca8-49fc7b55ba66", "fields": {"header": "be75d367-8ac8-457a-a8ff-2c0221f8cb5c", "standard": 3, "name": "Custom Field nineteen", "code": "Custom_Field_nineteen", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.764Z"}}, {"model": "defects.standardheader", "pk": "51bda2d3-4b4f-4acb-b9a6-2b90cb2f2ff4", "fields": {"header": "6b52d1b8-8dea-4a8c-8b28-7f1d64a45bc5", "standard": 5, "name": "Name of Operator", "code": "ABH", "required": true, "shown_by_default": true, "description": "Record the name of the inspection equipment operator.", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:15.390Z"}}, {"model": "defects.standardheader", "pk": "51e88681-f531-4b63-b538-15dd6fb28d38", "fields": {"header": "5569b600-b02e-4af9-810c-10b83d56b7b5", "standard": 5, "name": "Drawing Number", "code": "AAS", "required": false, "shown_by_default": false, "description": "The drawing reference number on which the pipeline is shown, if applicable", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:15.276Z"}}, {"model": "defects.standardheader", "pk": "5213a8cc-bd83-4431-b3b2-36dee01cac47", "fields": {"header": "0ddabd9a-a8a7-4e16-80c0-dd2b824437d4", "standard": 3, "name": "Total Length", "code": "Total_Length", "required": false, "shown_by_default": false, "description": "", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.311Z"}}, {"model": "defects.standardheader", "pk": "53317cea-4ae4-4b57-bc39-ca31ca0f8393", "fields": {"header": "184c5759-9d80-4207-be0f-c3f6b90be55c", "standard": 1, "name": "Diameter", "code": "ACB", "required": false, "shown_by_default": true, "description": "The height or diameter of the section in mm", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:18.117Z"}}, {"model": "defects.standardheader", "pk": "534053cc-6d9b-4a8e-9f7d-8121330422a5", "fields": {"header": "bb705029-6500-4112-a6bd-8c0c6193ed0b", "standard": 4, "name": "Date of inspection", "code": "ABF", "required": true, "shown_by_default": true, "description": "Record the short date of the inspection using the DD/MM/YYYY format", "data_type": "date", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.082Z"}}, {"model": "defects.standardheader", "pk": "53ece599-cd0e-4440-954b-bf0cb05cda17", "fields": {"header": "5569781a-faf2-4c54-bd81-d2dd65257b14", "standard": 5, "name": "<PERSON><PERSON><PERSON>", "code": "ACC", "required": true, "shown_by_default": true, "description": "The width or diameter of the section in mm (##)", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:15.591Z"}}, {"model": "defects.standardheader", "pk": "54863722-6990-477b-93f8-bc18e3cab428", "fields": {"header": "b1eee34d-767f-48a0-9439-35c82a6052e1", "standard": 1, "name": "Client <PERSON>f", "code": "ABJ", "required": false, "shown_by_default": false, "description": "The asset owner’s or engaging agency's job reference", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:18.028Z"}}, {"model": "defects.standardheader", "pk": "5674462d-9e4a-483b-8fbf-df367b441036", "fields": {"header": "f5c6995b-d7bf-4346-829c-ff8e395e5315", "standard": 5, "name": "Depth at upstream node", "code": "ACH", "required": false, "shown_by_default": false, "description": "Record the depth of the invert of the pipe below cover level at the upstream node in m", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:15.663Z"}}, {"model": "defects.standardheader", "pk": "56cac3ae-881c-4c83-9362-b468f8a27b8c", "fields": {"header": "d9bc4b3a-7802-4401-8ce1-5b1b17e48a85", "standard": 2, "name": "Client Defined 5", "code": "ClientDefined5", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:14.774Z"}}, {"model": "defects.standardheader", "pk": "56f6456c-8d2c-4721-9c22-47d384810ad7", "fields": {"header": "aa664bad-1088-432d-926a-b286c81931a5", "standard": 2, "name": "Start Node Reference", "code": "StartNodeRef", "required": false, "shown_by_default": true, "description": "12 characters", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.881Z"}}, {"model": "defects.standardheader", "pk": "575c629f-189b-47b9-b636-aa543bdeace1", "fields": {"header": "51eee565-03bd-43a5-9c26-ba4b04a97c85", "standard": 5, "name": "Downstream node location", "code": "AAU", "required": true, "shown_by_default": true, "description": "The address of the downstream node", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:15.303Z"}}, {"model": "defects.standardheader", "pk": "578b4436-9c52-44d9-a66d-f2005c22c679", "fields": {"header": "14fe8eaa-060b-42b5-b172-fa938fa2c301", "standard": 1, "name": "Contractor", "code": "ABS", "required": false, "shown_by_default": false, "description": "Record the company or trading name of the entity responsible for undertaking the inspection", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:18.092Z"}}, {"model": "defects.standardheader", "pk": "57db078f-d7ba-418d-8c03-b08737b0843c", "fields": {"header": "586fe0bf-0fe9-4504-a3aa-726ba26e5942", "standard": 4, "name": "Parallel Line", "code": "AAR", "required": true, "shown_by_default": true, "description": "Record the line number where there is more than one direct line between two manholes. The line number is supplied by the asset owner.", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:16.982Z"}}, {"model": "defects.standardheader", "pk": "58b34ad9-0ebd-4a08-a092-f323d6364892", "fields": {"header": "6f399782-06c0-447e-8d7b-46561086d080", "standard": 4, "name": "Depth at downstream node", "code": "ACI", "required": false, "shown_by_default": false, "description": "Record the depth of the invert of the pipe below cover level at the downstream node in m", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.391Z"}}, {"model": "defects.standardheader", "pk": "591888ed-3346-449f-b077-9923b8514298", "fields": {"header": "e26942f7-d18e-4e19-91d7-0be761a9e334", "standard": 3, "name": "Pipe Unit Length", "code": "PipeUnitLength", "required": false, "shown_by_default": false, "description": "Length of the pipe asset", "data_type": "number", "options_selections": [], "options_description": null, "created_at": "2023-11-09T07:01:01.053Z"}}, {"model": "defects.standardheader", "pk": "5bb52f61-15b0-4bc4-92c8-60796b6aa652", "fields": {"header": "2ecd3507-9dc7-47d2-98bc-08c7f8386600", "standard": 6, "name": "District", "code": "AAO", "required": false, "shown_by_default": false, "description": "The name of the district as specified by the asset owner", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:16.252Z"}}, {"model": "defects.standardheader", "pk": "5d8b6b1b-7fcd-4de0-a0a8-6531c00c6a85", "fields": {"header": "e26942f7-d18e-4e19-91d7-0be761a9e334", "standard": 1, "name": "Pipe Unit Length", "code": "PipeUnitLength", "required": false, "shown_by_default": false, "description": "Length of the pipe asset", "data_type": "number", "options_selections": [], "options_description": null, "created_at": "2023-11-09T07:01:00.981Z"}}, {"model": "defects.standardheader", "pk": "5e9ee114-c168-40c5-8d78-3dea4213d57b", "fields": {"header": "b1eee34d-767f-48a0-9439-35c82a6052e1", "standard": 2, "name": "Client's Job reference", "code": "ClientsJobRef", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:14.018Z"}}, {"model": "defects.standardheader", "pk": "5eef92bc-e5ec-4911-8c6b-a7afb858bd53", "fields": {"header": "f702ad48-c9d4-4fdb-9268-5f0edfd334e7", "standard": 3, "name": "Down Northing", "code": "Down_Northing", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:12.883Z"}}, {"model": "defects.standardheader", "pk": "60431628-fff0-470b-976f-88687d745af9", "fields": {"header": "6b52d1b8-8dea-4a8c-8b28-7f1d64a45bc5", "standard": 2, "name": "Name of Surveyor", "code": "NameOfSurveyor", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:14.006Z"}}, {"model": "defects.standardheader", "pk": "604d3950-e988-4a5b-ac43-ecb1d6cab0e8", "fields": {"header": "e16c52ae-56e8-43a9-bda0-f82798293016", "standard": 5, "name": "Time of inspection", "code": "ABG", "required": true, "shown_by_default": true, "description": "The time as specified in ISO 8601 using the 24-hour hh:mm format", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:15.376Z"}}, {"model": "defects.standardheader", "pk": "613fe31f-d8d7-4827-ac0c-4774eed242da", "fields": {"header": "7f9a2ab0-a7ad-4393-83d9-b004680471b4", "standard": 2, "name": "Downstream Node", "code": "DownstreamNode", "required": false, "shown_by_default": false, "description": "Downstream node of the pipe inspected", "data_type": "string", "options_selections": [], "options_description": null, "created_at": "2023-11-09T07:01:00.958Z"}}, {"model": "defects.standardheader", "pk": "619573ad-67a1-444e-b7f3-c61411b2bee4", "fields": {"header": "f0acb5b3-1c68-4282-8f6d-956c0e8b1586", "standard": 3, "name": "Certificate Number", "code": "Certificate_Number", "required": true, "shown_by_default": true, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:12.645Z"}}, {"model": "defects.standardheader", "pk": "619af232-c4d0-490c-8357-0e6d9b71ae24", "fields": {"header": "46c5e176-f46b-49f1-b9ae-31614d5d58ee", "standard": 4, "name": "Cleaning Status", "code": "ACM", "required": true, "shown_by_default": true, "description": "Record whether the pipeline was cleaned prior to the inspection", "data_type": "options", "options_selections": "LC, HC, NC, RC", "options_description": "The pipe was light cleaned prior to the inspection, The pipe was heavy cleaned prior to the inspection, The pipe was not cleaned prior to the inspection, The pipe was root cut prior to inspection", "created_at": "2023-07-05T06:37:17.430Z"}}, {"model": "defects.standardheader", "pk": "62aca2fb-7be4-4ff4-96e3-5a54e2a40b4c", "fields": {"header": "37c7dc06-3c44-40db-bb23-b22833d251df", "standard": 4, "name": "Joint Spacing", "code": "ACG", "required": true, "shown_by_default": true, "description": "Record the length (m) of the individual pipe units that comprise the pipeline. Where the pipe is continuous e.g. masonry, or PE, this field is left blank, and a remark made.", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.364Z"}}, {"model": "defects.standardheader", "pk": "62d01478-a46f-41fe-a3e6-43f76e2b5fb4", "fields": {"header": "8e8a50c9-5c5e-4838-a371-ee51f7532286", "standard": 4, "name": "Camera Setup Location", "code": "AAK", "required": true, "shown_by_default": true, "description": "Record the Setup node in which the camera is starting the inspection", "data_type": "options", "options_selections": "U, D, UD, DU, Q", "options_description": "The camera is starting at the upstream node—travelling in the same direction as the flow, The camera is starting at the downstream node—travelling opposite the direction of flow, Camera travels from both ends Starting at the upstream node then restarting at the downstream node, Camera travels from both ends starting at the downstream node then restarting at the upstream node", "created_at": "2023-07-05T06:37:16.877Z"}}, {"model": "defects.standardheader", "pk": "63c74ce3-45a2-4e12-9b97-87f501324cec", "fields": {"header": "f733f544-b660-4c2f-bdaa-6694f30d1ca1", "standard": 2, "name": "Location (Town or Village)", "code": "LocationTown", "required": true, "shown_by_default": true, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.869Z"}}, {"model": "defects.standardheader", "pk": "643caafd-8199-4c75-a372-0d2bcbf86bad", "fields": {"header": "d3613be4-b984-4e3f-97d3-dc2e6707d5e3", "standard": 1, "name": "Actual inspection length", "code": "ABR", "required": false, "shown_by_default": true, "description": "The actual length of conduit inspected may be significantly different to the anticipated length due to the inspection being abandoned or errors in the recorded information. This field may be automatically populated at the end of the inspection.", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:18.244Z"}}, {"model": "defects.standardheader", "pk": "644577e6-dfe9-45b7-a54c-626f865a3a6d", "fields": {"header": "bb705029-6500-4112-a6bd-8c0c6193ed0b", "standard": 1, "name": "Date of inspection", "code": "ABF", "required": false, "shown_by_default": true, "description": "Record the calendar date of the inspection using the DD-MM-YYYY format, leading zeros shall be included where necessary.", "data_type": "date", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.974Z"}}, {"model": "defects.standardheader", "pk": "675c75c5-e205-41c3-9663-c1d3a43ff4b4", "fields": {"header": "809a12e9-d6d8-41a0-93c3-c060d909c98b", "standard": 6, "name": "Client Defined 1", "code": "AEA", "required": false, "shown_by_default": false, "description": "User defined", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:16.672Z"}}, {"model": "defects.standardheader", "pk": "67de47fa-15dc-4005-91ab-292b7c70cd54", "fields": {"header": "1c029f63-70e4-4511-ad3b-39c787572666", "standard": 1, "name": "Flow control measures", "code": "ADC", "required": false, "shown_by_default": false, "description": "Record the measures taken to deal with the flow at the time of the inspection", "data_type": "options", "options_selections": "B, N, P, Z", "options_description": "Flows have been blocked or diverted upstream, No measures taken, Flows partially blocked or diverted upstream, Other—record further details in remarks", "created_at": "2023-07-05T06:37:18.272Z"}}, {"model": "defects.standardheader", "pk": "6879bb14-23fe-446b-a2cf-00f894c82bbe", "fields": {"header": "28b4efa5-1ec1-4f69-8ea7-f5c655d53ac1", "standard": 1, "name": "Operation of conduit", "code": "ACJ", "required": false, "shown_by_default": false, "description": "Record the operational mode of the conduit", "data_type": "options", "options_selections": "G, O, P", "options_description": "Gravity, Other/n, e.g. duct, Pressure", "created_at": "2023-07-05T06:37:18.203Z"}}, {"model": "defects.standardheader", "pk": "68abd028-fc5f-40ee-8f55-ff1ff2b46efa", "fields": {"header": "667eb269-fbf5-4ac9-ab0a-a2391305a8b9", "standard": 1, "name": "Precipitation", "code": "ADA", "required": false, "shown_by_default": false, "description": "Record the precipitation", "data_type": "options", "options_selections": "N, R, S", "options_description": "No precipitation, Precipitation (rain), Melting snow or ice", "created_at": "2023-07-05T06:37:18.259Z"}}, {"model": "defects.standardheader", "pk": "68b88d6e-7a52-4227-a400-36a46555a512", "fields": {"header": "6054551f-be85-432f-a486-f47b4b30333a", "standard": 1, "name": "Start node coordinates", "code": "AAC", "required": false, "shown_by_default": false, "description": "The grid reference (easting and northing coordinates) of the start node", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.719Z"}}, {"model": "defects.standardheader", "pk": "69469f1a-6c55-40a6-8e2c-1e342a07ecbb", "fields": {"header": "d3613be4-b984-4e3f-97d3-dc2e6707d5e3", "standard": 3, "name": "Length Surveyed", "code": "Length_Surveyed", "required": false, "shown_by_default": true, "description": "", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.325Z"}}, {"model": "defects.standardheader", "pk": "699811b0-651b-412f-8350-752f1dbc4fd2", "fields": {"header": "dc27cce9-929e-4980-891c-7e69e6e230a4", "standard": 4, "name": "Name of Coder", "code": "ABT", "required": true, "shown_by_default": true, "description": "Record the name of the person who encoded the pipe condition.", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.231Z"}}, {"model": "defects.standardheader", "pk": "69dabcb4-306b-4a13-a54a-e2544ab88ab6", "fields": {"header": "fb4fefec-bd51-42ac-ad90-ec5ca2011c3a", "standard": 5, "name": "Total Service Score", "code": "ACU", "required": false, "shown_by_default": false, "description": "Calculated total service score", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:15.834Z"}}, {"model": "defects.standardheader", "pk": "6a16f8d6-31ca-48b2-b2cb-159a0c387f6c", "fields": {"header": "eb5388dd-f2bb-4a8d-9e7a-f8ca3d4ef53c", "standard": 3, "name": "Pipe Use", "code": "Pipe_Use", "required": true, "shown_by_default": true, "description": "", "data_type": "options", "options_selections": "CB, DP, FM, LG, LP, PR, SS, SW, XX, ZZ", "options_description": "Combined pipe, Dam Pipe, Force Main, Levee Gravity Pipe, Levee Pressure Pipe, Process Pipe, Sanitary Sewage, Stormwater, Not Known, Other", "created_at": "2023-07-05T06:37:12.743Z"}}, {"model": "defects.standardheader", "pk": "6b9d888e-eb7c-42b4-8cfc-098dde5767c6", "fields": {"header": "667eb269-fbf5-4ac9-ab0a-a2391305a8b9", "standard": 2, "name": "Weather", "code": "Weather", "required": false, "shown_by_default": false, "description": "", "data_type": "options", "options_selections": "D, R, S", "options_description": "Dry (no rain or snow), Rain, Melting snow or ice", "created_at": "2023-07-05T06:37:14.509Z"}}, {"model": "defects.standardheader", "pk": "6c199910-b70d-4787-948e-e72c8eaba2f9", "fields": {"header": "078cb335-8a88-4c48-9ff9-4455c5fabca6", "standard": 4, "name": "Standard", "code": "ABA", "required": true, "shown_by_default": false, "description": "The version of the standard used to record the data. This shall be in the form NZPIM (Gravity)—4th Edition 2019", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.041Z"}}, {"model": "defects.standardheader", "pk": "6de757b2-14a4-4b8d-af2f-b8d16f2ae60f", "fields": {"header": "28b4efa5-1ec1-4f69-8ea7-f5c655d53ac1", "standard": 4, "name": "Operation of Pipeline", "code": "ACJ", "required": true, "shown_by_default": true, "description": "Record the operational mode of the pipeline", "data_type": "options", "options_selections": "G, P", "options_description": "Gravity (*Default), Pressure", "created_at": "2023-07-05T06:37:17.404Z"}}, {"model": "defects.standardheader", "pk": "6e20a77f-9674-44c2-9f39-f2744a39d5e8", "fields": {"header": "a96767ae-b8e1-46ad-9616-733da4dcf680", "standard": 5, "name": "Upstream node Location", "code": "AAT", "required": true, "shown_by_default": true, "description": "The address of the upstream node", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:15.289Z"}}, {"model": "defects.standardheader", "pk": "6e4db20f-aa4d-429a-a097-6da471a3d697", "fields": {"header": "2ecd3507-9dc7-47d2-98bc-08c7f8386600", "standard": 1, "name": "District", "code": "AAO", "required": false, "shown_by_default": false, "description": "The name of the district as specified by the asset owner", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.929Z"}}, {"model": "defects.standardheader", "pk": "6e5f69d7-6a01-4154-bf5c-e6ca44f46435", "fields": {"header": "1450ed30-5926-4e04-bdbc-1261a9c24efb", "standard": 1, "name": "Client", "code": "AAM", "required": false, "shown_by_default": false, "description": "The name of the asset owner or engaging agency for inspection of the conduit", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.904Z"}}, {"model": "defects.standardheader", "pk": "6e9c679d-4350-4b14-a22e-b757eeff6667", "fields": {"header": "abf85997-2c41-479e-9b37-10f2289dbdd9", "standard": 4, "name": "Up Node Type", "code": "ACP", "required": false, "shown_by_default": false, "description": "Record the type of node at the upstream", "data_type": "options", "options_selections": "SND, SMH, SPS, SIP, SMS, STND, STMH, STI, STO, STCP, STMS", "options_description": "Sewer node – includes: Buried Junctions, material change, bend/deviation, diameter change, Sewer Manhole, Sewer pump station, Sewer Inspection Point, Sewer Miscellaneous, Stormwater node – includes: Buried Junctions, material change, bend/deviation, diameter change, Stormwater manhole, Stormwater Inlet, Stormwater Outlet, Stormwater Catchpit, Stormwater Miscellaneous", "created_at": "2023-07-05T06:37:17.458Z"}}, {"model": "defects.standardheader", "pk": "70029952-e79c-45d5-97f2-d99e1ac1aae5", "fields": {"header": "bb705029-6500-4112-a6bd-8c0c6193ed0b", "standard": 3, "name": "Inspection Date", "code": "Inspection_Date", "required": true, "shown_by_default": true, "description": "YYYYMMDD", "data_type": "date", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:12.658Z"}}, {"model": "defects.standardheader", "pk": "7048f7a4-27b9-435b-bf0b-b522feaea316", "fields": {"header": "5569b600-b02e-4af9-810c-10b83d56b7b5", "standard": 4, "name": "Drawing Number", "code": "AAS", "required": false, "shown_by_default": false, "description": "The drawing reference number on which the pipeline is shown, if applicable", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:16.995Z"}}, {"model": "defects.standardheader", "pk": "70a96764-9bf6-435f-a58a-777e30f378b3", "fields": {"header": "51eee565-03bd-43a5-9c26-ba4b04a97c85", "standard": 4, "name": "Downstream node location", "code": "AAU", "required": true, "shown_by_default": true, "description": "The address of the downstream node", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.027Z"}}, {"model": "defects.standardheader", "pk": "71588fdc-2e29-4d76-aa4c-75647d9ead6b", "fields": {"header": "ff3f87cc-c289-4ae3-bbe2-b57ed1947c54", "standard": 1, "name": "Land ownership", "code": "AAQ", "required": false, "shown_by_default": false, "description": "Record the ownership of the land denoted as:", "data_type": "options", "options_selections": "C, Q, T", "options_description": "Public land (Common or Crown land), Not known (Query), Private land (Torrens Title)", "created_at": "2023-07-05T06:37:17.780Z"}}, {"model": "defects.standardheader", "pk": "73af8f57-dfda-42b3-95f8-a14b4fbaf1ea", "fields": {"header": "60d324a5-ca56-4f53-88d7-fb68b8944dc4", "standard": 1, "name": "Year Constructed", "code": "ACN", "required": false, "shown_by_default": false, "description": "The approximate year the conduit came into operation. Report in YYYY format either as a single year or as a range in YYYY-YYYY format", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.833Z"}}, {"model": "defects.standardheader", "pk": "73b88f7f-a6a2-4bdc-b26b-2b2724859168", "fields": {"header": "6bb0917f-df68-4990-872d-1053718c81c3", "standard": 3, "name": "PO Number", "code": "PO_Number", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.032Z"}}, {"model": "defects.standardheader", "pk": "73fae976-ec77-46cc-b29d-90da4d8c2c55", "fields": {"header": "59cd0410-9127-47ea-bc03-f1e846468f3c", "standard": 6, "name": "Direction of inspection", "code": "AAK", "required": false, "shown_by_default": true, "description": "Record the direction in which the camera is travelling to record images", "data_type": "options", "options_selections": "D, Q, U", "options_description": "The camera is travelling downstream—the same direction as the normal direction of flow, The direction of flow is not known (Query), The camera is travelling upstream—directly opposite the normal direction of flow", "created_at": "2023-07-05T06:37:16.340Z"}}, {"model": "defects.standardheader", "pk": "74a7ffb3-2081-4f62-be35-3cc3ee37b3aa", "fields": {"header": "8f2490a7-3089-4fef-8d24-ad38e24258f9", "standard": 3, "name": "Custom Field Ten", "code": "Custom_Field_ten", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.634Z"}}, {"model": "defects.standardheader", "pk": "74b6c326-93e2-407d-8c56-f35c5b9d897b", "fields": {"header": "e26942f7-d18e-4e19-91d7-0be761a9e334", "standard": 4, "name": "Pipe Unit Length", "code": "PipeUnitLength", "required": false, "shown_by_default": false, "description": "Length of the pipe asset", "data_type": "number", "options_selections": [], "options_description": null, "created_at": "2023-11-09T07:01:01.000Z"}}, {"model": "defects.standardheader", "pk": "751d24ee-1c0c-4bdc-b6b8-e6ec245f656f", "fields": {"header": "13da5c27-84bf-45ce-95f2-418b74796831", "standard": 5, "name": "General comment", "code": "ADE", "required": true, "shown_by_default": true, "description": "Record any information that cannot be included in any other way", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:15.972Z"}}, {"model": "defects.standardheader", "pk": "7649c652-f200-4f37-8786-726d4b2ee4cc", "fields": {"header": "8e8a50c9-5c5e-4838-a371-ee51f7532286", "standard": 5, "name": "Camera Setup Location", "code": "AAK", "required": true, "shown_by_default": true, "description": "Record the Setup node in which the camera is starting the inspection", "data_type": "options", "options_selections": "U, D, UD, DU, Q", "options_description": "The camera is starting at the upstream node—travelling in the same direction as the flow, The camera is starting at the downstream node—travelling opposite the direction of flow, Camera travels from both ends Starting at the upstream node then restarting at the downstream node, Camera travels from both ends starting at the downstream node then restarting at the upstream node", "created_at": "2023-07-05T06:37:15.147Z"}}, {"model": "defects.standardheader", "pk": "7796b1c7-367d-4421-af0d-8bfde6c523c0", "fields": {"header": "2ecd3507-9dc7-47d2-98bc-08c7f8386600", "standard": 5, "name": "District/Catchment", "code": "AAO", "required": false, "shown_by_default": false, "description": "The name of the district or catchment as specified by the asset owner", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:15.220Z"}}, {"model": "defects.standardheader", "pk": "77ffe564-759b-4549-9c33-b35a3bbe5c31", "fields": {"header": "9e1d89cb-dbef-4c26-83eb-ef3d21c916f5", "standard": 6, "name": "Name of conduit system", "code": "AAP", "required": false, "shown_by_default": false, "description": "The name of the conduit system, or a conduit system reference as specified by the asset owner", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:16.067Z"}}, {"model": "defects.standardheader", "pk": "783d3467-2fb2-44fd-9016-15bc8e905908", "fields": {"header": "b113ef34-c622-4478-b0df-2eb00f22d1c2", "standard": 2, "name": "Node 2 Grid Ref X", "code": "Node2GridRefX", "required": false, "shown_by_default": false, "description": "0 to 999999", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:14.217Z"}}, {"model": "defects.standardheader", "pk": "789114fa-341f-4096-bc6a-ba58c44f10da", "fields": {"header": "eb5388dd-f2bb-4a8d-9e7a-f8ca3d4ef53c", "standard": 1, "name": "Use of conduit", "code": "ACK", "required": false, "shown_by_default": true, "description": "Record the use of the conduit system", "data_type": "options", "options_selections": "COM, CUL, D, S, TW, Z", "options_description": "Combined system (Sewage and surface water combined), Culverted watercourse, e.g. a short buried section for a road crossing or similar, A drain designed to carry only surface water, The installation is designed to carry only sewage, Trade effluent sewer (Trade waste), Other—further information shall be included as a general header remark (code ADE)immediately following", "created_at": "2023-07-05T06:37:18.216Z"}}, {"model": "defects.standardheader", "pk": "78ba6709-a315-4b0a-bf66-23550184f35c", "fields": {"header": "5569781a-faf2-4c54-bd81-d2dd65257b14", "standard": 3, "name": "<PERSON><PERSON><PERSON>", "code": "<PERSON><PERSON><PERSON>", "required": false, "shown_by_default": false, "description": "", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:12.774Z"}}, {"model": "defects.standardheader", "pk": "78bfd569-6d30-4266-bed7-ba32a440bae7", "fields": {"header": "e59e14f0-5bbe-4455-af91-734134102fa7", "standard": 3, "name": "Date Cleaned", "code": "Date_Cleaned", "required": false, "shown_by_default": false, "description": "YYYYMMDD", "data_type": "date", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.139Z"}}, {"model": "defects.standardheader", "pk": "78dbe491-d2aa-46f4-8172-f23cdabcb199", "fields": {"header": "bc88fe45-f754-4332-a085-22f97b600ff4", "standard": 2, "name": "Location Type Code", "code": "LocationTypeCode", "required": false, "shown_by_default": false, "description": "", "data_type": "options", "options_selections": "FLD, PR, GDN, BLG, WLD, DIF, WWY, Z, XS1, XS2, XS3, XS4, XS5, XS6, XS7, XS8, XS9, XS0, XSE, XSQ, XSR, XST, XSW, XSY", "options_description": "Road, Footway, Verge, Other pedestrian area, Fields, Property with buidings, Gardens, Under a permanent building, Woodland, Difficult access, Under a waterway, Other, Roads left outside verge, Roads left footway, Roads left verge / Motorways verge, Roads lane 1 / Motorways hard shoulder, Roads lane 2 / Motorways slow lane, Roads lane 3 / Motorways central lane, Roads lane 4 / Motorways fast lane, Roads or Motorways right verge, Roads right footway, Roads right outside verge, Roads right turning lane, Roads or Motorway slip road, Roads bus lane, Roads or motorways crawler lane, Roads left turning lane, Roads or Motorways other", "created_at": "2023-07-05T06:37:14.091Z"}}, {"model": "defects.standardheader", "pk": "792a48c6-69b8-40c7-a9c8-8d98e34fa26c", "fields": {"header": "e16c52ae-56e8-43a9-bda0-f82798293016", "standard": 2, "name": "Time", "code": "Time", "required": false, "shown_by_default": true, "description": "HH:MM in 24 hour format", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:14.078Z"}}, {"model": "defects.standardheader", "pk": "7b345239-4e88-43ef-ab8d-c943fcc1db6b", "fields": {"header": "1f912bf8-8afc-4f0d-886c-2dfe8ad4f8db", "standard": 2, "name": "Lining Material", "code": "LiningMaterial", "required": false, "shown_by_default": false, "description": "", "data_type": "options", "options_selections": "AC, BL, BR, CI, CL, CO, CS, DI, EP, FC, FRP, GI, MAC, MAR, MX, PVC, PE, PF, PP, PS, RC, SPC, ST, VC, X, XI, XP, Z", "options_description": "Asbestos cement, Bitumen (lining), Brick, Cast iron, Cement mortar (lining), Concrete, Concrete segments, Ductile iron, Epoxy, Fibre cement, Fibre reinforced plastics, Grey cast iron, Masonry – in regular courses, Masonry – randomly coursed, Mixed material construction, Polyvinyl chloride, Polyethylene, Pitch fibre, Polypropylene, Polyester, Reinforced concrete, Sprayed concrete, Steel, Vitrified clay (i.e. all clayware), Unidentified material, Unidentified type of iron or steel, Unidentified type of plastics, Other", "created_at": "2023-07-05T06:37:14.402Z"}}, {"model": "defects.standardheader", "pk": "7b3c4550-e2d0-40cb-8e77-9696752cc108", "fields": {"header": "2316063f-9608-47df-abba-66ae8bcf8fa7", "standard": 4, "name": "Service Peak Score", "code": "ACV", "required": false, "shown_by_default": false, "description": "Calculated peak service score", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.545Z"}}, {"model": "defects.standardheader", "pk": "7c16722c-f0b1-4f26-a8ff-3e3160d751bd", "fields": {"header": "3b2ead02-7e71-42a8-af25-693e70d9ba04", "standard": 5, "name": "Temperature", "code": "ADB", "required": false, "shown_by_default": false, "description": "Record the ambient temperature coded", "data_type": "options", "options_selections": "C, W", "options_description": "Below freezing (Cold), Above freezing (Warm)", "created_at": "2023-07-05T06:37:15.931Z"}}, {"model": "defects.standardheader", "pk": "7c1e5417-5baa-4553-80d3-af8d7ae9ace4", "fields": {"header": "ba61f12e-367a-4161-b3a3-59f353bd2902", "standard": 2, "name": "Client Defined 2", "code": "ClientDefined2", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:14.721Z"}}, {"model": "defects.standardheader", "pk": "7e7c2f45-5d65-45ff-b79d-f7b7f2f683f0", "fields": {"header": "1e2775a9-8bc7-45e1-b0c0-0eaf835e920d", "standard": 2, "name": "Contractor's Job reference", "code": "ContractorsJobRef", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:14.031Z"}}, {"model": "defects.standardheader", "pk": "7f0c02c0-1fa3-41fc-aac7-b6dcfdbd4f8a", "fields": {"header": "1c029f63-70e4-4511-ad3b-39c787572666", "standard": 5, "name": "Flow control measures", "code": "ADC", "required": false, "shown_by_default": false, "description": "Record the measures taken to deal with the flow at the time of the inspection", "data_type": "options", "options_selections": "B, N, P, Z", "options_description": "Flows have been blocked or diverted upstream, No measures taken, Flows partially blocked or diverted upstream, Other—record further details in remarks", "created_at": "2023-07-05T06:37:15.944Z"}}, {"model": "defects.standardheader", "pk": "80a73b0e-4979-4ea9-9c2f-3ec550171063", "fields": {"header": "e26942f7-d18e-4e19-91d7-0be761a9e334", "standard": 2, "name": "Pipe Unit Length", "code": "PipeUnitLength", "required": false, "shown_by_default": false, "description": "0.0 to 9.9 - Note: for continuous pipes, e.g. brick culvert, field not used.", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:14.534Z"}}, {"model": "defects.standardheader", "pk": "80c3dde6-8ea2-4628-9a6b-cd4b782b74bd", "fields": {"header": "5138310c-4bc3-4a84-822c-f10235c971de", "standard": 2, "name": "Start Node Grid Ref <PERSON>", "code": "StartNodeGridRefY", "required": false, "shown_by_default": false, "description": "0 to 999999", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:14.137Z"}}, {"model": "defects.standardheader", "pk": "81a24db2-7d29-40e6-9f6b-4a94a4721750", "fields": {"header": "b1eee34d-767f-48a0-9439-35c82a6052e1", "standard": 4, "name": "Asset owner’s Reference", "code": "ABJ", "required": false, "shown_by_default": false, "description": "The reference code or name for the inspection supplied by the asset owner", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.135Z"}}, {"model": "defects.standardheader", "pk": "81bf47a8-34e4-406f-80c6-be5c433f6ade", "fields": {"header": "b49cc030-318e-4e0a-b207-ad9c029eef2f", "standard": 3, "name": "Project", "code": "Project", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.080Z"}}, {"model": "defects.standardheader", "pk": "832b2e42-0ae9-437e-8aa3-d23c87187ded", "fields": {"header": "6a0a2b04-9d45-4198-a02c-bd3923f9eb2f", "standard": 4, "name": "Tidal influence", "code": "ADD", "required": false, "shown_by_default": false, "description": "Record tidal influence", "data_type": "options", "options_selections": "A, B", "options_description": "At or above high tide level, Below high tide level", "created_at": "2023-07-05T06:37:17.639Z"}}, {"model": "defects.standardheader", "pk": "832c4c82-66ba-45c9-afb0-2f5184a12ead", "fields": {"header": "c04fc37d-fa16-48dc-b1f4-a1f2cea3ec72", "standard": 3, "name": "Custom Field thirteen", "code": "Custom_Field_thirteen", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.675Z"}}, {"model": "defects.standardheader", "pk": "837ee42d-0819-41ef-95b6-4115e954fb9d", "fields": {"header": "e0f4668f-7861-4e7e-9fd5-742b59fbb450", "standard": 3, "name": "Reviewer Certificate Number", "code": "Reviewer_Certificate_Number", "required": false, "shown_by_default": true, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:12.987Z"}}, {"model": "defects.standardheader", "pk": "8538f9d2-2cab-4670-a045-0fb1966765a0", "fields": {"header": "60d324a5-ca56-4f53-88d7-fb68b8944dc4", "standard": 6, "name": "Year Constructed", "code": "ACN", "required": false, "shown_by_default": false, "description": "The approximate year the conduit came into operation. Report in YYYY format either as a single year or as a range in YYYY-YYYY format", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:16.141Z"}}, {"model": "defects.standardheader", "pk": "85b1b58e-a33e-466d-89d4-a378dc9a1cf2", "fields": {"header": "6b52d1b8-8dea-4a8c-8b28-7f1d64a45bc5", "standard": 4, "name": "Name of Operator", "code": "ABH", "required": true, "shown_by_default": true, "description": "Record the name of the inspection equipment operator.", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.109Z"}}, {"model": "defects.standardheader", "pk": "85e48929-b70f-4178-9fa0-d7ca8cb5c5bd", "fields": {"header": "d0ec0576-dd2f-44ce-bca7-9eca42b2c8f4", "standard": 2, "name": "Video Image Location System", "code": "VideoImageLocationSystem", "required": false, "shown_by_default": false, "description": "", "data_type": "options", "options_selections": "A, B, X", "options_description": "", "created_at": "2023-07-05T06:37:14.590Z"}}, {"model": "defects.standardheader", "pk": "86877ec6-a84d-40f4-b9ef-4fc7b7f959c5", "fields": {"header": "fb6f7061-3956-427e-8026-f8936d37454f", "standard": 3, "name": "Drainage Area", "code": "Drainage_Area", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.228Z"}}, {"model": "defects.standardheader", "pk": "875256c5-8506-4d36-a33d-a6483f0a85ec", "fields": {"header": "7f9a2ab0-a7ad-4393-83d9-b004680471b4", "standard": 6, "name": "ID Downstream", "code": "AAF", "required": false, "shown_by_default": true, "description": "The node reference of the downstream node", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:16.184Z"}}, {"model": "defects.standardheader", "pk": "8a0e595b-534b-4db9-953d-ddf29d562154", "fields": {"header": "aeb13168-2b5a-4a21-828f-c2c14479b08b", "standard": 4, "name": "Method of inspection", "code": "ABE", "required": false, "shown_by_default": false, "description": "Record the method used to inspect the pipeline", "data_type": "options", "options_selections": "FZ, LP, M, PS, S, SS, TVPT, TVFA", "options_description": "Inspection by means of a fixed position zoom pipeline camera, Inspection by means of a remotely controlled laser profiler passed through the conduit, Direct inspection of a conduit by a person walking through the conduit (Manned), Inspection by means of a remotely controlled 3D optical pipeline scanner passed through the conduit, Inspection from the access structure only, Inspection by means of a remotely controlled sonar scanner passed through the conduit, Inspection by means of Pan-Tilt CCTV camera passed through the conduit, Inspection by means of Fixed Axial CCTV camera passed through the conduit", "created_at": "2023-07-05T06:37:17.067Z"}}, {"model": "defects.standardheader", "pk": "8a7fd4ad-6f51-4020-b9d5-c75811312afb", "fields": {"header": "569c7e4a-dad0-4ba2-8f7e-5d7a5b29f367", "standard": 2, "name": "Client Defined 7", "code": "ClientDefined7", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:14.809Z"}}, {"model": "defects.standardheader", "pk": "8ac0d294-a904-4be3-b134-cbb1679bf570", "fields": {"header": "28b4efa5-1ec1-4f69-8ea7-f5c655d53ac1", "standard": 2, "name": "Type of Drain/Sewer", "code": "TypeOfDrainSewer", "required": false, "shown_by_default": false, "description": "", "data_type": "options", "options_selections": "A, B, V", "options_description": "Gravity drain/sewer, Rising main, Vaccum", "created_at": "2023-07-05T06:37:14.329Z"}}, {"model": "defects.standardheader", "pk": "8e68b321-8d3f-4b22-aa3b-33a76f8c38bc", "fields": {"header": "ba61f12e-367a-4161-b3a3-59f353bd2902", "standard": 6, "name": "Client Defined 2", "code": "AEB", "required": false, "shown_by_default": false, "description": "User defined", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:16.688Z"}}, {"model": "defects.standardheader", "pk": "8f3cdf50-573f-46ba-bdf8-3eda6c10bd10", "fields": {"header": "bc88fe45-f754-4332-a085-22f97b600ff4", "standard": 1, "name": "Location type", "code": "AAL", "required": false, "shown_by_default": false, "description": "Record the most predominant and/or critical type of location of the conduit", "data_type": "string", "options_selections": "", "options_description": "Bushland/parkland, Under a permanent building (Built over), Under a waterway (Creek) or body of water, On property with buildings (Developed), Easement, In a footway beside road, Gardens, In other pedestrian area (Mall), Motorway or operational railway, In verge beside a road (Nature Strip), In a field (Paddock), In a road carriageway, Road to easement, Water foreshore, Other—further details shall be stated in remarks", "created_at": "2023-07-05T06:37:17.754Z"}}, {"model": "defects.standardheader", "pk": "90253018-dde4-4bbd-914f-af47d9acbc83", "fields": {"header": "96d1a467-9c00-4902-8d67-b8e6bbb89c14", "standard": 4, "name": "Storage medium for video", "code": "ABK", "required": false, "shown_by_default": false, "description": "Record the type of media used for storing moving images", "data_type": "options", "options_selections": "CD, DVD, PHD, USB, Z", "options_description": "Video CD. Details of format shall be recorded in remark, Digital versatile disc. Details of format shall be recorded in remark, Portable Hard Drive. Details of format shall be recorded in remark, Universal Serial Bus. Details of format shall be recorded in remark, Other—full details shall be recorded in a general header comment (code ADE) immediately following", "created_at": "2023-07-05T06:37:17.149Z"}}, {"model": "defects.standardheader", "pk": "910509ae-3b84-4e5b-81cf-9a967ec5ba7b", "fields": {"header": "60d324a5-ca56-4f53-88d7-fb68b8944dc4", "standard": 3, "name": "Year Constructed", "code": "Year_Constructed", "required": false, "shown_by_default": false, "description": "YYYY", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.339Z"}}, {"model": "defects.standardheader", "pk": "91074fff-798a-4f01-9c2c-9877ee2f80d3", "fields": {"header": "8eb92eff-07b2-41d4-83c7-88630758ea66", "standard": 6, "name": "Depth at downstream node", "code": "ACI", "required": false, "shown_by_default": false, "description": "Record the depth of the invert of the conduit below cover level at the downstream node in m", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:16.113Z"}}, {"model": "defects.standardheader", "pk": "9120f474-a4bb-4f9e-8fc2-26c2684de534", "fields": {"header": "68b9f14e-5952-49ab-ac91-007e467b2787", "standard": 2, "name": "Material", "code": "Material", "required": false, "shown_by_default": true, "description": "", "data_type": "options", "options_selections": "AC, BL, BR, CI, CL, CO, CS, DI, EP, FC, FRP, GI, MAC, MAR, MX, PVC, PE, PF, PP, PS, RC, SPC, ST, VC, X, XI, XP, Z", "options_description": "Asbestos cement, Bitumen (lining), Brick, Cast iron, Cement mortar (lining), Concrete, Concrete segments, Ductile iron, Epoxy, Fibre cement, Fibre reinforced plastics, Grey cast iron, Masonry – in regular courses, Masonry – randomly coursed, Mixed material construction, Polyvinyl chloride, Polyethylene, Pitch fibre, Polypropylene, Polyester, Reinforced concrete, Sprayed concrete, Steel, Vitrified clay (i.e. all clayware), Unidentified material, Unidentified type of iron or steel, Unidentified type of plastics, Other", "created_at": "2023-07-05T06:37:14.388Z"}}, {"model": "defects.standardheader", "pk": "91460dc6-eb1a-4e33-b377-424968a28985", "fields": {"header": "bb705029-6500-4112-a6bd-8c0c6193ed0b", "standard": 5, "name": "Date of inspection", "code": "ABF", "required": true, "shown_by_default": true, "description": "Record the short date of the inspection using the DD/MM/YYYY format", "data_type": "date", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:15.359Z"}}, {"model": "defects.standardheader", "pk": "91bc08eb-6f86-4acc-b68d-4c7503584b90", "fields": {"header": "a80da5e4-5fe0-4e2f-84a3-eabe24234912", "standard": 3, "name": "Custom Field eighteen", "code": "Custom_Field_eighteen", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.750Z"}}, {"model": "defects.standardheader", "pk": "91fec177-4f59-4eb8-ae20-cb04406da357", "fields": {"header": "ba61f12e-367a-4161-b3a3-59f353bd2902", "standard": 1, "name": "Client Defined 2", "code": "AEB", "required": false, "shown_by_default": false, "description": "User defined", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:18.299Z"}}, {"model": "defects.standardheader", "pk": "92afd019-6be0-4f74-bac3-0c41c9d89964", "fields": {"header": "f7e2642d-09a6-48fb-a2b6-7e9c12382b4f", "standard": 2, "name": "Pipeline Length Reference", "code": "PipelineLengthRef", "required": true, "shown_by_default": true, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.826Z"}}, {"model": "defects.standardheader", "pk": "92b233ea-7051-42c0-84fa-b628ac6cc361", "fields": {"header": "667eb269-fbf5-4ac9-ab0a-a2391305a8b9", "standard": 3, "name": "Weather", "code": "Weather", "required": false, "shown_by_default": false, "description": "", "data_type": "options", "options_selections": "1, 2, 3, 4, 5", "options_description": "Dry - No precipitation during survey, Heavy Rain, Light Rain, Snow, Dry Weather/Wet Ground", "created_at": "2023-07-05T06:37:13.126Z"}}, {"model": "defects.standardheader", "pk": "92ee8c4d-be49-4064-a792-ec1d4f0526f5", "fields": {"header": "cee6ece4-7cf2-484e-a4e7-aa70231e2599", "standard": 4, "name": "Up node coordinate", "code": "AAE", "required": false, "shown_by_default": false, "description": "The grid reference (coordinates) of the upstream node", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:16.817Z"}}, {"model": "defects.standardheader", "pk": "930421e1-5b9c-4242-a010-b3e1937b5f52", "fields": {"header": "8fec4535-12bb-497f-92ba-a2301c52a262", "standard": 3, "name": "Pressure Value", "code": "Pressure_Value", "required": false, "shown_by_default": false, "description": "", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.214Z"}}, {"model": "defects.standardheader", "pk": "9392a103-070b-47d2-9623-ed609d13f588", "fields": {"header": "f9879149-82cc-471d-b3a0-68eb0e83bd4a", "standard": 2, "name": "Client Defined 14", "code": "ClientDefined14", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:14.926Z"}}, {"model": "defects.standardheader", "pk": "93b2ca06-3032-43d7-9c86-33480be045c8", "fields": {"header": "db309101-854a-4b34-aeb1-bff23f62fc31", "standard": 3, "name": "Inspection Status", "code": "Inspection_Status", "required": true, "shown_by_default": true, "description": "", "data_type": "options", "options_selections": "CI, BM, NA, NE, NF, NI, NO, SD", "options_description": "Complete Inspection, B<PERSON>ed and Marked, No Access, Does Not Exist, Not Found, Traffic, Not Opened, Surcharged/Debris", "created_at": "2023-07-05T06:37:12.700Z"}}, {"model": "defects.standardheader", "pk": "9465eaa2-a58f-4a81-a8a8-be1391786b3b", "fields": {"header": "1b2573b0-df4a-4f9b-937f-b7394af581da", "standard": 6, "name": "Purpose of inspection", "code": "ABP", "required": false, "shown_by_default": false, "description": "Record the purpose of the inspection", "data_type": "options", "options_selections": "B, C, IE, IP, L, NC, OE, R, RC, S, SE, T, W, Z", "options_description": "Inspection before and after building works over or near the conduit, Completion of an earlier abandoned inspection, Suspected infiltration problem (Infiltration exam), Investment planning, Locating a conduit, connection or a maintenance structure, Final control of new construction, Suspected operational problem (Operational exam), Routine inspection of condition, Final control of renovation or repair (Renovation/repair control), Sample inspection, Suspected structural problem (Structural exam), Transfer of ownership, End of warranty period, Other—the reason shall be recorded as a header remark (code ADE) immediately following", "created_at": "2023-07-05T06:37:16.420Z"}}, {"model": "defects.standardheader", "pk": "94d84b93-16d4-4c1b-a374-0f02eeb23637", "fields": {"header": "eb52f9d9-e499-4ac6-8b4a-63988ab64f87", "standard": 2, "name": "Client Defined 20", "code": "ClientDefined20", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:15.016Z"}}, {"model": "defects.standardheader", "pk": "9560393c-d3eb-456f-a65f-80dbfc8bcbb5", "fields": {"header": "6054551f-be85-432f-a486-f47b4b30333a", "standard": 6, "name": "Start node coordinates", "code": "AAC", "required": false, "shown_by_default": false, "description": "The grid reference (easting and northing coordinates) of the start node", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:16.027Z"}}, {"model": "defects.standardheader", "pk": "9568d03e-31d6-4403-a67c-6127f7cb98f0", "fields": {"header": "667eb269-fbf5-4ac9-ab0a-a2391305a8b9", "standard": 6, "name": "Precipitation", "code": "ADA", "required": false, "shown_by_default": false, "description": "Record the precipitation", "data_type": "options", "options_selections": "N, R, S", "options_description": "No precipitation, Precipitation (rain), Melting snow or ice", "created_at": "2023-07-05T06:37:16.637Z"}}, {"model": "defects.standardheader", "pk": "9774eaa4-8382-4bfe-84ab-5bf59a1d6bb5", "fields": {"header": "d3613be4-b984-4e3f-97d3-dc2e6707d5e3", "standard": 4, "name": "Survey Length", "code": "ABR", "required": true, "shown_by_default": true, "description": "Record the measured Length is the length of pipe that has been surveyed (#.#).", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.201Z"}}, {"model": "defects.standardheader", "pk": "983ab1ae-fced-470f-8f7e-b6698b7e9485", "fields": {"header": "3e140d9a-b8e1-4f95-9c6b-ee252407af43", "standard": 6, "name": "Depth at upstream node", "code": "ACH", "required": false, "shown_by_default": false, "description": "Record the depth of the invert of the conduit below cover level at the upstream node in m", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:16.550Z"}}, {"model": "defects.standardheader", "pk": "98653f4e-2b4e-4cb7-885f-363c137e2d52", "fields": {"header": "0ddabd9a-a8a7-4e16-80c0-dd2b824437d4", "standard": 6, "name": "GIS Length", "code": "ABQ", "required": false, "shown_by_default": false, "description": "The anticipated length of the inspection as advised by the asset owner.", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:16.096Z"}}, {"model": "defects.standardheader", "pk": "98a565b8-0be3-46ff-b914-8f870356ace2", "fields": {"header": "9e1d89cb-dbef-4c26-83eb-ef3d21c916f5", "standard": 4, "name": "Name of pipe system", "code": "AAP", "required": false, "shown_by_default": false, "description": "The name of the pipe system, or a pipe system reference as specified by the asset owner", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:16.952Z"}}, {"model": "defects.standardheader", "pk": "99117d81-c002-42c1-825b-faa81babc1b8", "fields": {"header": "9fb2a9d5-ca89-4f05-92b3-e16341455e66", "standard": 2, "name": "Video Image Filename", "code": "VideoImageFilename", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:14.631Z"}}, {"model": "defects.standardheader", "pk": "9c418313-1c07-47b1-86f2-f090c2114e67", "fields": {"header": "1b2573b0-df4a-4f9b-937f-b7394af581da", "standard": 5, "name": "Purpose of inspection", "code": "ABP", "required": true, "shown_by_default": true, "description": "Record the purpose of the inspection", "data_type": "options", "options_selections": "C, IE, IP, L, NC, OE, R, RC, S, SE, T, W, Z", "options_description": "Completion of an earlier abandoned inspection, Suspected infiltration problem (Infiltration exam), Investment planning, Locating a pipe, connection or a manhole structure, Final inspection of a new construction, Suspected operational problem (Operational exam), Routine inspection of condition, Final inspection of renovation or repair (Renovation/repair control), Sample inspection, Suspected structural problem (Structural exam), Transfer of ownership, End of warranty period, Other—the reason shall be recorded as a header remark (code ADE) immediately following", "created_at": "2023-07-05T06:37:15.462Z"}}, {"model": "defects.standardheader", "pk": "9e52be46-c370-4b6f-9b03-d9b7526cdd1b", "fields": {"header": "a9d55a06-9c9c-48af-8b16-2fedcd095f2f", "standard": 3, "name": "Reviewed By", "code": "Reviewed_By", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:12.973Z"}}, {"model": "defects.standardheader", "pk": "9e5a8786-79b4-4f72-a9bf-1e47ad08cc48", "fields": {"header": "69db29d1-c1a1-4852-96b6-2fe06573d25b", "standard": 2, "name": "Client Defined 11", "code": "ClientDefined11", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:14.875Z"}}, {"model": "defects.standardheader", "pk": "9efee4a3-6ca2-406c-b2be-8ef8cb515cdb", "fields": {"header": "96d1a467-9c00-4902-8d67-b8e6bbb89c14", "standard": 5, "name": "Storage medium for video", "code": "ABK", "required": false, "shown_by_default": false, "description": "Record the type of media used for storing moving images", "data_type": "options", "options_selections": "CD, DVD, PHD, USB, Z", "options_description": "Video CD. Details of format shall be recorded in remark, Digital versatile disc. Details of format shall be recorded in remark, Portable Hard Drive. Details of format shall be recorded in remark, Universal Serial Bus. Details of format shall be recorded in remark, Other—full details shall be recorded in a general header comment (code ADE) immediately following", "created_at": "2023-07-05T06:37:15.432Z"}}, {"model": "defects.standardheader", "pk": "a034483d-4980-4982-9019-fa3198743c92", "fields": {"header": "832f823f-5f0e-41bd-91a4-93be65552c3b", "standard": 2, "name": "Client Defined 3", "code": "ClientDefined3", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:14.736Z"}}, {"model": "defects.standardheader", "pk": "a130ebe4-69bb-4a1c-b17b-29b20a7be5b0", "fields": {"header": "6e0b99fa-193a-417a-98a5-d350ed545125", "standard": 2, "name": "Photographic Volume Reference", "code": "PhotographicVolume", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:14.674Z"}}, {"model": "defects.standardheader", "pk": "a17b61ad-aff1-40d3-90c4-78a2908f16ca", "fields": {"header": "7fefc6c5-d21b-476c-a085-bf694d4b4500", "standard": 2, "name": "Node 3 Reference", "code": "Node3Ref", "required": false, "shown_by_default": false, "description": "12 characters", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.930Z"}}, {"model": "defects.standardheader", "pk": "a3473c44-7f77-4925-b28e-136700b333f5", "fields": {"header": "78fe4754-7bf7-4c57-832b-aeb6d1afce89", "standard": 5, "name": "Total Structural Score", "code": "ACR", "required": false, "shown_by_default": false, "description": "Calculated total structural score", "data_type": "options", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:15.787Z"}}, {"model": "defects.standardheader", "pk": "a3f6619d-0d98-427a-95f4-9d8121ce731d", "fields": {"header": "4ef9b123-9582-4c23-9eb1-967dda2bc501", "standard": 5, "name": "Lining type", "code": "ACE", "required": false, "shown_by_default": false, "description": "Where a pipe has been lined, record the method of lining", "data_type": "options", "options_selections": "CFL, CIP, LCP, LDP, MFL, SEG, SPL, SWL, Z", "options_description": "Close fit lining, Cured in place lining, Lining with a continuous conduit (pipeline) e.g. a pipe string welded on the surface prior to insertion, Lining with discrete pipes i.e. short pipes jointed underground, Lining inserted during manufacture (Manufacturer’s lining), Segmental lining, Sprayed lining, Spirally wound lining, Other", "created_at": "2023-07-05T06:37:15.619Z"}}, {"model": "defects.standardheader", "pk": "a4200887-7aa7-4453-8e1c-bc90ce344976", "fields": {"header": "1e2775a9-8bc7-45e1-b0c0-0eaf835e920d", "standard": 6, "name": "Contractor <PERSON>", "code": "ABI", "required": false, "shown_by_default": false, "description": "The inspection company's or operator’s job reference ", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:16.354Z"}}, {"model": "defects.standardheader", "pk": "a497cc73-8ed1-4a68-ae83-a4a73accbc86", "fields": {"header": "8a48b1c0-088f-4db8-824f-f5eb742f44e3", "standard": 2, "name": "Video Image Format", "code": "VideoImageFormat", "required": false, "shown_by_default": false, "description": "", "data_type": "options", "options_selections": "F, MPEG1, MPEG2, MPEG4, AVI, VOB, DIVX, Z", "options_description": "Fixed to media type, MPEG1, MPEG2, MPEG4, AVI, VOB, DIVX, Other", "created_at": "2023-07-05T06:37:14.618Z"}}, {"model": "defects.standardheader", "pk": "a6e90d4d-ce4c-41ad-9790-d1cc68294c9a", "fields": {"header": "68b9f14e-5952-49ab-ac91-007e467b2787", "standard": 5, "name": "Material", "code": "ACD", "required": false, "shown_by_default": true, "description": "The material of the fabric of the pipe. Where the pipe has been lined the Material field shall be left blank.", "data_type": "options", "options_selections": "RC, UR, PE, PP, PVC, FPVC, GRP, FRP, AC, PFPF, GEW, EW, VC, CORS, DI, ST, CLS, CI, BK, CIS, ZC, ZS, ZP, ZZ", "options_description": "Steel Reinforced Concrete, Non-reinforced Concrete, Polyethylene, Polypropylene (including Profiled Wall Pipes), Polyvinyl Chloride, Fusible Polyvinyl Chloride, Glass Reinforced Plastic, Fibre Reinforced Plastic, Asbestos Cement, Pitch Fibre, Glazed Earthenware, Earthenware (unglazed), Vitrified Clay, Corrugated Steel Pipe, Ductile Iron, Steel (unlined), Cement lined Steel, Cast Iron, Brick and other masonry materials, Cast In-Situ concrete or Mortar, Unidentified type of concrete or cement mortar, Unidentified type of iron or steel, Unidentified type of plastics, Unidentified material", "created_at": "2023-07-05T06:37:15.606Z"}}, {"model": "defects.standardheader", "pk": "a72f8966-c2c8-436e-af2c-35431e5fb44a", "fields": {"header": "f9879149-82cc-471d-b3a0-68eb0e83bd4a", "standard": 3, "name": "Custom Field fourteen", "code": "Custom_Field_fourteen", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.690Z"}}, {"model": "defects.standardheader", "pk": "a89bbd48-9ae5-4db2-a474-ffe725b2a26b", "fields": {"header": "13da5c27-84bf-45ce-95f2-418b74796831", "standard": 6, "name": "General Remarks", "code": "ADE", "required": false, "shown_by_default": true, "description": "Record any information that cannot be included in any other way including errors in asset information, defects in the maintenance structures etc.", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:16.743Z"}}, {"model": "defects.standardheader", "pk": "a8f795f9-f412-4e42-9313-82ba84a1f0ec", "fields": {"header": "7f9a2ab0-a7ad-4393-83d9-b004680471b4", "standard": 3, "name": "Downstream MH", "code": "Downstream_MH", "required": true, "shown_by_default": true, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:12.870Z"}}, {"model": "defects.standardheader", "pk": "a9e3bfee-3a1f-4bd9-8062-6930be96a278", "fields": {"header": "8eb92eff-07b2-41d4-83c7-88630758ea66", "standard": 3, "name": "Down Rim to Invert", "code": "Down_Rim_to_Invert", "required": false, "shown_by_default": false, "description": "", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.411Z"}}, {"model": "defects.standardheader", "pk": "a9fd81d5-0075-40bd-87f7-10583e04c19f", "fields": {"header": "eb5388dd-f2bb-4a8d-9e7a-f8ca3d4ef53c", "standard": 5, "name": "Use of Pipeline", "code": "ACK", "required": true, "shown_by_default": true, "description": "Record the use of the pipeline system", "data_type": "options", "options_selections": "COM, CUL, S, F, TW, Z", "options_description": "Combined system (Sewage and Stormwater combined), Culverted watercourse e.g. a short, buried section for a road crossing or similar, A drain designed to carry only surface water (Stormwater).,The installation is designed to carry only sewage (Foul)., Trade effluent sewer (Trade waste),Other—further information shall be included as a general header remark (code ADE) immediately following", "created_at": "2023-07-05T06:37:15.712Z"}}, {"model": "defects.standardheader", "pk": "ab54754d-9c3b-4be7-b3df-d3e80c067f43", "fields": {"header": "eb5388dd-f2bb-4a8d-9e7a-f8ca3d4ef53c", "standard": 4, "name": "Use of Pipeline", "code": "ACK", "required": true, "shown_by_default": true, "description": "Record the use of the pipeline system", "data_type": "options", "options_selections": "COM, CUL, S, F, TW, Z", "options_description": "Combined system (Sewage and Stormwater combined), Culverted watercourse e.g. a short, buried section for a road crossing or similar, A drain designed to carry only surface water (Stormwater).,The installation is designed to carry only sewage (Foul)., Trade effluent sewer (Trade waste),Other—further information shall be included as a general header remark (code ADE) immediately following", "created_at": "2023-07-05T06:37:17.417Z"}}, {"model": "defects.standardheader", "pk": "abe260e6-26a2-4722-b7cb-fe6e4beb9fa9", "fields": {"header": "577e6be3-61b8-4226-b2f6-2ca2fd042f58", "standard": 3, "name": "Upstream MH", "code": "Upstream_MH", "required": true, "shown_by_default": true, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:12.815Z"}}, {"model": "defects.standardheader", "pk": "ac21485e-f9c8-44b9-88ba-2a82ab68a4ca", "fields": {"header": "bc88fe45-f754-4332-a085-22f97b600ff4", "standard": 5, "name": "Location type", "code": "AAL", "required": false, "shown_by_default": false, "description": "Record the type of location of the pipe", "data_type": "options", "options_selections": "B, BO, C, D, DA, F, G, M, NS, P, R, W, Z", "options_description": "Within Bushland/parkland, Under a permanent building (Built over), Under a waterway (Creek), Under property with buildings (Developed), Difficult access e.g. motorway or operational railway land, Under a footway beside road, Beneath Gardens, Under other pedestrian area (Mall), Under a berm beside a road (Nature Strip), Under a field (Paddock), Under a road, Water foreshore, Other—further details shall be stated in remarks", "created_at": "2023-07-05T06:37:15.174Z"}}, {"model": "defects.standardheader", "pk": "ad3503c5-1887-44fa-8aac-5205d56db756", "fields": {"header": "586fe0bf-0fe9-4504-a3aa-726ba26e5942", "standard": 5, "name": "Parallel Line", "code": "AAR", "required": true, "shown_by_default": true, "description": "Record the line number where there is more than one direct line between two manholes. The line number is supplied by the asset owner.", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:15.263Z"}}, {"model": "defects.standardheader", "pk": "adb91d22-8d75-43d9-8272-77cf4bfa72d0", "fields": {"header": "5569781a-faf2-4c54-bd81-d2dd65257b14", "standard": 4, "name": "<PERSON><PERSON><PERSON>", "code": "ACC", "required": true, "shown_by_default": true, "description": "The width or diameter of the section in mm (##)", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.287Z"}}, {"model": "defects.standardheader", "pk": "adccbe4d-44a9-468e-8966-5d9cac7a774e", "fields": {"header": "577e6be3-61b8-4226-b2f6-2ca2fd042f58", "standard": 1, "name": "ID Upstream", "code": "AAB", "required": false, "shown_by_default": true, "description": "The node reference of the upstream node", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.862Z"}}, {"model": "defects.standardheader", "pk": "adf1a223-8ec8-45a9-99b9-b2805a33ac7a", "fields": {"header": "1450ed30-5926-4e04-bdbc-1261a9c24efb", "standard": 6, "name": "Client", "code": "AAM", "required": false, "shown_by_default": false, "description": "The name of the asset owner or engaging agency for inspection of the conduit", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:16.218Z"}}, {"model": "defects.standardheader", "pk": "af852c11-7e35-4d3a-8cee-b3db06f4b7af", "fields": {"header": "dd958525-95b7-4855-8e1e-728ffe0ea2c6", "standard": 2, "name": "Finish Node or Node 2 Reference", "code": "FinishNodeRef", "required": true, "shown_by_default": true, "description": "12 characters", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.917Z"}}, {"model": "defects.standardheader", "pk": "afe5ce9a-b4c5-4d26-ba6b-3db5386954e4", "fields": {"header": "882a5f44-4d2d-4c09-8fc0-2c48f5bd1538", "standard": 5, "name": "Structural Peak Score", "code": "ACS", "required": false, "shown_by_default": false, "description": "Calculated peak structural score", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:15.803Z"}}, {"model": "defects.standardheader", "pk": "b01e567a-68e0-4a8b-ad99-a41794b1d9b9", "fields": {"header": "68b9f14e-5952-49ab-ac91-007e467b2787", "standard": 1, "name": "Material", "code": "ACD", "required": false, "shown_by_default": true, "description": "The material of the fabric of the conduit", "data_type": "string", "options_selections": "", "options_description": "Acrylonitrile butadiene styrene, Aluminium alloy, Asbestos cement, Cast iron (type unknown), Clay-vitrified, Concrete reinforced, Concrete segments, Concrete sprayed, Concrete unreinforced, Ductile cast iron, Fibre reinforced cement, Fibre reinforced plastics, Galvanised iron or steel, Glass reinforced plastics, Grey cast iron, Masonry - brick (coursed), Masonry - bluestone/basalt (coursed), Masonry - other stone (coursed), Masonry - bluestone/basalt (uncoursed), Masonry - other stone (uncoursed), Masonry - concrete composite, Pitch fibre, Plastics (type unknown), Polyethylene, Polypropylene, Polyvinylchloride, Stainless steel, Steel, Unidentified type of concrete or cement mortar, Unidentified type of steel, Unidentified type of plastics, Unidentified material, Other-record details in remarks", "created_at": "2023-07-05T06:37:18.144Z"}}, {"model": "defects.standardheader", "pk": "b0fb2a62-54fa-44ee-98cf-4eb394b2a049", "fields": {"header": "bb705029-6500-4112-a6bd-8c0c6193ed0b", "standard": 6, "name": "Date of inspection", "code": "ABF", "required": false, "shown_by_default": true, "description": "Record the calendar date of the inspection using the DD-MM-YYYY format, leading zeros shall be included where necessary.", "data_type": "date", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:16.312Z"}}, {"model": "defects.standardheader", "pk": "b1650f47-f604-4553-a4ff-7ceffe069e43", "fields": {"header": "2c76e908-5b32-4bb6-bead-e8f1fa195ce8", "standard": 3, "name": "Street", "code": "Street", "required": true, "shown_by_default": true, "description": "Street number and street name", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:12.714Z"}}, {"model": "defects.standardheader", "pk": "b1e2eff4-777f-42b2-9c17-16a3369f9811", "fields": {"header": "c4a3b164-03d2-4c35-99d5-89a3aeefa543", "standard": 3, "name": "Custom Field Eight", "code": "Custom_Field_eight", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.604Z"}}, {"model": "defects.standardheader", "pk": "b23a1a88-dd77-45d1-b2ff-67e0b5379b8b", "fields": {"header": "78e11417-7d59-49b7-8d18-dbd7be4ae842", "standard": 2, "name": "Client Defined 12", "code": "ClientDefined12", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:14.897Z"}}, {"model": "defects.standardheader", "pk": "b29ac2a9-aad3-413b-ad40-dfad2583361d", "fields": {"header": "c1ddbad9-0682-4a29-9abe-1a88d785efe1", "standard": 3, "name": "Down Rim to Grade", "code": "Down_Rim_to_Grade", "required": false, "shown_by_default": false, "description": "", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.430Z"}}, {"model": "defects.standardheader", "pk": "b37b91df-008f-423e-9a69-1be99112abc4", "fields": {"header": "2e2574db-5beb-4e9d-892d-be28f1fe0fc6", "standard": 2, "name": "Circumferential Location of Start of Lateral", "code": "CircLocStartLat", "required": false, "shown_by_default": false, "description": "", "data_type": "options", "options_selections": "01, 02, 03, 04, 05, 06, 07, 08, 09, 10, 11, 12", "options_description": "", "created_at": "2023-07-05T06:37:14.299Z"}}, {"model": "defects.standardheader", "pk": "b3f1d18c-3bde-4491-8dfd-a8828af4f800", "fields": {"header": "7f9a2ab0-a7ad-4393-83d9-b004680471b4", "standard": 1, "name": "ID Downstream", "code": "AAF", "required": false, "shown_by_default": true, "description": "The node reference of the downstream node", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.876Z"}}, {"model": "defects.standardheader", "pk": "b4cb9865-d12d-4290-ae91-4445a3525b0e", "fields": {"header": "f733f544-b660-4c2f-bdaa-6694f30d1ca1", "standard": 1, "name": "Suburb", "code": "AAN", "required": false, "shown_by_default": false, "description": "The name of the town or suburb as specified by the asset owner", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.917Z"}}, {"model": "defects.standardheader", "pk": "b51e176a-0912-48e0-b5e5-550a909f662d", "fields": {"header": "f16aa8a5-286d-4507-8e8a-b35c61fac4ca", "standard": 3, "name": "Consequence Of Failure", "code": "Consequence_Of_Failure", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.200Z"}}, {"model": "defects.standardheader", "pk": "b532f72e-90e5-43b6-802d-8790e32c010c", "fields": {"header": "2c76e908-5b32-4bb6-bead-e8f1fa195ce8", "standard": 6, "name": "Street", "code": "AAJ", "required": false, "shown_by_default": true, "description": "A description of the location of the conduit e.g. street name. Include a street directory reference where this is a normal method of defining location", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:16.199Z"}}, {"model": "defects.standardheader", "pk": "b540efb3-55d2-4408-935a-875d6e22e796", "fields": {"header": "d12d393b-97ca-4a60-82b3-0ee70234a0b0", "standard": 3, "name": "Location Details", "code": "Location_Details", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.259Z"}}, {"model": "defects.standardheader", "pk": "b73c80d5-f190-4f66-bee3-a9b4257f1d0f", "fields": {"header": "1f912bf8-8afc-4f0d-886c-2dfe8ad4f8db", "standard": 4, "name": "Lining material", "code": "ACF", "required": true, "shown_by_default": true, "description": "Where a pipe has been lined, record the lining material", "data_type": "options", "options_selections": "CL, EP, GRP, FRP, PE, PVC, CIP", "options_description": "Cement Lining (including Sprayed Concrete or Geopolymer), Epoxy Lining, Glass Reinforced Plastic, Fibre Reinforced Plastic, Polyethylene, Polyvinyl Chloride, Cured In-Place Polyurethane or Vinyl Ester", "created_at": "2023-07-05T06:37:17.347Z"}}, {"model": "defects.standardheader", "pk": "b8b2316b-9cb4-4dc8-a341-8e89e4827afa", "fields": {"header": "eb5388dd-f2bb-4a8d-9e7a-f8ca3d4ef53c", "standard": 6, "name": "Use of conduit", "code": "ACK", "required": false, "shown_by_default": true, "description": "Record the use of the conduit system", "data_type": "options", "options_selections": "COM, CUL, D, S, TW, Z", "options_description": "Combined system (Sewage and surface water combined), Culverted watercourse, e.g. a short buried section for a road crossing or similar, A drain designed to carry only surface water, The installation is designed to carry only sewage, Trade effluent sewer (Trade waste), Other—further information shall be included as a general header remark (code ADE)immediately following", "created_at": "2023-07-05T06:37:16.582Z"}}, {"model": "defects.standardheader", "pk": "b8bc3970-1eeb-4c20-9a4e-5a7d14517f5f", "fields": {"header": "13da5c27-84bf-45ce-95f2-418b74796831", "standard": 3, "name": "Additional Info", "code": "Additional_Info", "required": false, "shown_by_default": true, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.482Z"}}, {"model": "defects.standardheader", "pk": "b8ecdeb6-3a39-4be0-b07d-cc4dd2990c4b", "fields": {"header": "62ef07ca-fc5f-4a65-bf3d-51e2f25de746", "standard": 4, "name": "<PERSON><PERSON><PERSON>", "code": "ACA", "required": true, "shown_by_default": true, "description": "Record the shape of the cross section of the pipe", "data_type": "options", "options_selections": "A, C, E, O, R, U, Z", "options_description": "Arch shaped—circular soffit and flat invert with parallel sides, Circular, Oviform (egg shaped), Oval—circular invert and soffit (of equal diameter) with parallel sides, Rectangular or Square, U shape—circular invert and flat top with parallel sides, Other—a description shall be included as a general header comment (code ADE) immediately following", "created_at": "2023-07-05T06:37:17.259Z"}}, {"model": "defects.standardheader", "pk": "b91609fd-f116-4f59-80eb-e73b2bf89bb9", "fields": {"header": "2ecd3507-9dc7-47d2-98bc-08c7f8386600", "standard": 2, "name": "Division / District", "code": "DivisionDistrict", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:14.062Z"}}, {"model": "defects.standardheader", "pk": "b93944dc-049c-4694-a98d-4c86776afdd2", "fields": {"header": "81e4ae6f-229a-458a-911d-16a670acd3b3", "standard": 5, "name": "Structural Mean Score", "code": "ACT", "required": false, "shown_by_default": false, "description": "Calculated mean structural score", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:15.820Z"}}, {"model": "defects.standardheader", "pk": "b9db222a-32f1-4a63-9c50-6dc99d26aa29", "fields": {"header": "1c029f63-70e4-4511-ad3b-39c787572666", "standard": 2, "name": "Flow Control Measures", "code": "FlowControlMeasures", "required": false, "shown_by_default": false, "description": "", "data_type": "options", "options_selections": "N, <PERSON><PERSON>, PB, X", "options_description": "No flow control, Blocked upstream, Partially blocked upstream, Other", "created_at": "2023-07-05T06:37:14.497Z"}}, {"model": "defects.standardheader", "pk": "ba6475cd-ed60-49db-84ae-db60e96add98", "fields": {"header": "68b9f14e-5952-49ab-ac91-007e467b2787", "standard": 3, "name": "Material", "code": "Material", "required": true, "shown_by_default": true, "description": "", "data_type": "options", "options_selections": "ABS, AC, BR, CAS, CLC, CMP, CP, CSB, CSU, CT, DIP, FRP, OB, PCP, PCCP, PE, PP, PSC, PVC, RCP, RPM, SB, SP, VCP, WD, XXX, ZZZ", "options_description": "Acrylonitrile Butadiene Styrene, Asbestos Cement, Brick, Cast Iron, Clay-lined Concrete Pipe, Corrugated Metal Pipe, Concrete Pipe (non-reinforced), Concrete Segments (bolted), Concrete Segments (unbolted), Clay Tile (not vitrified clay), Ductile Iron Pipe, Fiberglass Reinforced Pipe, Pitch Fiber (Orangeburg), Polymer Concrete Pipe, Pre-stressed Concrete Cylinder Pipe, Polyethylene, Polypropylene, Plastic/Steel Composite, Polyvinyl Chloride, Reinforced Concrete Pipe, Reinforced Plastic Pipe (Truss Pipe), Segmented Block, Steel Pipe, Vitrified Clay Pipe, Wood, Not Known, Other", "created_at": "2023-07-05T06:37:12.798Z"}}, {"model": "defects.standardheader", "pk": "baa248f0-6a40-4bcd-90ee-e0b78d8f639b", "fields": {"header": "253243a9-ef6a-4ba6-8d48-54e164b20cda", "standard": 6, "name": "Criticality", "code": "ACL", "required": false, "shown_by_default": false, "description": "A client defined alpha-numeric code describing how critical the conduit is to the system", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:16.127Z"}}, {"model": "defects.standardheader", "pk": "bacc622c-63ef-4008-b916-155624eaa7c4", "fields": {"header": "bc88fe45-f754-4332-a085-22f97b600ff4", "standard": 4, "name": "Location type", "code": "AAL", "required": false, "shown_by_default": false, "description": "Record the type of location of the pipe", "data_type": "options", "options_selections": "B, BO, C, D, DA, F, G, M, NS, P, R, W, Z", "options_description": "Within Bushland/parkland, Under a permanent building (Built over), Under a waterway (Creek), Under property with buildings (Developed), Difficult access e.g. motorway or operational railway land, Under a footway beside road, Beneath Gardens, Under other pedestrian area (Mall), Under a berm beside a road (Nature Strip), Under a field (Paddock), Under a road, Water foreshore, Other—further details shall be stated in remarks", "created_at": "2023-07-05T06:37:16.893Z"}}, {"model": "defects.standardheader", "pk": "baedd3a4-b8d9-43b3-bd0e-9066dd8248bb", "fields": {"header": "28b4efa5-1ec1-4f69-8ea7-f5c655d53ac1", "standard": 6, "name": "Operation of conduit", "code": "ACJ", "required": false, "shown_by_default": false, "description": "Record the operational mode of the conduit", "data_type": "options", "options_selections": "G, O, P", "options_description": "Gravity, Other/n, e.g. duct, Pressure", "created_at": "2023-07-05T06:37:16.564Z"}}, {"model": "defects.standardheader", "pk": "bb489228-963f-42a3-8c0e-53095c37c5ad", "fields": {"header": "7a4e53ec-ab40-4a2a-903b-a817d402b2f8", "standard": 4, "name": "Preliminary Structural Peak Grade", "code": "ACX", "required": false, "shown_by_default": false, "description": "Calculated peak structural condition grade", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.571Z"}}, {"model": "defects.standardheader", "pk": "bbdc6728-ec65-4817-ad31-87ff03f58151", "fields": {"header": "809a12e9-d6d8-41a0-93c3-c060d909c98b", "standard": 2, "name": "Client Defined 1", "code": "ClientDefined1", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:14.688Z"}}, {"model": "defects.standardheader", "pk": "bbe3e81a-d392-4d86-8011-ca3b8433a567", "fields": {"header": "1f912bf8-8afc-4f0d-886c-2dfe8ad4f8db", "standard": 3, "name": "Coating Method", "code": "Coating_Method", "required": false, "shown_by_default": false, "description": "", "data_type": "options", "options_selections": "CM, CT, EP, PE, PO, PU, PVC, XX, ZZ", "options_description": "Cement Mortar, Coal Tar or Bituminous, Epoxy, Polyethylene, Polyurethane, Polyurea, Polyvinyl Chloride, Not Known, Other", "created_at": "2023-07-05T06:37:13.283Z"}}, {"model": "defects.standardheader", "pk": "bce136e6-152d-4448-87fd-ac8e7d92dabf", "fields": {"header": "f7e2642d-09a6-48fb-a2b6-7e9c12382b4f", "standard": 1, "name": "ID Pipe", "code": "AAA", "required": true, "shown_by_default": true, "description": "The conduit reference as specified by the asset owner", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.849Z"}}, {"model": "defects.standardheader", "pk": "bd6e0b49-0d38-403b-96df-d7a13bffb593", "fields": {"header": "5df0c89c-8e70-4c5d-be64-e80b57339deb", "standard": 4, "name": "Video volume reference", "code": "ABO", "required": true, "shown_by_default": true, "description": "Record the file name for the video file, or storage media reference name.", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.162Z"}}, {"model": "defects.standardheader", "pk": "be3f36d8-0e28-4530-b451-2c5987d63ccc", "fields": {"header": "1450ed30-5926-4e04-bdbc-1261a9c24efb", "standard": 2, "name": "Client", "code": "Client", "required": false, "shown_by_default": false, "description": "BS EN 13508-2:2003", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.993Z"}}, {"model": "defects.standardheader", "pk": "be53858f-f7af-42d0-898a-ff5aff3f3fe5", "fields": {"header": "68b9f14e-5952-49ab-ac91-007e467b2787", "standard": 6, "name": "Material", "code": "ACD", "required": false, "shown_by_default": true, "description": "The material of the fabric of the conduit", "data_type": "string", "options_selections": "", "options_description": "Acrylonitrile butadiene styrene, Aluminium alloy, Asbestos cement, Cast iron (type unknown), Clay-vitrified, Concrete reinforced, Concrete segments, Concrete sprayed, Concrete unreinforced, Ductile cast iron, Fibre reinforced cement, Fibre reinforced plastics, Galvanised iron or steel, Glass reinforced plastics, Grey cast iron, Masonry - brick (coursed), Masonry - bluestone/basalt (coursed), Masonry - other stone (coursed), Masonry - bluestone/basalt (uncoursed), Masonry - other stone (uncoursed), Masonry - concrete composite, Pitch fibre, Plastics (type unknown), Polyethylene, Polypropylene, Polyvinylchloride, Stainless steel, Steel, Unidentified type of concrete or cement mortar, Unidentified type of steel, Unidentified type of plastics, Unidentified material, Other-record details in remarks", "created_at": "2023-07-05T06:37:16.500Z"}}, {"model": "defects.standardheader", "pk": "be98babb-7f67-4454-8f57-5f2344ab0954", "fields": {"header": "184c5759-9d80-4207-be0f-c3f6b90be55c", "standard": 2, "name": "Height or Diameter", "code": "HeightDiameter", "required": false, "shown_by_default": true, "description": "20-99999, whole numbers only", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:14.344Z"}}, {"model": "defects.standardheader", "pk": "bef53317-0e2f-4db3-b3e0-32d750b0255a", "fields": {"header": "1ebb8de0-500a-43dd-833a-fe0212ba0025", "standard": 5, "name": "Inspection Completion Status", "code": "ABS", "required": true, "shown_by_default": true, "description": "Record the completion status of the inspection", "data_type": "options", "options_selections": "IC, UI", "options_description": "Inspection Complete, Uncompleted Inspection", "created_at": "2023-07-05T06:37:15.518Z"}}, {"model": "defects.standardheader", "pk": "bf8aa6cd-b509-4e0b-8533-5ce6155d42c9", "fields": {"header": "bb705029-6500-4112-a6bd-8c0c6193ed0b", "standard": 2, "name": "Date", "code": "Date", "required": false, "shown_by_default": true, "description": "CCYY-MM-DD", "data_type": "date", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.840Z"}}, {"model": "defects.standardheader", "pk": "c033ebd7-2187-4ae4-9141-f75febfb73e1", "fields": {"header": "ff28a77e-9bdd-474c-b570-2ea998fd67c6", "standard": 4, "name": "Down node coordinate", "code": "AAG", "required": false, "shown_by_default": false, "description": "The grid reference (coordinates) of the downstream node", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:16.845Z"}}, {"model": "defects.standardheader", "pk": "c04782c2-0ab2-4656-9d1d-ab2028a8d58d", "fields": {"header": "1f912bf8-8afc-4f0d-886c-2dfe8ad4f8db", "standard": 1, "name": "Lining material", "code": "ACF", "required": false, "shown_by_default": false, "description": "Where a conduit has been lined, record the lining material", "data_type": "string", "options_selections": "", "options_description": "Cement (mortar) lining, Epoxy, Fibre reinforced plastics, Glass fibre reinforced cement, Glass fibre reinforced plastics, Polyethylene, Plastics (thermosetting) impregnated felt or woven sock, Flexible, Polyurethane, Polyurea, Polyvinylchloride, Other—record details in remarks", "created_at": "2023-07-05T06:37:18.171Z"}}, {"model": "defects.standardheader", "pk": "c144a50f-4f1f-476f-a564-8acbc013ecdb", "fields": {"header": "aeb13168-2b5a-4a21-828f-c2c14479b08b", "standard": 5, "name": "Method of inspection", "code": "ABE", "required": false, "shown_by_default": false, "description": "Record the method used to inspect the pipeline", "data_type": "options", "options_selections": "FZ, LP, M, PS, S, SS, TVPT, TVFA", "options_description": "Inspection by means of a fixed position zoom pipeline camera, Inspection by means of a remotely controlled laser profiler passed through the conduit, Direct inspection of a conduit by a person walking through the conduit (Manned), Inspection by means of a remotely controlled 3D optical pipeline scanner passed through the conduit, Inspection from the access structure only, Inspection by means of a remotely controlled sonar scanner passed through the conduit, Inspection by means of Pan-Tilt CCTV camera passed through the conduit, Inspection by means of Fixed Axial CCTV camera passed through the conduit", "created_at": "2023-07-05T06:37:15.345Z"}}, {"model": "defects.standardheader", "pk": "c2b4b96a-f649-4a42-a6f8-6b8a733f5434", "fields": {"header": "c04fc37d-fa16-48dc-b1f4-a1f2cea3ec72", "standard": 2, "name": "Client Defined 13", "code": "ClientDefined13", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:14.910Z"}}, {"model": "defects.standardheader", "pk": "c2f9ee07-c24f-4464-b2c4-21c9cddd6eae", "fields": {"header": "1c029f63-70e4-4511-ad3b-39c787572666", "standard": 4, "name": "Flow control measures", "code": "ADC", "required": false, "shown_by_default": false, "description": "Record the measures taken to deal with the flow at the time of the inspection", "data_type": "options", "options_selections": "B, N, P, Z", "options_description": "Flows have been blocked or diverted upstream, No measures taken, Flows partially blocked or diverted upstream, Other—record further details in remarks", "created_at": "2023-07-05T06:37:17.626Z"}}, {"model": "defects.standardheader", "pk": "c4a4f483-9e7e-40c3-8493-00e5748ca92a", "fields": {"header": "7f9a2ab0-a7ad-4393-83d9-b004680471b4", "standard": 5, "name": "Down node reference", "code": "AAF", "required": true, "shown_by_default": true, "description": "The asset ID of the downstream node", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:15.102Z"}}, {"model": "defects.standardheader", "pk": "c54a78cd-3aef-46de-b079-9b244380a20c", "fields": {"header": "62ef07ca-fc5f-4a65-bf3d-51e2f25de746", "standard": 1, "name": "<PERSON><PERSON><PERSON>", "code": "ACA", "required": false, "shown_by_default": false, "description": "Record the shape of the cross section of the conduit", "data_type": "options", "options_selections": "A, C, E, O, R, U, X, Z", "options_description": "Arch shaped—circular soffit and flat invert with parallel sides, Circular, Oviform (egg shaped), Oval—circular invert and soffit (of equal diameter) with parallel sides, Rectangular, U shape—circular invert and flat top with parallel sides, Local section code to be specified by the asset owner and prefixed by an X, e.g. XA,XB etc, Other—a description shall be included as a general header comment (code ADE)immediately following", "created_at": "2023-07-05T06:37:18.105Z"}}, {"model": "defects.standardheader", "pk": "c5b1476a-c5c4-4c00-91e5-a4f8e2d9baf2", "fields": {"header": "f733f544-b660-4c2f-bdaa-6694f30d1ca1", "standard": 3, "name": "City", "code": "City", "required": true, "shown_by_default": true, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:12.729Z"}}, {"model": "defects.standardheader", "pk": "c7645eab-dc16-4bd1-868d-42efef2fe53f", "fields": {"header": "46c5e176-f46b-49f1-b9ae-31614d5d58ee", "standard": 1, "name": "Cleaning", "code": "ACM", "required": false, "shown_by_default": false, "description": "Record whether the conduit was cleaned prior to the inspection", "data_type": "options", "options_selections": "C, NC", "options_description": "The conduit was cleaned prior to the inspection, The conduit was not cleaned prior to the inspection", "created_at": "2023-07-05T06:37:18.231Z"}}, {"model": "defects.standardheader", "pk": "c7ad8de9-cd7e-4d0b-b43b-79f87dd991d7", "fields": {"header": "eb4c549e-5ca0-4e3b-957b-43867e410709", "standard": 3, "name": "Location Code", "code": "Location_Code", "required": false, "shown_by_default": false, "description": "", "data_type": "options", "options_selections": "A, B, C, D, E, F, G, H, I, J, K, L, M, N, O, P, Y, Z", "options_description": "Primary major arterial roads, interstates, numbered roads or town/city center roads for heavy vehicles. Secondary roads, non-numbered suburban/rural  for heavy vehicles. Local rural streets with light traffic, town and city back streets, estate streets and curbside parking areas., Easement/Right of way, Woods, Sidewalk, Parking lot, Alley, Ditch, Building, Creek, Railway, Airport, Levee/Floodwall, Dam, Levee Pump Station, Yard, Other", "created_at": "2023-07-05T06:37:13.243Z"}}, {"model": "defects.standardheader", "pk": "c7b5690f-2e06-48cb-9d60-78d50391ba87", "fields": {"header": "ced86c20-4b65-4e44-830b-36de0e0d9c1b", "standard": 5, "name": "Service Mean Score", "code": "ACW", "required": false, "shown_by_default": false, "description": "Calculated mean service score", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:15.872Z"}}, {"model": "defects.standardheader", "pk": "c865275d-9e04-424b-950e-f296dd24bec9", "fields": {"header": "732192bc-ef6d-4e38-ab2d-fcebcdf789a3", "standard": 6, "name": "Finish node coordinates", "code": "AAG", "required": false, "shown_by_default": false, "description": "The grid reference (easting and northing coordinates) of the finish node", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:16.041Z"}}, {"model": "defects.standardheader", "pk": "c8ddad49-39b5-4d87-be6b-63c7a82c10a9", "fields": {"header": "fbefac8e-2b38-448a-9607-73737fec849d", "standard": 3, "name": "IsImperial", "code": "IsImperial", "required": false, "shown_by_default": true, "description": "", "data_type": "boolean", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:12.960Z"}}, {"model": "defects.standardheader", "pk": "c95d7cc4-f63f-4b4c-aaa1-7a11be1639a2", "fields": {"header": "ace6021c-890d-4b6d-a2a3-7f6ec1855658", "standard": 2, "name": "Node 2 Grid Ref Y", "code": "Node2GridRefY", "required": false, "shown_by_default": false, "description": "0 to 999999", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:14.230Z"}}, {"model": "defects.standardheader", "pk": "ca55b055-2dbf-4552-873a-e679d4ce2a91", "fields": {"header": "fb4fefec-bd51-42ac-ad90-ec5ca2011c3a", "standard": 4, "name": "Total Service Score", "code": "ACU", "required": false, "shown_by_default": false, "description": "Calculated total service score", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.531Z"}}, {"model": "defects.standardheader", "pk": "ca967388-86b6-4969-b961-966e67894ee7", "fields": {"header": "f733f544-b660-4c2f-bdaa-6694f30d1ca1", "standard": 5, "name": "Town or suburb", "code": "AAN", "required": false, "shown_by_default": false, "description": "The name of the town or suburb as specified by the asset owner", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:15.205Z"}}, {"model": "defects.standardheader", "pk": "cac6424a-c173-46b3-abc8-4a647cbed9b0", "fields": {"header": "6f399782-06c0-447e-8d7b-46561086d080", "standard": 5, "name": "Depth at downstream node", "code": "ACI", "required": false, "shown_by_default": false, "description": "Record the depth of the invert of the pipe below cover level at the downstream node in m", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:15.681Z"}}, {"model": "defects.standardheader", "pk": "cad72632-99ca-4c6f-90ca-f1f249bf00f2", "fields": {"header": "3b2ead02-7e71-42a8-af25-693e70d9ba04", "standard": 2, "name": "Temperature", "code": "Temperature", "required": false, "shown_by_default": false, "description": "", "data_type": "options", "options_selections": "A, B", "options_description": "", "created_at": "2023-07-05T06:37:14.522Z"}}, {"model": "defects.standardheader", "pk": "cb24a971-97e0-4f4e-a315-32d9cbdde748", "fields": {"header": "bef2a86d-c43e-4fc4-94b4-808420302548", "standard": 2, "name": "Client Defined 6", "code": "ClientDefined6", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:14.797Z"}}, {"model": "defects.standardheader", "pk": "cb5f632d-7f3e-4cb3-8d32-2a92927f0c80", "fields": {"header": "cee6ece4-7cf2-484e-a4e7-aa70231e2599", "standard": 5, "name": "Up node coordinate", "code": "AAE", "required": false, "shown_by_default": false, "description": "The grid reference (coordinates) of the upstream node", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:15.088Z"}}, {"model": "defects.standardheader", "pk": "cc2aa268-7236-4b1b-9634-073a8ee1a326", "fields": {"header": "5569781a-faf2-4c54-bd81-d2dd65257b14", "standard": 2, "name": "<PERSON><PERSON><PERSON>", "code": "<PERSON><PERSON><PERSON>", "required": false, "shown_by_default": false, "description": "20-99999, whole numbers only", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:14.357Z"}}, {"model": "defects.standardheader", "pk": "cfa97bad-c398-4a71-b37f-3f46294acdc0", "fields": {"header": "f733f544-b660-4c2f-bdaa-6694f30d1ca1", "standard": 6, "name": "Suburb", "code": "AAN", "required": false, "shown_by_default": false, "description": "The name of the town or suburb as specified by the asset owner", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:16.234Z"}}, {"model": "defects.standardheader", "pk": "cfeba280-2c2b-4353-8b4a-9ba6b09515e9", "fields": {"header": "78fe4754-7bf7-4c57-832b-aeb6d1afce89", "standard": 4, "name": "Total Structural Score", "code": "ACR", "required": false, "shown_by_default": false, "description": "Calculated total structural score", "data_type": "options", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.484Z"}}, {"model": "defects.standardheader", "pk": "d1f09048-fd40-4fbd-8512-10c9578b6dcc", "fields": {"header": "9227dbbf-d623-40ae-8544-f6b592bcb9d7", "standard": 3, "name": "Up Elevation", "code": "Up_Elevation", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:12.856Z"}}, {"model": "defects.standardheader", "pk": "d27f8da6-cfb2-4280-952a-74678653dea1", "fields": {"header": "31839d43-fa88-4b3a-8c2e-ff140e37ac9b", "standard": 2, "name": "Client Defined 4", "code": "ClientDefined4", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:14.759Z"}}, {"model": "defects.standardheader", "pk": "d29df3d5-7ab1-47c8-b5b3-34e37780ccc2", "fields": {"header": "6b52d1b8-8dea-4a8c-8b28-7f1d64a45bc5", "standard": 1, "name": "Operator", "code": "ABH", "required": false, "shown_by_default": false, "description": "Record the name of the operator, company, work group or individual engaged to conduct the inspection. Record the name of the individual person conducting the inspection where appropriate.", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.987Z"}}, {"model": "defects.standardheader", "pk": "d2a9c930-3b5f-4d7b-a30e-cb59180ef7cc", "fields": {"header": "7f9a2ab0-a7ad-4393-83d9-b004680471b4", "standard": 4, "name": "Down node reference", "code": "AAF", "required": true, "shown_by_default": true, "description": "The asset ID of the downstream node", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:16.831Z"}}, {"model": "defects.standardheader", "pk": "d37fa750-41f5-446c-95a2-0a1845d8b170", "fields": {"header": "96d1a467-9c00-4902-8d67-b8e6bbb89c14", "standard": 2, "name": "Video Image Storage Media", "code": "VideoImageStorage", "required": false, "shown_by_default": false, "description": "", "data_type": "options", "options_selections": "VHS, CD, DVD, PHD, X", "options_description": "VHS tape, Video CD, Video DVD, Portable hard drive, Other", "created_at": "2023-07-05T06:37:14.578Z"}}, {"model": "defects.standardheader", "pk": "d4a1dc5c-1d13-4696-be53-3c93703a1a05", "fields": {"header": "2ecd3507-9dc7-47d2-98bc-08c7f8386600", "standard": 4, "name": "District/Catchment", "code": "AAO", "required": false, "shown_by_default": false, "description": "The name of the district or catchment as specified by the asset owner", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:16.936Z"}}, {"model": "defects.standardheader", "pk": "d56bf988-b730-436e-a065-110403857002", "fields": {"header": "d3613be4-b984-4e3f-97d3-dc2e6707d5e3", "standard": 5, "name": "Survey Length", "code": "ABR", "required": true, "shown_by_default": true, "description": "Record the measured Length is the length of pipe that has been surveyed (#.#).", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:15.503Z"}}, {"model": "defects.standardheader", "pk": "d6146177-5b5a-4968-b1b2-765171aebd87", "fields": {"header": "4ef9b123-9582-4c23-9eb1-967dda2bc501", "standard": 4, "name": "Lining type", "code": "ACE", "required": false, "shown_by_default": false, "description": "Where a pipe has been lined, record the method of lining", "data_type": "options", "options_selections": "CFL, CIP, LCP, LDP, MFL, SEG, SPL, SWL, Z", "options_description": "Close fit lining, Cured in place lining, Lining with a continuous conduit (pipeline) e.g. a pipe string welded on the surface prior to insertion, Lining with discrete pipes i.e. short pipes jointed underground, Lining inserted during manufacture (Manufacturer’s lining), Segmental lining, Sprayed lining, Spirally wound lining, Other", "created_at": "2023-07-05T06:37:17.331Z"}}, {"model": "defects.standardheader", "pk": "d6a0656d-fdd1-4453-8f62-5f55c463239f", "fields": {"header": "d3613be4-b984-4e3f-97d3-dc2e6707d5e3", "standard": 6, "name": "Actual inspection length", "code": "ABR", "required": false, "shown_by_default": true, "description": "The actual length of conduit inspected may be significantly different to the anticipated length due to the inspection being abandoned or errors in the recorded information. This field may be automatically populated at the end of the inspection.", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:16.622Z"}}, {"model": "defects.standardheader", "pk": "d6b62995-e307-4ceb-9e44-9ef1d5b79f76", "fields": {"header": "ff3f87cc-c289-4ae3-bbe2-b57ed1947c54", "standard": 4, "name": "Land ownership", "code": "AAQ", "required": false, "shown_by_default": false, "description": "Record the ownership of the land", "data_type": "options", "options_selections": "C, Q, T", "options_description": "Public land (Council or Crown land), Not known (Query), Private land", "created_at": "2023-07-05T06:37:16.969Z"}}, {"model": "defects.standardheader", "pk": "d8a63127-915d-4dca-866e-ee3b4132af87", "fields": {"header": "577e6be3-61b8-4226-b2f6-2ca2fd042f58", "standard": 6, "name": "ID Upstream", "code": "AAB", "required": false, "shown_by_default": true, "description": "The node reference of the upstream node", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:16.170Z"}}, {"model": "defects.standardheader", "pk": "d8a9e652-a337-4fde-b044-c8b64dc0fdb8", "fields": {"header": "9f01ca1f-4f64-4f96-8470-931d298691aa", "standard": 3, "name": "Up Easting", "code": "Up_Easting", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:12.842Z"}}, {"model": "defects.standardheader", "pk": "d908c7fc-3305-4699-96db-bb8b2f6f2b43", "fields": {"header": "14fe8eaa-060b-42b5-b172-fa938fa2c301", "standard": 6, "name": "Contractor", "code": "ABS", "required": false, "shown_by_default": false, "description": "Record the company or trading name of the entity responsible for undertaking the inspection", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:16.437Z"}}, {"model": "defects.standardheader", "pk": "d91bc2e7-4798-46b2-b6e1-66c49259de9f", "fields": {"header": "ba61f12e-367a-4161-b3a3-59f353bd2902", "standard": 3, "name": "Custom Field Two", "code": "Custom_Field_two", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.522Z"}}, {"model": "defects.standardheader", "pk": "d995a326-9836-41fc-97d1-17ff764159ec", "fields": {"header": "80f0c697-3167-498c-8599-fd77ec0b24d7", "standard": 4, "name": "Original coding system", "code": "ABB", "required": false, "shown_by_default": false, "description": "Where the coding has been translated from an earlier version or from another system, the name of the original coding system.", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.054Z"}}, {"model": "defects.standardheader", "pk": "da7ae773-6856-4720-98f6-ac1bf4074482", "fields": {"header": "aeb13168-2b5a-4a21-828f-c2c14479b08b", "standard": 3, "name": "Inspection Technology Used", "code": "Inspection_Technology_Used", "required": false, "shown_by_default": false, "description": "", "data_type": "options", "options_selections": "CC, LA, SO, SS, ZM, ZZ", "options_description": "CCTV, Laser, Sonar, Sidewall scanning, Zoom, Other", "created_at": "2023-07-05T06:37:13.186Z"}}, {"model": "defects.standardheader", "pk": "db0a4ce0-59ab-4c59-8d01-fe7b7c7d535f", "fields": {"header": "5df0c89c-8e70-4c5d-be64-e80b57339deb", "standard": 5, "name": "Video volume reference", "code": "ABO", "required": true, "shown_by_default": true, "description": "Record the file name for the video file, or storage media reference name.", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:15.449Z"}}, {"model": "defects.standardheader", "pk": "db6b87fb-613f-4e20-86be-bb0b84b06b38", "fields": {"header": "a80da5e4-5fe0-4e2f-84a3-eabe24234912", "standard": 2, "name": "Client Defined 18", "code": "ClientDefined18", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:14.987Z"}}, {"model": "defects.standardheader", "pk": "dc730d79-bac3-4105-855c-02fc0e43bdce", "fields": {"header": "1b2573b0-df4a-4f9b-937f-b7394af581da", "standard": 4, "name": "Purpose of inspection", "code": "ABP", "required": true, "shown_by_default": true, "description": "Record the purpose of the inspection", "data_type": "options", "options_selections": "C, IE, IP, L, NC, OE, R, RC, S, SE, T, W, Z", "options_description": "Completion of an earlier abandoned inspection, Suspected infiltration problem (Infiltration exam), Investment planning, Locating a pipe, connection or a manhole structure, Final inspection of a new construction, Suspected operational problem (Operational exam), Routine inspection of condition, Final inspection of renovation or repair (Renovation/repair control), Sample inspection, Suspected structural problem (Structural exam), Transfer of ownership, End of warranty period, Other—the reason shall be recorded as a header remark (code ADE) immediately following", "created_at": "2023-07-05T06:37:17.176Z"}}, {"model": "defects.standardheader", "pk": "dc91b298-5b92-400a-9a5d-6b6572dbfc51", "fields": {"header": "b1eee34d-767f-48a0-9439-35c82a6052e1", "standard": 6, "name": "Client <PERSON>f", "code": "ABJ", "required": false, "shown_by_default": false, "description": "The asset owner’s or engaging agency's job reference", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:16.373Z"}}, {"model": "defects.standardheader", "pk": "ddf7eb7f-ff94-4617-aed6-8f3c3e7fd416", "fields": {"header": "656d863c-e398-450d-b462-1ac4e93139cb", "standard": 5, "name": "Jointing method", "code": "ACO", "required": false, "shown_by_default": false, "description": "Record the pipe jointing method", "data_type": "options", "options_selections": "A, BF, BFD, CMJ, EF, F, L, MC, RRJ, SCJ", "options_description": "Pipe sections abutted with no jointing elements or material, Butt fusion welded e.g. steel and PE, Butt fusion welded with ground weld reinforcement(steel) or debeading (PE), Cement mortar jointed e.g. concrete, Electrofusion coupling weld (PE only), Flange jointedLLap fillet weld (steel only), Mechanical coupling, JRubber ring (elastomeric seal) jointed (socket and spigot or joint coupling/collar), Solvent cement jointed e.g. PVC, ABS", "created_at": "2023-07-05T06:37:15.746Z"}}, {"model": "defects.standardheader", "pk": "de15f25b-4960-4a7e-bcde-5ddc9a088e46", "fields": {"header": "b1c8c021-579d-4782-bccd-1573595610a0", "standard": 2, "name": "Node 3 Grid Ref Y", "code": "Node3GridRefY", "required": false, "shown_by_default": false, "description": "0 to 999999", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:14.259Z"}}, {"model": "defects.standardheader", "pk": "de592a2e-10c5-429a-93e8-5fa4cae3ab28", "fields": {"header": "184c5759-9d80-4207-be0f-c3f6b90be55c", "standard": 6, "name": "Diameter", "code": "ACB", "required": false, "shown_by_default": true, "description": "The height or diameter of the section in mm", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:16.469Z"}}, {"model": "defects.standardheader", "pk": "dea97b45-58b9-495a-a2a1-d7ee7295ce09", "fields": {"header": "abf85997-2c41-479e-9b37-10f2289dbdd9", "standard": 5, "name": "Up Node Type", "code": "ACP", "required": false, "shown_by_default": false, "description": "Record the type of node at the upstream", "data_type": "options", "options_selections": "SND, SMH, SPS, SIP, SMS, STND, STMH, STI, STO, STCP, STMS", "options_description": "Sewer node – includes: Buried Junctions, material change, bend/deviation, diameter change, Sewer Manhole, Sewer pump station, Sewer Inspection Point, Sewer Miscellaneous, Stormwater node – includes: Buried Junctions, material change, bend/deviation, diameter change, Stormwater manhole, Stormwater Inlet, Stormwater Outlet, Stormwater Catchpit, Stormwater Miscellaneous", "created_at": "2023-07-05T06:37:15.760Z"}}, {"model": "defects.standardheader", "pk": "df232fb1-ef84-4cf2-8dae-a7ad46e5d3d7", "fields": {"header": "078cb335-8a88-4c48-9ff9-4455c5fabca6", "standard": 6, "name": "Standard", "code": "ABA", "required": false, "shown_by_default": false, "description": "The version of the standard and software used to record the data", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:16.274Z"}}, {"model": "defects.standardheader", "pk": "df42d76d-5364-4ad3-b58d-9eecfb060f7c", "fields": {"header": "28161956-6810-4781-917c-e9f2094539bf", "standard": 2, "name": "Node 3 Grid Ref X", "code": "Node3GridRefX", "required": false, "shown_by_default": false, "description": "0 to 999999", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:14.244Z"}}, {"model": "defects.standardheader", "pk": "e064e20f-9696-4474-8446-1bf0c1c42622", "fields": {"header": "4ce2fd7b-dd42-49b0-bd51-5185d7e522eb", "standard": 3, "name": "GPS Accuracy", "code": "GPS_Accuracy", "required": false, "shown_by_default": false, "description": "", "data_type": "options", "options_selections": "L, M, N", "options_description": "Survey Level, Sub-Meter, Nearest Meter", "created_at": "2023-07-05T06:37:13.457Z"}}, {"model": "defects.standardheader", "pk": "e15aefa9-5298-442b-ad77-63ded645b04f", "fields": {"header": "e0b41c70-229c-4aa3-af46-223b031d3b07", "standard": 4, "name": "Date of Data Entry", "code": "ABU", "required": false, "shown_by_default": false, "description": "The date of the data entry (coding is undertaken) if different to the date of inspection using the DD/MM/YYYY format.", "data_type": "date", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.245Z"}}, {"model": "defects.standardheader", "pk": "e231b079-6589-47e8-8d35-6d26761c0aaa", "fields": {"header": "ff3f87cc-c289-4ae3-bbe2-b57ed1947c54", "standard": 5, "name": "Land ownership", "code": "AAQ", "required": false, "shown_by_default": false, "description": "Record the ownership of the land", "data_type": "options", "options_selections": "C, Q, T", "options_description": "Public land (Council or Crown land), Not known (Query), Private land", "created_at": "2023-07-05T06:37:15.248Z"}}, {"model": "defects.standardheader", "pk": "e31bdf55-822b-409f-9d45-5ba0fa2fc956", "fields": {"header": "58aa11be-5ec6-4fa1-b160-8bb6885a7439", "standard": 2, "name": "Start Node Grid Ref X", "code": "StartNodeGridRefX", "required": false, "shown_by_default": false, "description": "0 to 999999", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:14.121Z"}}, {"model": "defects.standardheader", "pk": "e3265949-81c5-460f-b11c-1fded5fe91ff", "fields": {"header": "3e140d9a-b8e1-4f95-9c6b-ee252407af43", "standard": 1, "name": "Depth at upstream node", "code": "ACH", "required": false, "shown_by_default": false, "description": "Record the depth of the invert of the conduit below cover level at the upstream node in m", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:18.188Z"}}, {"model": "defects.standardheader", "pk": "e3e475b0-498d-4b97-8587-e5ade6dd158b", "fields": {"header": "ced86c20-4b65-4e44-830b-36de0e0d9c1b", "standard": 4, "name": "Service Mean Score", "code": "ACW", "required": false, "shown_by_default": false, "description": "Calculated mean service score", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.558Z"}}, {"model": "defects.standardheader", "pk": "e40eef5a-346b-4603-abca-8659135be7b8", "fields": {"header": "fef33581-6cd1-4550-8d10-8f372f2a9491", "standard": 2, "name": "Photographic Image Storage Format", "code": "PhotographicStorageFormat", "required": false, "shown_by_default": false, "description": "", "data_type": "options", "options_selections": "A, C, D, E, F, G, X", "options_description": "Still photographs, Windows Meta File (WMF), Graphics Image File (GIF), JPEG file, TIF, PNG, Other", "created_at": "2023-07-05T06:37:14.660Z"}}, {"model": "defects.standardheader", "pk": "e442c4e7-0b22-4c62-8dcc-8663dd479262", "fields": {"header": "37c7dc06-3c44-40db-bb23-b22833d251df", "standard": 3, "name": "Pipe Joint Length", "code": "Pipe_Joint_Length", "required": false, "shown_by_default": false, "description": "", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.299Z"}}, {"model": "defects.standardheader", "pk": "e49dca8e-1611-4f75-907a-8335405d577d", "fields": {"header": "0abd6186-a53c-47fb-9931-294a05ce84cd", "standard": 2, "name": "Depth at Start Node", "code": "DepthAtStartNode", "required": false, "shown_by_default": false, "description": "0 to 99.99", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:14.183Z"}}, {"model": "defects.standardheader", "pk": "e583b00e-25cc-4d3d-9a2d-b0a14638d44f", "fields": {"header": "13da5c27-84bf-45ce-95f2-418b74796831", "standard": 2, "name": "General Remarks", "code": "GeneralRemarks", "required": false, "shown_by_default": true, "description": "Max 50 characters", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:14.445Z"}}, {"model": "defects.standardheader", "pk": "e5c862b7-9579-44bf-ba78-b83675bdd32b", "fields": {"header": "4f83bd6b-8e8f-4952-94e7-505eba80f635", "standard": 3, "name": "Custom Field sixteen", "code": "Custom_Field_sixteen", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.723Z"}}, {"model": "defects.standardheader", "pk": "e6ace9db-f4c4-4287-91c6-9f1712999f4c", "fields": {"header": "0ddabd9a-a8a7-4e16-80c0-dd2b824437d4", "standard": 5, "name": "Pipe <PERSON>", "code": "ABQ", "required": true, "shown_by_default": true, "description": "Record the measured length of the pipe asset (#.#).", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:15.478Z"}}, {"model": "defects.standardheader", "pk": "e72da698-901f-4e78-9c02-2cd5b71d2b05", "fields": {"header": "078cb335-8a88-4c48-9ff9-4455c5fabca6", "standard": 2, "name": "Standard", "code": "Standard", "required": true, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.980Z"}}, {"model": "defects.standardheader", "pk": "e7e4abdc-c808-4b0f-af0e-3eabc0da36b9", "fields": {"header": "c68a7bd7-7447-4337-b2d8-5c58e09e4ecc", "standard": 3, "name": "Up Rim to Grade", "code": "Up_Rim_to_Grade", "required": false, "shown_by_default": false, "description": "", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.381Z"}}, {"model": "defects.standardheader", "pk": "e7f44dcb-dd97-46a1-86a4-91c5b2ae9fbb", "fields": {"header": "1b2573b0-df4a-4f9b-937f-b7394af581da", "standard": 3, "name": "Purpose of Survey", "code": "Purpose", "required": false, "shown_by_default": false, "description": "", "data_type": "options", "options_selections": "A, B, C, D, E, F, G, H, I, R, X", "options_description": "Maintenance Related, Infiltration/Inflow Investigation, Post Rehabilitation Survey, Pre-Rehabilitation Survey, Pre-Acceptance New Sewers, Routine Assessment, Capital Improvement Program Assessment, Resurvey For Any Reason, Sewer System Evaluation Survey (SSES), Pre-Existing Video, Not Known", "created_at": "2023-07-05T06:37:13.172Z"}}, {"model": "defects.standardheader", "pk": "e8547df6-6595-4daa-b92c-7d6b6af54dc8", "fields": {"header": "7f3942c5-355c-45cd-a92b-ffbefcdb5cdf", "standard": 3, "name": "Media Label", "code": "Media_Label", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.067Z"}}, {"model": "defects.standardheader", "pk": "e98c739d-77b5-4a5b-9db7-3c1c97f5106f", "fields": {"header": "733d2e1b-88ec-4618-958b-9d13892c5829", "standard": 2, "name": "Longitudinal Location of Start of Lateral", "code": "LongLocStartLat", "required": false, "shown_by_default": false, "description": "0.1 to 999.9", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:14.286Z"}}, {"model": "defects.standardheader", "pk": "ea13d614-8590-4a8f-ba6d-e547b67717af", "fields": {"header": "1b2573b0-df4a-4f9b-937f-b7394af581da", "standard": 2, "name": "Purpose of Inspection", "code": "PurposeOfInspection", "required": false, "shown_by_default": false, "description": "", "data_type": "options", "options_selections": "A, B, C, D, E, F, G, H, I, J, X", "options_description": "Investigation of known structural or service defects, Investigation of infiltration problems, Post completion inspection of repairs or renovations, Pre-adoption survey prior to vesting as public sewer, Post completion inspection of new sewers, Sample survey of sewers to determine asset condition of a sewer system, Routine inspection of condition, Investigation of a suspected operational problem, Investment planning, End of warranty period, Other", "created_at": "2023-07-05T06:37:14.472Z"}}, {"model": "defects.standardheader", "pk": "ea17a636-a0fc-492f-b667-95b7a0f48ad1", "fields": {"header": "04453617-9aa3-4b49-a90e-cb383ede3b52", "standard": 3, "name": "Custom Field fifteen", "code": "Custom_Field_fifteen", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.707Z"}}, {"model": "defects.standardheader", "pk": "ea60e8c3-f739-40ca-9423-707a911598fe", "fields": {"header": "62ef07ca-fc5f-4a65-bf3d-51e2f25de746", "standard": 5, "name": "<PERSON><PERSON><PERSON>", "code": "ACA", "required": true, "shown_by_default": true, "description": "Record the shape of the cross section of the pipe", "data_type": "options", "options_selections": "A, C, E, O, R, U, Z", "options_description": "Arch shaped—circular soffit and flat invert with parallel sides, Circular, Oviform (egg shaped), Oval—circular invert and soffit (of equal diameter) with parallel sides, Rectangular or Square, U shape—circular invert and flat top with parallel sides, Other—a description shall be included as a general header comment (code ADE) immediately following", "created_at": "2023-07-05T06:37:15.561Z"}}, {"model": "defects.standardheader", "pk": "eac2cd22-9f58-4a4e-aefd-8b503ad09539", "fields": {"header": "f7e2642d-09a6-48fb-a2b6-7e9c12382b4f", "standard": 3, "name": "Pipe Segment Reference", "code": "Pipe_Segment_Reference", "required": true, "shown_by_default": true, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:12.623Z"}}, {"model": "defects.standardheader", "pk": "ed64837f-798c-4545-9757-8f631211b5e4", "fields": {"header": "a37cd83e-08f2-4a34-9ce2-868b949f6dcb", "standard": 2, "name": "Node 1 Reference", "code": "Node1Ref", "required": true, "shown_by_default": true, "description": "12 characters", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.893Z"}}, {"model": "defects.standardheader", "pk": "edb8eaa7-edaf-4728-b685-fbed16f6d169", "fields": {"header": "bc88fe45-f754-4332-a085-22f97b600ff4", "standard": 6, "name": "Location type", "code": "AAL", "required": false, "shown_by_default": false, "description": "Record the most predominant and/or critical type of location of the conduit", "data_type": "string", "options_selections": "", "options_description": "Bushland/parkland, Under a permanent building (Built over), Under a waterway (Creek) or body of water, On property with buildings (Developed), Easement, In a footway beside road, Gardens, In other pedestrian area (Mall), Motorway or operational railway, In verge beside a road (Nature Strip), In a field (Paddock), In a road carriageway, Road to easement, Water foreshore, Other—further details shall be stated in remarks", "created_at": "2023-07-05T06:37:16.054Z"}}, {"model": "defects.standardheader", "pk": "ee7f24b0-27cf-4607-87f6-2de9dda8ef54", "fields": {"header": "2316063f-9608-47df-abba-66ae8bcf8fa7", "standard": 5, "name": "Service Peak Score", "code": "ACV", "required": false, "shown_by_default": false, "description": "Calculated peak service score", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:15.851Z"}}, {"model": "defects.standardheader", "pk": "ef1f327a-0f17-4781-bceb-7a9680c86f15", "fields": {"header": "58a9f101-0387-4847-9f27-6185b77061dc", "standard": 2, "name": "Client Defined 9", "code": "ClientDefined9", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:14.847Z"}}, {"model": "defects.standardheader", "pk": "ef927d2f-da57-4dcb-9a26-6af7f0c580cc", "fields": {"header": "e16c52ae-56e8-43a9-bda0-f82798293016", "standard": 3, "name": "Inspection Time", "code": "Inspection_Time", "required": false, "shown_by_default": true, "description": "HH:MM in 24 hour time", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.098Z"}}, {"model": "defects.standardheader", "pk": "efebda11-c6f3-4c9d-b38d-245cbd943f21", "fields": {"header": "569c7e4a-dad0-4ba2-8f7e-5d7a5b29f367", "standard": 3, "name": "Custom Field Seven", "code": "Custom_Field_seven", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.591Z"}}, {"model": "defects.standardheader", "pk": "f02ba698-fb24-464a-8a86-d68735926680", "fields": {"header": "e26942f7-d18e-4e19-91d7-0be761a9e334", "standard": 6, "name": "Pipe Unit Length", "code": "PipeUnitLength", "required": false, "shown_by_default": false, "description": "Length of the pipe asset", "data_type": "number", "options_selections": [], "options_description": null, "created_at": "2023-11-09T07:01:01.041Z"}}, {"model": "defects.standardheader", "pk": "f13aed8b-204b-44de-b3cf-4a2eca40f005", "fields": {"header": "bc39399c-cda9-4390-8fb6-9b64c15d9a29", "standard": 3, "name": "Reverse setup", "code": "Reverse_Setup", "required": false, "shown_by_default": false, "description": "Specifys that a second survey has been done on the pipe segment--use inspection ID from matching survey", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.496Z"}}, {"model": "defects.standardheader", "pk": "f1689e35-adb9-4c0a-afc8-397de18b60bc", "fields": {"header": "656d863c-e398-450d-b462-1ac4e93139cb", "standard": 4, "name": "Jointing method", "code": "ACO", "required": false, "shown_by_default": false, "description": "Record the pipe jointing method", "data_type": "options", "options_selections": "A, BF, BFD, CMJ, EF, F, L, MC, RRJ, SCJ", "options_description": "Pipe sections abutted with no jointing elements or material, Butt fusion welded e.g. steel and PE, Butt fusion welded with ground weld reinforcement(steel) or debeading (PE), Cement mortar jointed e.g. concrete, Electrofusion coupling weld (PE only), Flange jointedLLap fillet weld (steel only), Mechanical coupling, JRubber ring (elastomeric seal) jointed (socket and spigot or joint coupling/collar), Solvent cement jointed e.g. PVC, ABS", "created_at": "2023-07-05T06:37:17.443Z"}}, {"model": "defects.standardheader", "pk": "f2be9c36-ad12-4c9f-b585-99d64eb001c4", "fields": {"header": "f7e2642d-09a6-48fb-a2b6-7e9c12382b4f", "standard": 4, "name": "AssetID", "code": "AAA", "required": true, "shown_by_default": true, "description": "Unique asset identification number as supplied by the asset owner, or generated by the Contractor if the inspected pipe is a new asset.", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:16.787Z"}}, {"model": "defects.standardheader", "pk": "f2f195b0-9e12-4fb9-8064-fcca5809497a", "fields": {"header": "bb504a1a-005f-4654-b472-022db3564086", "standard": 3, "name": "Up Grade to Invert", "code": "Up_Grade_to_Invert", "required": false, "shown_by_default": false, "description": "", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.398Z"}}, {"model": "defects.standardheader", "pk": "f4e34d14-36ad-40d0-9d50-27ba22673c6a", "fields": {"header": "78e11417-7d59-49b7-8d18-dbd7be4ae842", "standard": 3, "name": "Custom Field twelve", "code": "Custom_Field_twelve", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.660Z"}}, {"model": "defects.standardheader", "pk": "f6a3c9ed-cc6e-46c5-80e7-b827c2d14857", "fields": {"header": "0ddabd9a-a8a7-4e16-80c0-dd2b824437d4", "standard": 1, "name": "GIS Length", "code": "ABQ", "required": false, "shown_by_default": false, "description": "The anticipated length of the inspection as advised by the asset owner.", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.794Z"}}, {"model": "defects.standardheader", "pk": "f7840e8d-be20-4be6-a3e5-b6efe9b23fbf", "fields": {"header": "1b2573b0-df4a-4f9b-937f-b7394af581da", "standard": 1, "name": "Purpose of inspection", "code": "ABP", "required": false, "shown_by_default": false, "description": "Record the purpose of the inspection", "data_type": "options", "options_selections": "B, C, IE, IP, L, NC, OE, R, RC, S, SE, T, W, Z", "options_description": "Inspection before and after building works over or near the conduit, Completion of an earlier abandoned inspection, Suspected infiltration problem (Infiltration exam), Investment planning, Locating a conduit, connection or a maintenance structure, Final control of new construction, Suspected operational problem (Operational exam), Routine inspection of condition, Final control of renovation or repair (Renovation/repair control), Sample inspection, Suspected structural problem (Structural exam), Transfer of ownership, End of warranty period, Other—the reason shall be recorded as a header remark (code ADE) immediately following", "created_at": "2023-07-05T06:37:18.079Z"}}, {"model": "defects.standardheader", "pk": "f7cf7881-cf35-453f-a2cb-07a8be772903", "fields": {"header": "253243a9-ef6a-4ba6-8d48-54e164b20cda", "standard": 1, "name": "Criticality", "code": "ACL", "required": false, "shown_by_default": false, "description": "A client defined alpha-numeric code describing how critical the conduit is to the system", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.820Z"}}, {"model": "defects.standardheader", "pk": "f84fe23a-6e55-472f-a009-7c2a362ce934", "fields": {"header": "aeb13168-2b5a-4a21-828f-c2c14479b08b", "standard": 1, "name": "Method of inspection", "code": "ABE", "required": false, "shown_by_default": false, "description": "Record the method used to access the conduit for inspection", "data_type": "options", "options_selections": "FZ, LP, M, PR, PS, SS, TV, Z", "options_description": "Inspection by means of a fixed position zoom pipeline camera, Inspection by means of a remotely controlled laser profiler passed through the conduit, Direct inspection of a conduit by a person walking through the conduit (Mann<PERSON>), Push rod, Inspection by means of a remotely controlled 3D optical pipeline scanner passed through the conduit, Inspection by means of a remotely controlled sonar scanner passed through the conduit, Inspection by means of a remotely controlled television camera passed through the conduit, Other", "created_at": "2023-07-05T06:37:17.960Z"}}, {"model": "defects.standardheader", "pk": "fa1a51d7-004b-48f2-ac2f-6f9ef6566350", "fields": {"header": "58a9f101-0387-4847-9f27-6185b77061dc", "standard": 3, "name": "Custom Field Nine", "code": "Custom_Field_nine", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.620Z"}}, {"model": "defects.standardheader", "pk": "fa8053e5-15fa-4da9-bbae-648ca14a7648", "fields": {"header": "8c4f4f6a-356a-49c6-9e15-e90c7e590e64", "standard": 3, "name": "Up Northing", "code": "Up_Northing", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:12.828Z"}}, {"model": "defects.standardheader", "pk": "fafadabb-732c-4085-a4cb-7a15e2bbfbb9", "fields": {"header": "69db29d1-c1a1-4852-96b6-2fe06573d25b", "standard": 3, "name": "Custom Field eleven", "code": "Custom_Field_eleven", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.646Z"}}, {"model": "defects.standardheader", "pk": "fbc61f2f-3292-4075-be81-b14f42f3d6be", "fields": {"header": "46c5e176-f46b-49f1-b9ae-31614d5d58ee", "standard": 6, "name": "Cleaning", "code": "ACM", "required": false, "shown_by_default": false, "description": "Record whether the conduit was cleaned prior to the inspection", "data_type": "options", "options_selections": "C, NC", "options_description": "The conduit was cleaned prior to the inspection, The conduit was not cleaned prior to the inspection", "created_at": "2023-07-05T06:37:16.596Z"}}, {"model": "defects.standardheader", "pk": "fbd7c659-f8da-4944-a1f1-be36a3972a0a", "fields": {"header": "63947516-527b-4feb-bf0c-614a2a052db6", "standard": 3, "name": "Custom Field seventeen", "code": "Custom_Field_seventeen", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.736Z"}}, {"model": "defects.standardheader", "pk": "fbf63b0e-55a7-4c98-811c-cd31aecd4d8a", "fields": {"header": "8eb92eff-07b2-41d4-83c7-88630758ea66", "standard": 1, "name": "Depth at downstream node", "code": "ACI", "required": false, "shown_by_default": false, "description": "Record the depth of the invert of the conduit below cover level at the downstream node in m", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.807Z"}}, {"model": "defects.standardheader", "pk": "fc823fa3-03b3-4e74-85df-2a44db7985d5", "fields": {"header": "184c5759-9d80-4207-be0f-c3f6b90be55c", "standard": 4, "name": "Height", "code": "ACB", "required": false, "shown_by_default": true, "description": "The height of the section in mm (##), not required where both dimensions are the same e.g. circular", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:17.272Z"}}, {"model": "defects.standardheader", "pk": "fc91fe08-448e-4f17-887a-af975e08c4ca", "fields": {"header": "be75d367-8ac8-457a-a8ff-2c0221f8cb5c", "standard": 2, "name": "Client Defined 19", "code": "ClientDefined19", "required": false, "shown_by_default": false, "description": "", "data_type": "string", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:15.002Z"}}, {"model": "defects.standardheader", "pk": "ff5ef8e1-9211-4059-b319-902647608234", "fields": {"header": "e26942f7-d18e-4e19-91d7-0be761a9e334", "standard": 5, "name": "Pipe Unit Length", "code": "PipeUnitLength", "required": false, "shown_by_default": false, "description": "Length of the pipe asset", "data_type": "number", "options_selections": [], "options_description": null, "created_at": "2023-11-09T07:01:01.013Z"}}, {"model": "defects.standardheader", "pk": "fffe22f6-ff74-4c1f-93ca-e3a692c91fde", "fields": {"header": "ada7c8c2-3901-42bd-896d-146e0bd5e439", "standard": 3, "name": "Down Grade to Invert", "code": "Down_Grade_to_Invert", "required": false, "shown_by_default": false, "description": "", "data_type": "number", "options_selections": "", "options_description": "", "created_at": "2023-07-05T06:37:13.443Z"}}]