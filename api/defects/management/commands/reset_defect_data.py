from pathlib import Path

from django.core.management.base import BaseCommand
from django.db import transaction, connection
import pandas as pd

from api.defects.models import DefectModelList, DefectScores


MANAGEMENT_FOLDER = Path(__file__).parent
DATA_FOLDER = MANAGEMENT_FOLDER.parent / "baseline_data"


def drop_contraints():
    # remove FK constraints that will prevent defectscores table from being truncated.
    cursor = connection.cursor()

    # defect scores FK constraints
    sql = """ALTER TABLE service_videoframes
    DROP CONSTRAINT service_videoframes_defect_scores_id_27589025_fk_service_d"""
    cursor.execute(sql)

    # defect modellist FK constraints
    sql = """ALTER TABLE service_videoframes
    DROP CONSTRAINT service_videoframes_defect_model_id_a54ab743_fk_service_d"""
    cursor.execute(sql)
    sql = """ALTER TABLE service_defectscores
    DROP CONSTRAINT service_defectscores_defect_key_id_b33870df_fk_service_d"""
    cursor.execute(sql)


def truncate_defect_tables():
    # truncate tables
    cursor = connection.cursor()
    cursor.execute("TRUNCATE TABLE service_defectscores")
    cursor.execute("TRUNCATE TABLE service_defectmodellist")


def insert_baseline_data():
    # load baseline production defectscores data
    cursor = connection.cursor()

    columns = (
        "id",
        "defect_description",
        "defect_type",
        "defect_code",
        "defect_score",
        "defect_key_id",
        "standard_key_id",
        "service_score",
        "structural_score",
        "quantity_units",
        "quantity_value",
        "is_shown",
        "characterisation_1",
        "characterisation_2",
        "continuous_score",
        "main_category",
        "material_applied",
        "quantity_1_default_val",
        "quantity_1_desc",
        "quantity_1_end_val",
        "quantity_1_score_type",
        "quantity_1_start_val",
        "quantity_1_units",
        "quantity_2_default_val",
        "quantity_2_desc",
        "quantity_2_end_val",
        "quantity_2_score_type",
        "quantity_2_start_val",
        "quantity_2_units",
        "sub_standard_id",
        "repair_category",
        "repair_priority",
        "at_joint_required",
        "clock_position_required",
        "clock_spread_possible",
        "end_survey",
        "fastpass_code",
        "percentage_required",
        "start_survey",
    )
    data_source = DATA_FOLDER / "defectscores.csv"
    with data_source.open() as fp:
        cursor.copy_from(fp, "service_defectscores", sep=",", null="", columns=columns)

    data_source = DATA_FOLDER / "defectmodellist.csv"
    with data_source.open() as fp:
        cursor.copy_from(fp, "service_defectmodellist", sep=",", null="")


def update_existing_records():
    # use alignment dataset to update existing records
    records = pd.read_csv(DATA_FOLDER / "service_defectscores_realignment.csv")
    # only updating existing records
    records.dropna(subset=["id"], inplace=True)
    records["id"] = records["id"].astype(int)

    for _, record in records.iterrows():
        defectscore = DefectScores.objects.get(pk=record["id"])
        defectscore.defect_key = DefectModelList.objects.get(pk=record["defect_key_id"])
        defectscore.save()


def reset_seqeuence_ids():
    # reset the sequence id so that new records can be added
    cursor = connection.cursor()
    cursor.execute(
        """SELECT setval(pg_get_serial_sequence('service_defectscores', 'id'), coalesce(MAX(id), 1))
        FROM service_defectscores"""
    )
    cursor.execute(
        """SELECT setval(pg_get_serial_sequence('service_defectmodellist', 'id'), coalesce(MAX(id), 1))
        FROM service_defectmodellist"""
    )


def null_missing_frame_references():
    # set null frame references where defectscore_id is no longer valid
    cursor = connection.cursor()
    cursor.execute(
        """UPDATE service_videoframes 
        SET defect_scores_id = null
        WHERE defect_scores_id not in (
            SELECT id from service_defectscores
        )"""
    )
    cursor.execute(
        """UPDATE service_videoframes 
        SET defect_model_id = null
        WHERE defect_model_id not in (
            SELECT id from service_defectmodellist
        )"""
    )


def apply_constraints():
    # restore the FK constraints to the defect scores table
    cursor = connection.cursor()

    sql = """ALTER TABLE service_videoframes
    ADD CONSTRAINT service_videoframes_defect_scores_id_27589025_fk_service_d
    FOREIGN KEY (defect_scores_id) 
    REFERENCES service_defectscores(id)
    DEFERRABLE INITIALLY DEFERRED"""
    cursor.execute(sql)

    sql = """ALTER TABLE service_defectscores 
    ADD CONSTRAINT service_defectscores_defect_key_id_b33870df_fk_service_d 
    FOREIGN KEY (defect_key_id) 
    REFERENCES service_defectmodellist(id) 
    DEFERRABLE INITIALLY DEFERRED"""
    cursor.execute(sql)

    sql = """ALTER TABLE service_videoframes 
    ADD CONSTRAINT "service_videoframes_defect_model_id_a54ab743_fk_service_d" 
    FOREIGN KEY (defect_model_id) 
    REFERENCES service_defectmodellist(id) 
    DEFERRABLE INITIALLY DEFERRED"""
    cursor.execute(sql)


class Command(BaseCommand):
    help = "Reset defect modellist and scores table to a baseline dataset."

    def add_arguments(self, parser):
        # parser.add_argument("dry_run", type=bool)
        pass

    def handle(self, *args, **options):
        """Handle the command execution."""

        with transaction.atomic():
            drop_contraints()
            truncate_defect_tables()
            insert_baseline_data()
            reset_seqeuence_ids()
            # re-align the defectscores table with defect modellist
            update_existing_records()
            # only require for dev/uat
            null_missing_frame_references()
        # constraints applied outside transaction due to update triggers (from null missing frame refs)
        apply_constraints()
