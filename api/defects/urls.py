from django.urls import path

from api.defects.views import StandardList, StandardHeaderList, StandardHeaderCsv, AllDefectsList, DefectList, StandardSubcategoryList

urlpatterns = [
    path("standards/headers", StandardHeaderList.as_view(), name="standard_header"),
    path("standards/headers/csv-template", StandardHeaderCsv.as_view(), name="standard_headers_csv"),
    path("standards/defects", AllDefectsList.as_view(), name="all_defects_list"),
    path("standards/defects/<int:inspection_id>", DefectList.as_view(), name="defect_list"),
    path("standards", StandardList.as_view(), name="standard_list"),
    path("standards/subcategories", StandardSubcategoryList.as_view(), name="standard_subcategory_list"),
]
