import uuid
from django.db import models
from django.db.models import OuterRef, Case, When, Value, Subquery, ExpressionWrapper, F
from django.db.models.functions import Coalesce, Cast
from django_countries.fields import CountryField

from api.base.models import Header


def default_repair_param():
    return {
        "Minimum roots class": 15,
        "Debris build up class": 15,
        "Debris build up length m": 1,
        "Debris single instance class": 20,
        "Patch if score over length >=": 50,
        "Patch length for scoring m": 1,
        "Maximum number of patches over distance": 2,
        "Maximum distance for patch": 30,
        "Maximum number of patches in total": 3,
    }


class Standard(models.Model):
    name = models.CharField(max_length=200, blank=False)
    display_name = models.CharField(max_length=200, blank=False, default="Australia 2020")
    default_repair_param = models.JSONField(blank=True, null=True, default=default_repair_param)

    def __str__(self):
        return str(self.display_name)

    class Meta:
        db_table = "service_scoringstandardslist"


class StandardSubcategory(models.Model):
    standard_key = models.ForeignKey(Standard, on_delete=models.SET_NULL, null=True)
    pipe_type_sewer = models.BooleanField(null=True, blank=True)
    material_type = models.CharField(max_length=100, blank=True, null=True)
    region = CountryField(blank=True, null=True)
    comment = models.CharField(max_length=200, blank=True)

    def __str__(self):
        return str(self.comment)

    class Meta:
        db_table = "service_standard_subcategory"


class _StandardHeaderDataType(models.TextChoices):
    NUMBER = "number", "Number"
    STRING = "string", "String"
    BOOLEAN = "boolean", "Boolean"
    OPTIONS = "options", "Options"
    DATE = "date", "Date"


class StandardHeader(models.Model):
    DataType = _StandardHeaderDataType

    uuid = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    header = models.ForeignKey(Header, on_delete=models.CASCADE)
    standard = models.ForeignKey(Standard, on_delete=models.CASCADE)
    name = models.CharField(max_length=50)
    code = models.CharField(max_length=50, null=True)
    required = models.BooleanField()
    shown_by_default = models.BooleanField()
    description = models.TextField(blank=True, null=True)
    data_type = models.CharField(max_length=7, choices=DataType.choices, null=False)
    options_selections = models.JSONField(blank=True, null=False)
    options_description = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=False)

    def get_mapped_mpl_field(self):
        return self.header.mapped_mpl_field

    class Meta:
        db_table = "inspections_standardheader"
        constraints = [
            models.CheckConstraint(
                check=models.Q(data_type__in=list(_StandardHeaderDataType)),
                name="standard_header_data_type_allowed_constraint",
            )
        ]


class ScoreSeverity(models.Model):
    scoring_types = [("structural", "Structural"), ("service", "Service")]

    severity_levels = [("low", "Low"), ("medium", "Medium"), ("high", "High")]

    type = models.CharField(max_length=10, choices=scoring_types)
    standard = models.ForeignKey(Standard, on_delete=models.CASCADE, null=True)
    severity = models.CharField(max_length=6, choices=severity_levels, null=True)
    min_score = models.IntegerField()
    max_score = models.IntegerField()


class DefectModelList(models.Model):
    name = models.CharField(max_length=100, blank=False)
    defect_model = models.CharField(max_length=100, blank=False, default="SS")

    class Meta:
        db_table = "service_defectmodellist"


class DefectScoreQueryset(models.QuerySet):
    def with_severity(self):
        """
        Annotate the queryset with 'severity' - a str describing the severity level of the defect score
        """

        # Find a matching severity record for the correct standard, defect type, and score range
        severity_subquery = (
            ScoreSeverity.objects.annotate(
                curr_defect_type=ExpressionWrapper(OuterRef("defect_type"), output_field=models.CharField())
            )
            .annotate(
                severity_type=Case(
                    When(curr_defect_type="ser", then=Value("service")),
                    When(curr_defect_type="str", then=Value("structural")),
                    default=Value(None),
                    output_field=models.CharField(),
                )
            )
            .filter(
                standard=OuterRef("standard_key__id"),
                type=F("severity_type"),
                min_score__lte=Cast(
                    Case(
                        When(curr_defect_type="ser", then=OuterRef("service_score")),
                        When(curr_defect_type="str", then=OuterRef("structural_score")),
                        default=Value(None),
                    ),
                    output_field=models.IntegerField(),
                ),
                max_score__gte=Cast(
                    Case(
                        When(curr_defect_type="ser", then=OuterRef("service_score")),
                        When(curr_defect_type="str", then=OuterRef("structural_score")),
                        default=Value(None),
                    ),
                    output_field=models.IntegerField(),
                ),
            )
            .values("severity")[:1]
        )

        return self.annotate(severity=Coalesce(Subquery(severity_subquery), Value("none")))


class DefectScores(models.Model):
    REPAIR_CHOICES = [
        ("Roots", "Roots"),
        ("Debris", "Debris"),
        ("Digup", "Digup"),
    ]

    standard_key = models.ForeignKey(
        Standard,
        on_delete=models.CASCADE,
    )
    sub_standard = models.ForeignKey(StandardSubcategory, on_delete=models.SET_NULL, null=True)
    defect_key = models.ForeignKey(DefectModelList, on_delete=models.CASCADE, null=True)
    defect_description = models.CharField(max_length=200, blank=False)
    defect_type = models.CharField(max_length=100, blank=False, default="structural")
    defect_code = models.CharField(max_length=10, blank=False, null=True)
    defect_score = models.CharField(max_length=200, blank=False, default=0)
    structural_score = models.CharField(max_length=200, blank=False)
    service_score = models.CharField(max_length=200, blank=False)
    quantity_value = models.DecimalField(blank=True, null=True, default=None, max_digits=10, decimal_places=2)
    quantity_units = models.CharField(max_length=200, blank=True, default=None, null=True)
    is_shown = models.BooleanField(default=True)
    main_category = models.CharField(max_length=200, blank=True)
    characterisation_1 = models.CharField(max_length=200, blank=True, null=True)
    characterisation_2 = models.CharField(max_length=200, blank=True, null=True)
    quantity_1_desc = models.CharField(max_length=200, blank=True, null=True)
    quantity_1_units = models.CharField(max_length=200, blank=True, null=True)
    quantity_1_start_val = models.FloatField(max_length=10, default=0.0, blank=True, null=True)
    quantity_1_end_val = models.FloatField(max_length=10, default=0.0, blank=True, null=True)
    quantity_1_default_val = models.FloatField(max_length=10, default=0.0, blank=True, null=True)
    quantity_1_score_type = models.CharField(max_length=200, blank=True, null=True)
    quantity_2_desc = models.CharField(max_length=200, blank=True, null=True)
    quantity_2_units = models.CharField(max_length=200, blank=True, null=True)
    quantity_2_start_val = models.FloatField(max_length=10, default=0.0, blank=True, null=True)
    quantity_2_end_val = models.FloatField(max_length=10, default=0.0, blank=True, null=True)
    quantity_2_default_val = models.FloatField(max_length=10, default=0.0, blank=True, null=True)
    quantity_2_score_type = models.CharField(max_length=200, blank=True, null=True)
    clock_position_required = models.BooleanField(default=False)
    clock_spread_possible = models.BooleanField(default=False)
    at_joint_required = models.BooleanField(default=False)
    percentage_required = models.BooleanField(default=False)
    start_survey = models.BooleanField(default=False)
    end_survey = models.BooleanField(default=False)
    fastpass_code = models.BooleanField(default=False)
    continuous_score = models.BooleanField(default=False)
    material_applied = models.CharField(max_length=200, blank=True, null=True)
    repair_category = models.CharField(max_length=20, blank=True, null=True, choices=REPAIR_CHOICES)
    repair_priority = models.IntegerField(blank=True, null=True, default=-1)

    objects = DefectScoreQueryset.as_manager()

    def __str__(self):
        return str(self.defect_description)

    def validate(self):
        pass

    @property
    def defect_score_severity(self):
        # Fetch from the annotated 'severity' field if available, else recalculate
        if hasattr(self, "severity"):
            return self.severity
        return DefectScores.objects.with_severity().filter(pk=self.pk).first().severity

    class Meta:
        db_table = "service_defectscores"
