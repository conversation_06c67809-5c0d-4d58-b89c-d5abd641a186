from abc import ABC
from typing import Dict, Generator, List, Union
from uuid import uuid4

from api.common.errors import CustomValidationError
from api.common.validation_util import is_boolean, is_float, is_option, is_string
from api.defects.models import StandardHeader
from api.inspections.models import (
    AssetValue,
    Inspection,
    InspectionValue,
    VideoFrames,
)
from django.conf import settings
from django.db.models import QuerySet
from vapar.clients import nassco_validator as nv


StandardValue = Union[InspectionValue, AssetValue]


class BaseStandard(ABC):
    name: str = ""
    headers: List[StandardHeader] = []

    def __init__(self) -> None:
        self.headers = StandardHeader.objects.filter(standard__name=self.name)

    def _check_required(self, values: List[StandardValue]) -> Generator[CustomValidationError, None, None]:
        # Check for missing required fields
        required = list(filter(lambda x: x.required, self.headers))
        value_lookup: Dict[str, StandardValue] = {value.standard_header.name: value for value in values}
        # as there is currently no way to distinguish inspection headers from asset headers
        # we have to reduce the list of required to the headers passed via `values`
        # TODO: distinguish StandardHeaders by entity type.
        value_headers = [value.standard_header for value in values]
        required = set().intersection(value_headers, required)
        for header in required:
            standard_value = value_lookup[header.name]
            if not standard_value.value:
                entity_type = "inspection" if isinstance(standard_value, InspectionValue) else "asset"
                custom_message = None
                if header.options_selections:
                    custom_message = f"'{header.name}' value missing - valid options are ({header.options_selections})"
                yield CustomValidationError.generate_missing(
                    header.name, entity=entity_type, custom_message=custom_message
                )

    def validate_standard_value(self, standard_value: StandardValue) -> Union[CustomValidationError, None]:
        # Validate inspection value aligns with Standard
        standard_header = standard_value.standard_header

        standard_value_type = "inspection" if isinstance(standard_value, InspectionValue) else "asset"
        # ensure standard header exists in standard
        if standard_header not in self.headers:
            return CustomValidationError(
                title=standard_header.name,
                message=f"{standard_header.name} is not a valid {standard_value_type} value for {self.name} Standard",
                field_name=standard_value.standard_header.name,
                entity=standard_value_type,
            ).serialize()

        # ensure the correct data type
        if any(
            (
                (standard_header.data_type == "date" and not is_string(standard_value.value)),
                (standard_header.data_type == "string" and not is_string(standard_value.value)),
                (standard_header.data_type == "number" and not is_float(standard_value.value)),
                (standard_header.data_type == "boolean" and not is_boolean(standard_value.value)),
                (
                    standard_header.data_type == "options"
                    and (
                        not is_option(standard_value.value, standard_header.options_selections)
                        and not is_option(standard_value.value, standard_header.header.options)
                    )
                ),
            )
        ):
            data_type_str = standard_value.standard_header.data_type

            if standard_value.standard_header.data_type == "options":
                data_type_str = f"one of the following: {standard_value.standard_header.options_selections}"

            message = (
                f"{standard_value.value} is an invalid value for this field. Expected data type is {data_type_str}"
            )

            return CustomValidationError(
                title=standard_value.standard_header.name,
                message=message,
                field_name=standard_value.standard_header.name,
                entity=standard_value_type,
            ).serialize()

        return None

    def validate_inspection(self, inspection: Inspection) -> List[CustomValidationError]:
        inspection_values = InspectionValue.objects.filter(inspection=inspection).select_related("standard_header")
        errors = list(self._check_required(inspection_values))
        # filter out non-errors after validating all inspection values
        errors.extend(filter(None, map(self.validate_standard_value, inspection_values)))
        return errors

    def validate_asset(self, **kwargs):
        # asset = kwargs.get("asset")
        asset_values = kwargs.get("asset_values")
        # Check for missing required fields
        errors = list(self._check_required(asset_values))
        # filter out non-errors after validating all inspection values
        errors.extend(filter(None, map(self.validate_standard_value, asset_values)))
        return errors

    def validate_frames(self, **kwargs):
        frames = kwargs.get("frames")
        errors = []
        for frame in frames:
            errors.extend(frame.validate())
        return errors


class MSCC5(BaseStandard):
    name: str = "MSCC5"

    def check_first_frame_chainage(self, **kwargs):
        frames = kwargs.get("frames")
        first_frame = frames.first()
        errors = []
        if first_frame.chainage_number != 0:
            errors.append(
                CustomValidationError(
                    title="First Frame Chainage Length",
                    message="First frame must have a chainage length of 0",
                    field_name="chainage",
                    entity="defect",
                    metadata={"frame_id": first_frame.id},
                ).serialize()
            )
        return errors

    def check_first_frame_defect_code(self, **kwargs):
        frames = kwargs.get("frames")
        first_frame = frames.first()
        errors = []
        start_node_types = ["BN", "CP", "GY", "IC", "LH", "BR", "MH", "OS", "OC", "OF", "RE", "SK", "WR"]
        if first_frame.defect_scores.defect_code not in start_node_types:
            errors.append(
                CustomValidationError(
                    title="First Frame Defect Code",
                    message="First frame must be a 'Start Node Type' Code",
                    field_name="defect_code",
                    entity="defect",
                    metadata={"frame_id": first_frame.id},
                ).serialize()
            )
        return errors

    def check_last_frame_chainage(self, **kwargs):
        frames = kwargs.get("frames")
        last_frame = frames.last()
        errors = []
        if last_frame.chainage_number != max([frame.chainage_number for frame in frames]):
            errors.append(
                CustomValidationError(
                    title="Last Frame Chainage Length",
                    message="Last frame must have the maximum chainage length",
                    field_name="chainage",
                    entity="defect",
                    metadata={"frame_id": last_frame.id},
                ).serialize()
            )
        return errors

    def check_last_frame_defect_code(self, **kwargs):
        frames = kwargs.get("frames")
        last_frame = frames.last()
        errors = []
        finish_node_types = [
            "BNF",
            "CPF",
            "GYF",
            "ICF",
            "LHF",
            "BRF",
            "MHF",
            "OSF",
            "OCF",
            "OFF",
            "REF",
            "SKF",
            "WRF",
            "SA",
        ]
        if last_frame.defect_scores.defect_code not in finish_node_types:
            errors.append(
                CustomValidationError(
                    title="Last Frame Defect Code",
                    message="Last frame must be a 'Finish Node Type' Code",
                    field_name="defect_code",
                    entity="defect",
                    metadata={"frame_id": last_frame.id},
                ).serialize()
            )
        return errors

    def validate_frames(self, **kwargs):
        frames = kwargs.get("frames")
        errors = []
        errors.extend(self.check_first_frame_chainage(frames=frames))
        errors.extend(self.check_first_frame_defect_code(frames=frames))
        errors.extend(self.check_last_frame_chainage(frames=frames))
        errors.extend(self.check_last_frame_defect_code(frames=frames))
        errors.extend(super().validate_frames(frames=frames))
        return errors


class NZPipeManual3(BaseStandard):
    name: str = "NZ Pipe Manual 3"

    def validate_frames(self, **kwargs):
        return super().validate_frames(**kwargs)


class NZPipeManual4(BaseStandard):
    name: str = "NZ Pipe Manual 4"

    def validate_frames(self, **kwargs):
        return super().validate_frames(**kwargs)


class PACP7(BaseStandard):
    name: str = "PACP7"

    def validate_inspection(self, inspection: Inspection):
        errors = super().validate_inspection(inspection)

        try:
            frames = VideoFrames.objects.filter(
                parent_video=inspection.file,
                is_hidden=False,
                defect_scores__isnull=False,
            )
        except Exception:
            frames = []

        # Check continuous defect start and end values
        for frame in frames:
            if frame.cont_defect_start and frame.cont_defect_end is None:
                error = CustomValidationError(
                    title="Continuous defect end",
                    message="Continuous defect end value required",
                    field_name="continuous_defect_end",
                    entity="defect",
                    metadata={"frame_id": frame.id},
                )
                errors.append(error.serialize())

        return errors

    def validate_frames(self, **kwargs):
        return super().validate_frames(**kwargs)


class PACP8(BaseStandard):
    name: str = "PACP8"

    def _create_pacp_rating(self, inspection: Inspection) -> nv.PACPRating:
        # We don't yet have code to calculate all of the different PACP rating types
        # The only fields that are non-optional and don't have a default are the two ID fields above
        return nv.PACPRating(RatingID=str(uuid4()), InspectionID=str(inspection.uuid))

    def _create_pacp_conditions(self, inspection: Inspection, frames: QuerySet[VideoFrames]) -> list[nv.PACPCondition]:
        conditions = []

        for frame in frames:
            pacp_data = {
                "ConditionID": str(uuid4()),
                "InspectionID": str(inspection.uuid),
                "Distance": frame.chainage,
                "Counter": frame.time_reference,
                "Continuous": str(frame.cont_defect_end) if frame.cont_defect_start else None,
                "Value_1st_Dimension": str(frame.quantity1_value),
                "Value_2nd_Dimension": str(frame.quantity2_value),
                "Joint": 1 if frame.at_joint else 0,
                "Clock_At_From": frame.at_clock,
                "Clock_To": frame.to_clock,
                "Remarks": frame.remarks,
                "VCR_Time": frame.review_timestamp.strftime("%H%M%S") if frame.review_timestamp else "",
            }
            if frame.defect_scores.standard_key.name == self.name:
                pacp_data["PACP_Code"] = frame.defect_scores.defect_code
            if frame.defect_scores.percentage_required:
                pacp_data["Value_Percent"] = frame.defect_scores.quantity_value

            conditions.append(nv.PACPCondition(**pacp_data))

        return conditions

    def _create_pacp_inspection(self, inspection: Inspection) -> nv.PACPInspection:
        pacp_data = {"InspectionID": str(inspection.uuid)}
        inspection_values = inspection.get_inspection_values()

        for iv in inspection_values:
            sh = iv.standard_header
            if sh.standard.name == self.name:
                # Direct mapping where the code matches the alias
                if sh.code in [f.alias for f in nv.PACPInspection.model_fields.values()]:
                    pacp_data[sh.code] = iv.value
                # Manual mapping for where there are differences
                elif sh.code == "Inspection_Technology_Used":
                    itu_map = [
                        ("CC", "Inspection_Technology_Used_CCTV"),
                        ("LA", "Inspection_Technology_Used_Laser"),
                        ("SO", "Inspection_Technology_Used_Sonar"),
                        ("SS", "Inspection_Technology_Used_Sidewall"),
                        ("ZM", "Inspection_Technology_Used_Zoom"),
                        ("ZZ", "Inspection_Technology_Used_Other"),
                    ]
                    for itu_value, itu_field in itu_map:
                        pacp_data[itu_field] = 1 if iv.value == itu_value else 0

                # IsImperial should be bool-as-integer, whereas we have bool-as-string, so we convert
                if sh.name == "Is Imperial" and isinstance(iv.value, str):
                    pacp_data["IsImperial"] = 1 if iv.value == "True" else 0

        return nv.PACPInspection(**pacp_data)

    def validate_inspection(self, inspection: Inspection):
        frames = VideoFrames.objects.filter(
            parent_video=inspection.file,
            is_hidden=False,
            defect_scores__isnull=False,
        ).select_related("defect_scores")

        pacp_inspection = self._create_pacp_inspection(inspection)
        pacp_conditions = self._create_pacp_conditions(inspection, frames)
        pacp_rating = self._create_pacp_rating(inspection)

        payload = nv.NASSCOValidatorPayload(
            pacp_inspections=[pacp_inspection], pacp_conditions=pacp_conditions, pacp_ratings=[pacp_rating]
        )

        nv_client = nv.NASSCOValidatorClient(executable_path=settings.NASSCO_VALIDATOR_CLIENT_EXECUTABLE_PATH)
        result = nv_client.validate(payload)

        filtered_errors = self._convert_validation_errors(result)
        return filtered_errors

    def _convert_validation_errors(self, result: nv.NASSCOValidationResult) -> list[dict]:
        errors = []
        for e in result.validation_errors:
            standard_header_exists = StandardHeader.objects.filter(
                code=e.field, standard__name=self.name, header__is_editable=True
            ).exists()
            if not standard_header_exists:
                continue  # Either doesn't correspond to a standard header or is not editable by the user

            error = CustomValidationError(
                title=e.value, message=e.message, field_name=e.field, entity="inspection", metadata=e.code
            )
            errors.append(error.serialize())

        return errors

    def validate_frames(self, **kwargs):
        return super().validate_frames(**kwargs)


class WSA052013(BaseStandard):
    name: str = "WSA-05 2013"

    def validate_frames(self, **kwargs):
        return super().validate_frames(**kwargs)


class WSA052020(BaseStandard):
    name: str = "WSA-05 2020"

    def validate_frames(self, **kwargs):
        return super().validate_frames(**kwargs)


class JapanVS(BaseStandard):
    name: str = "Japan VS"

    def validate_frames(self, **kwargs):
        return super().validate_frames(**kwargs)


STANDARD_NAME_TO_VALIDATION_CLASS = {
    "MSCC5": MSCC5,
    "NZ Pipe Manual 3": NZPipeManual3,
    "NZ Pipe Manual 4": NZPipeManual4,
    "PACP7": PACP7,
    "PACP8": PACP8,
    "WSA-05 2013": WSA052013,
    "WSA-05 2020": WSA052020,
    "Japan VS": JapanVS,
}


def get_standard_instance(standard_name: str) -> BaseStandard:
    standard_class = STANDARD_NAME_TO_VALIDATION_CLASS[standard_name]
    return standard_class()
