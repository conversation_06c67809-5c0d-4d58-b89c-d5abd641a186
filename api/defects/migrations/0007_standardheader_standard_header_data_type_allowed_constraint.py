# Generated by Django 4.1.2 on 2024-05-02 04:30

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("defects", "0006_options_selections_to_array"),
    ]

    operations = [
        migrations.AddConstraint(
            model_name="standardheader",
            constraint=models.CheckConstraint(
                check=models.Q(
                    (
                        "data_type__in",
                        ["number", "string", "boolean", "options", "date"],
                    )
                ),
                name="standard_header_data_type_allowed_constraint",
            ),
        ),
    ]
