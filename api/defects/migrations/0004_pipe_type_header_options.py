from django.db import migrations


def add_pipe_type_header_options(apps, schema_editor):
    Header = apps.get_model("base", "Header")
    pipe_type_header = Header.objects.filter(name="UseOfDrainSewer")
    pipe_type_header.update(
        data_type="options",
        options=["SS", "SW"],
        is_editable=True,
    )


def remove_pipe_type_header_options(apps, schema_editior):
    Header = apps.get_model("base", "Header")
    pipe_type_header = Header.objects.filter(name="UseOfDrainSewer")
    pipe_type_header.update(
        data_type="options",
        options=[],
        is_editable=False,
    )


class Migration(migrations.Migration):
    dependencies = [
        ("defects", "0003_defect_label_additions"),
    ]

    operations = [
        migrations.RunPython(code=add_pipe_type_header_options, reverse_code=remove_pipe_type_header_options),
    ]
