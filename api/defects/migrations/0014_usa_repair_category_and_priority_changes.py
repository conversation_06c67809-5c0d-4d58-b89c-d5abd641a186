from pathlib import Path

import pandas as pd
from django.db import migrations

MIGRATIONS_FOLDER = Path(__file__).parent
DATA_FOLDER = MIGRATIONS_FOLDER.parent / "migrations_data" / "0014_usa_repair_category_and_priority_changes"


def update_repair_category_and_priority(apps, schema_editor):
    DefectScores = apps.get_model("defects", "DefectScores")

    filenames = list(DATA_FOLDER.iterdir())
    for filename in filenames:
        records = pd.read_csv(filename)
        for _, record in records.iterrows():
            ds = DefectScores.objects.filter(
                defect_description=record["defect_description"],
                defect_type=record["defect_type"],
                defect_code=record["defect_code"],
            ).first()
            ds.repair_category = record["repair_category"]
            ds.repair_priority = record["repair_priority"]
            ds.save()


class Migration(migrations.Migration):
    dependencies = [
        ("defects", "0013_auto_20240711_2339"),
    ]

    operations = [
        migrations.RunPython(update_repair_category_and_priority, migrations.RunPython.noop),
    ]
