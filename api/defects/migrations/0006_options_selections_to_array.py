from django.db import migrations


def replace_invalid_data_types(apps, schema_editor):
    StandardHeader = apps.get_model("defects", "StandardHeader")

    StandardHeader.objects.filter(data_type="list").update(data_type="options")
    StandardHeader.objects.filter(data_type="Street").update(data_type="string")


def convert_options_to_array(apps, schema_editor):
    StandardHeader = apps.get_model("defects", "StandardHeader")

    for header in StandardHeader.objects.all():
        old_opts = header.options_selections
        if isinstance(old_opts, str):
            new_opts = [x.strip() for x in old_opts.split(",")]
            header.options_selections = new_opts
            header.save()


class Migration(migrations.Migration):
    dependencies = [
        ("defects", "0005_defect_label_additions"),
    ]

    operations = [
        migrations.RunPython(code=replace_invalid_data_types, reverse_code=migrations.RunPython.noop),
        migrations.RunPython(code=convert_options_to_array, reverse_code=migrations.RunPython.noop),
    ]
