# Generated by Django 4.1.2 on 2024-07-12 02:52

from django.db import migrations


def update_defect_is_shown(apps, schema_editor):
    DefectScores = apps.get_model("defects", "DefectScores")

    defect = DefectScores.objects.filter(defect_code="LHMS").filter(
        defect_description="LHMS - Lifting hole - Manufactured - Sealed"
    )

    if len(defect) == 1:
        defect[0].is_shown = True
        defect[0].save()


def reverse_update_defect_is_shown(apps, schema_editor):
    DefectScores = apps.get_model("defects", "DefectScores")

    defect = DefectScores.objects.filter(defect_code="LHMS").filter(
        defect_description="LHMS - Lifting hole - Manufactured - Sealed"
    )

    if len(defect) == 1:
        defect[0].is_shown = False
        defect[0].save()


class Migration(migrations.Migration):

    dependencies = [
        ("defects", "0015_update_defect_keys"),
    ]

    operations = [migrations.RunPython(code=update_defect_is_shown, reverse_code=reverse_update_defect_is_shown)]
