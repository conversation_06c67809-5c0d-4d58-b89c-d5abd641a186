from pathlib import Path

import pandas as pd
from django.db import migrations

MIGRATIONS_FOLDER = Path(__file__).parent
DATA_FOLDER = MIGRATIONS_FOLDER.parent / "migrations_data" / "0022_usa_repair_category_and_priority_changes_add"


def update_repair_category_and_priority(apps, schema_editor):
    DefectScores = apps.get_model("defects", "DefectScores")

    filenames = list(DATA_FOLDER.iterdir())
    for filename in filenames:
        records = pd.read_excel(filename)
        for _, record in records.iterrows():
            DefectScores.objects.filter(
                defect_description=record["defect_description"],
                standard_key__id=3).update(
                    repair_category = record["repair_category"], 
                    repair_priority = record["repair_priority"])


class Migration(migrations.Migration):
    dependencies = [
        ("defects", "0021_more_csrq_defect_migrations"),
    ]

    operations = [
        migrations.RunPython(update_repair_category_and_priority, migrations.RunPython.noop),
    ]
