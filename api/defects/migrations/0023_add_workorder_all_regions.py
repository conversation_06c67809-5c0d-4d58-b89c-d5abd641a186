from django.db import migrations


def add_workorder_all_regions(apps, schema_editor):
    Header = apps.get_model("base", "Header")
    StandardHeader = apps.get_model("defects", "StandardHeader")
    Standard = apps.get_model("defects", "Standard")

    workorder_headers = Header.objects.filter(name="WorkOrder")
    if len(workorder_headers) == 0:
        Header.objects.create(
            name="WorkOrder", type="inspection", mapped_mpl_field="", is_editable=True, is_filterable=True
        )
    else:
        h = workorder_headers.first()
        if not h.is_editable or not h.is_filterable:
            h.is_editable, h.is_filterable = True, True
            h.save()

    for s in Standard.objects.all():
        if s.name not in ["PACP7", "PACP8"]:
            StandardHeader.objects.create(
                header=Header.objects.filter(name="WorkOrder").first(),
                standard=s,
                name="WorkOrder",
                code="WorkOrder",
                required=False,
                shown_by_default=False,
                data_type="string",
                options_selections="",
            )


class Migration(migrations.Migration):
    dependencies = [
        ("defects", "0022_usa_repair_category_and_priority_changes_add"),
    ]

    operations = [
        migrations.RunPython(add_workorder_all_regions, migrations.RunPython.noop),
    ]
