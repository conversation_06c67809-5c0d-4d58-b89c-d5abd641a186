# Generated by Django 4.1.2 on 2025-03-10 23:34

from django.db import migrations


ROOT_BALL_DEFECT_CODES = [
    "RBB",  # Ball
    "RBL",  # Lateral
    "RBB",  # <PERSON><PERSON>
    "RBC",  # Connection
    "RBJ",  # Joint
]


def set_root_ball_priority(apps, schema_editor):
    """
    Per PACP standards, root ball defects should have a repair priority of 55.
    """
    DefectScores = apps.get_model("defects", "DefectScores")
    DefectScores.objects.filter(
        standard_key__name__in=("PACP7", "PACP8"),
        defect_code__in=ROOT_BALL_DEFECT_CODES,
    ).update(repair_priority=55)


class Migration(migrations.Migration):
    dependencies = [
        ("defects", "0023_add_workorder_all_regions"),
    ]

    operations = [
        migrations.RunPython(set_root_ball_priority, reverse_code=migrations.RunPython.noop),
    ]
