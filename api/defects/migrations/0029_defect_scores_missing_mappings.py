# Generated by Django 4.1.2 on 2025-04-16 01:29
from pathlib import Path

import numpy as np
import pandas as pd
from django.db import migrations

MIGRATIONS_FOLDER = Path(__file__).parent
DATA_FOLDER = MIGRATIONS_FOLDER.parent / "migrations_data" / "0029_defect_scores_missing_mappings"
DATA_FILE = DATA_FOLDER / "defect_scores_entries.csv"


def add_missing_defect_scores(apps, schema_editor):
    DefectScores = apps.get_model("defects", "DefectScores")
    df = pd.read_csv(DATA_FILE).replace({np.nan: None})
    created = []
    for _, row in df.iterrows():
        created.append(
            DefectScores(
                defect_description=row.defect_description,
                defect_type=row.defect_type,
                defect_code=row.defect_code,
                defect_score=row.defect_score,
                defect_key_id=row.defect_key_id,
                standard_key_id=row.standard_key_id,
                service_score=str(int(row.service_score)),
                structural_score=str(int(row.structural_score)),
                quantity_units=row.quantity_units,
                quantity_value=row.quantity_value,
                is_shown=row.is_shown,
                characterisation_1=row.characterisation_1,
                characterisation_2=row.characterisation_2,
                continuous_score=row.continuous_score,
                main_category=row.main_category,
                material_applied=row.material_applied,
                quantity_1_default_val=row.quantity_1_default_val,
                quantity_1_desc=row.quantity_1_desc,
                quantity_1_end_val=row.quantity_1_end_val,
                quantity_1_score_type=row.quantity_1_score_type,
                quantity_1_start_val=row.quantity_1_start_val,
                quantity_1_units=row.quantity_1_units,
                quantity_2_default_val=row.quantity_2_default_val,
                quantity_2_desc=row.quantity_2_desc,
                quantity_2_end_val=row.quantity_2_end_val,
                quantity_2_score_type=row.quantity_2_score_type,
                quantity_2_start_val=row.quantity_2_start_val,
                quantity_2_units=row.quantity_2_units,
                sub_standard_id=row.sub_standard_id,
                repair_category=row.repair_category,
                repair_priority=row.repair_priority,
                at_joint_required=row.at_joint_required,
                clock_position_required=row.clock_position_required,
                clock_spread_possible=row.clock_spread_possible,
                end_survey=row.end_survey,
                fastpass_code=row.fastpass_code,
                percentage_required=row.percentage_required,
                start_survey=row.start_survey,
            )
        )
    DefectScores.objects.bulk_create(created)


def remove_added_defect_scores(apps, schema_editor):
    DefectScores = apps.get_model("defects", "DefectScores")
    df = pd.read_csv(DATA_FILE).replace({np.nan: None})
    for _, row in df.iterrows():
        DefectScores.objects.filter(
            defect_description=row.defect_description,
            defect_key_id=row.defect_key_id,
            standard_key_id=row.standard_key_id,
            sub_standard_id=row.sub_standard_id,
        ).delete()


class Migration(migrations.Migration):
    dependencies = [
        ("defects", "0028_fix_invalid_class_labels"),
    ]

    operations = [
        migrations.RunPython(code=add_missing_defect_scores, reverse_code=remove_added_defect_scores),
    ]
