from pathlib import Path

import pandas as pd
from django.db import migrations

MIGRATIONS_FOLDER = Path(__file__).parent
DATA_FOLDER = MIGRATIONS_FOLDER.parent / "migrations_data" / "0003_defect_label_additions"


def make_defects_from_records(records: pd.DataFrame, apps):
    Standard = apps.get_model("defects", "Standard")
    StandardSubcategory = apps.get_model("defects", "StandardSubcategory")
    DefectScores = apps.get_model("defects", "DefectScores")
    DefectModelList = apps.get_model("defects", "DefectModelList")

    for _, record in records.iterrows():
        standard = Standard.objects.get(display_name=record["Service_scoringstandardslist.Display_name"])
        subcategory = StandardSubcategory.objects.get(
            standard_key=standard,
            comment=record["Subcategory_Comment"],
            material_type=record["Material_Type"],
        )

        defect, _ = DefectModelList.objects.get_or_create(
            name=record["Service_defectmodellist.Name"],
            defect_model="SS" if record["Pipe_Type_Sewer"] is True else "SW",
        )

        DefectScores.objects.get_or_create(
            standard_key=standard,
            sub_standard=subcategory,
            defect_key=defect,
            defect_description=record["Service_defectscores.defect_description"],
            defect_code=record["Defect_Code"],
            defect_type=record["Defect_Type"],
            defect_score=str(record["Defect_Score"]) if isinstance(record["Defect_Score"], (float, int)) else None,
            structural_score=str(record["Structural_Score"]),
            service_score=str(record["Service_Score"]),
        )


def set_new_defect_labels(apps, schema_editor):
    file_names = list(DATA_FOLDER.iterdir())
    for file_name in file_names:
        records = pd.read_csv(file_name)
        make_defects_from_records(records, apps)


def unset_new_defect_labels(apps, schema_editor):
    DefectScores = apps.get_model("defects", "DefectScores")
    DefectModelList = apps.get_model("defects", "DefectModelList")

    filenames = list(DATA_FOLDER.iterdir())

    for filename in filenames:
        records = pd.read_csv(filename)
        for _, record in records.iterrows():
            DefectScores.objects.filter(
                defect_description=record["Service_defectscores.defect_description"],
                defect_code=record["Defect_Code"],
                defect_type=record["Defect_Type"],
            ).delete()
            DefectModelList.objects.filter(
                name=record["Service_defectmodellist.Name"],
                defect_model="SS" if record["Pipe_Type_Sewer"] is True else "SW",
            ).delete()


class Migration(migrations.Migration):
    dependencies = [
        ("defects", "0002_defect_label_additions"),
    ]

    operations = [
        migrations.RunPython(code=set_new_defect_labels, reverse_code=unset_new_defect_labels),
    ]
