# Generated by Django 4.1.2 on 2025-04-16 00:39

from django.db import migrations
from django.db.models import When, Case, Value

CORRECTED_LABELS = [
    {
        "old_class_label": "Connection - Intruding connection_Large - No_defect",
        "new_class_label": "Connection - Intruding connection_Large",
        "new_dml_id": 10,
    },
    {
        "old_class_label": "Lifting_hole - Lifting_hole - Lifting_hole",
        "new_class_label": "Lifting_hole - Lifting_hole",
        "new_dml_id": 104,
    },
    {
        "old_class_label": "Lifting_hole - Damaged - Lifting_hole",
        "new_class_label": "Lifting_hole - Damaged",
        "new_dml_id": 103,
    },
    {
        "old_class_label": "Connection_or_Junction - intruding",
        "new_class_label": "Connection - Intruding connection_Large",
        "new_dml_id": 10,
    },
    {
        "old_class_label": "Connection_or_Junction - connection",
        "new_class_label": "Connection - No_defect",
        "new_dml_id": 12,
    },
    {
        "old_class_label": "Connection_or_Junction - junction",
        "new_class_label": "Junction - No_defect",
        "new_dml_id": 121,
    },
    {
        "old_class_label": "Connection_or_Junction - lateral",
        "new_class_label": "Junction - No_defect",
        "new_dml_id": 121,
    },
    {
        "old_class_label": "Junction - connection",
        "new_class_label": "Connection - No_defect",
        "new_dml_id": 12,
    },
    {
        "old_class_label": "Junction - intruding",
        "new_class_label": "Connection - Intruding connection_Large",
        "new_dml_id": 10,
    },
    {
        "old_class_label": "Connection",
        "new_class_label": "Connection - No_defect",
        "new_dml_id": 12,
    },
    {
        "old_class_label": "Debris or Deposits - Intruding_objects",
        "new_class_label": "Obstruction - Intruding_objects",
        "new_dml_id": 74,
    },
]


def fix_invalid_labels(apps, schema_editor):
    VideoFrames = apps.get_model("inspections", "VideoFrames")
    old_labels = [r["old_class_label"] for r in CORRECTED_LABELS]
    label_cases = [When(class_label=r["old_class_label"], then=Value(r["new_class_label"])) for r in CORRECTED_LABELS]
    dml_id_cases = [When(class_label=r["old_class_label"], then=Value(r["new_dml_id"])) for r in CORRECTED_LABELS]

    VideoFrames.objects.filter(class_label__in=old_labels, defect_model__isnull=True).update(
        class_label=Case(*label_cases),
        defect_model_id=Case(*dml_id_cases),
    )


class Migration(migrations.Migration):
    dependencies = [
        ("defects", "0027_standardsubcategory_region_update"),
    ]

    operations = [
        migrations.RunPython(code=fix_invalid_labels, reverse_code=migrations.RunPython.noop),
    ]
