import pandas as pd

from django.db import migrations
from pathlib import Path

MIGRATIONS_FOLDER = Path(__file__).parent
DATA_FOLDER = MIGRATIONS_FOLDER.parent / "migrations_data" / "0020_defect_standard_mapping_liners"


def update_defects(apps, schema_editor):
    DefectModelList = apps.get_model("defects", "DefectModelList")
    DefectScores = apps.get_model("defects", "DefectScores")
    Standard = apps.get_model("defects", "Standard")
    StandardSubcategory = apps.get_model("defects", "StandardSubcategory")

    new_dm = DefectModelList.objects.create(name="Liner - Discolouration", defect_model="Both")
    records = pd.read_csv(list(DATA_FOLDER.iterdir())[0])
    records = records.where(pd.notnull(records), None).astype(object).where(pd.notnull(records), None)

    for _, record in records.iterrows():
        if record["id"]:
            existing_ds = DefectScores.objects.filter(id=record["id"]).first()
            existing_ds.defect_key = new_dm
            existing_ds.save()
        else:
            new_ds_dict = record.to_dict()
            if defect_key_name := new_ds_dict.pop("defect_key__name"):
                new_ds_dict["defect_key"] = DefectModelList.objects.filter(name=defect_key_name).first()
            new_ds_dict["standard_key"] = Standard.objects.get(id=new_ds_dict["standard_key_id"])
            new_ds_dict["sub_standard"] = StandardSubcategory.objects.get(
                standard_key=new_ds_dict["standard_key"],
                id=new_ds_dict["sub_standard_id"],
            )
            new_ds_dict["defect_score"] = (
                str(new_ds_dict["defect_score"]) if isinstance(new_ds_dict["defect_score"], (float, int)) else 0
            )
            new_ds_dict["defect_type"] = (
                str(new_ds_dict["defect_type"]) if isinstance(new_ds_dict["defect_type"], (float, int)) else "str"
            )

            DefectScores.objects.create(**new_ds_dict)


class Migration(migrations.Migration):

    dependencies = [
        ("defects", "0019_update_defects"),
    ]

    operations = [
        migrations.RunPython(code=update_defects, reverse_code=migrations.RunPython.noop),
    ]
