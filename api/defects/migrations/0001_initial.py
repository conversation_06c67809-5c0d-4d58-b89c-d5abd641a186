from pathlib import Path

from django.db import migrations, models
import django.db.models.deletion
import uuid
import json


def populate_initial_standards(apps, schema_editor):
    # load the listed standards from a json file and create the records.
    fixtures_folder = Path(__file__).parent.parent / "fixtures"
    standards_data = fixtures_folder / "standards.json"
    substandards_data = fixtures_folder / "substandards.json"

    with open(standards_data, "r") as f:
        data = json.load(f)
        Standard = apps.get_model("defects", "Standard")
        Standard.objects.bulk_create(
            Standard(
                pk=standard["pk"],
                name=standard["fields"]["name"],
                display_name=standard["fields"]["display_name"],
            )
            for standard in data
        )

    with open(substandards_data, "r") as f:
        data = json.load(f)
        StandardSubcategory = apps.get_model("defects", "StandardSubcategory")

        standards = Standard.objects.in_bulk([substandard["fields"]["standard_key"] for substandard in data])

        StandardSubcategory.objects.bulk_create(
            StandardSubcategory(
                pk=substandard["pk"],
                standard_key=standards[substandard["fields"]["standard_key"]],
                pipe_type_sewer=substandard["fields"]["pipe_type_sewer"],
                material_type=substandard["fields"]["material_type"],
                comment=substandard["fields"]["comment"],
            )
            for substandard in data
        )


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("base", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="DefectModelList",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("defect_model", models.CharField(default="SS", max_length=100)),
            ],
            options={
                "db_table": "service_defectmodellist",
            },
        ),
        migrations.CreateModel(
            name="Standard",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=200)),
                (
                    "display_name",
                    models.CharField(default="Australia 2020", max_length=200),
                ),
            ],
            options={
                "db_table": "service_scoringstandardslist",
            },
        ),
        migrations.CreateModel(
            name="StandardSubcategory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("pipe_type_sewer", models.BooleanField(blank=True, null=True)),
                (
                    "material_type",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("comment", models.CharField(blank=True, max_length=200)),
                (
                    "standard_key",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="defects.standard",
                    ),
                ),
            ],
            options={
                "db_table": "service_standard_subcategory",
            },
        ),
        migrations.CreateModel(
            name="StandardHeader",
            fields=[
                (
                    "uuid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=50)),
                ("code", models.CharField(max_length=50, null=True)),
                ("required", models.BooleanField()),
                ("shown_by_default", models.BooleanField()),
                ("description", models.TextField(blank=True, null=True)),
                (
                    "data_type",
                    models.CharField(
                        choices=[
                            ("number", "Number"),
                            ("string", "String"),
                            ("boolean", "Boolean"),
                            ("options", "Options"),
                            ("date", "Date"),
                        ],
                        max_length=7,
                    ),
                ),
                ("options_selections", models.JSONField(blank=True)),
                ("options_description", models.TextField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "header",
                    models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to="base.header"),
                ),
                (
                    "standard",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="defects.standard",
                    ),
                ),
            ],
            options={
                "db_table": "inspections_standardheader",
            },
        ),
        migrations.CreateModel(
            name="ScoreSeverity",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "type",
                    models.CharField(
                        choices=[("structural", "Structural"), ("service", "Service")],
                        max_length=10,
                    ),
                ),
                (
                    "severity",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("medium", "Medium"),
                            ("high", "High"),
                        ],
                        max_length=6,
                        null=True,
                    ),
                ),
                ("min_score", models.IntegerField()),
                ("max_score", models.IntegerField()),
                (
                    "standard",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="defects.standard",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="DefectScores",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("defect_description", models.CharField(max_length=200)),
                ("defect_type", models.CharField(default="structural", max_length=100)),
                ("defect_code", models.CharField(max_length=10)),
                ("defect_score", models.CharField(max_length=200)),
                ("structural_score", models.CharField(max_length=200)),
                ("service_score", models.CharField(max_length=200)),
                (
                    "quantity_value",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        default=None,
                        max_digits=10,
                        null=True,
                    ),
                ),
                (
                    "quantity_units",
                    models.CharField(blank=True, default="", max_length=200),
                ),
                ("is_shown", models.BooleanField(default=True)),
                ("main_category", models.CharField(blank=True, max_length=200)),
                ("characterisation_1", models.CharField(blank=True, max_length=200)),
                ("characterisation_2", models.CharField(blank=True, max_length=200)),
                ("quantity_1_desc", models.CharField(blank=True, max_length=200)),
                ("quantity_1_units", models.CharField(blank=True, max_length=200)),
                (
                    "quantity_1_start_val",
                    models.FloatField(blank=True, default=0.0, max_length=10, null=True),
                ),
                (
                    "quantity_1_end_val",
                    models.FloatField(blank=True, default=0.0, max_length=10, null=True),
                ),
                (
                    "quantity_1_default_val",
                    models.FloatField(blank=True, default=0.0, max_length=10, null=True),
                ),
                ("quantity_1_score_type", models.CharField(blank=True, max_length=200)),
                ("quantity_2_desc", models.CharField(blank=True, max_length=200)),
                ("quantity_2_units", models.CharField(blank=True, max_length=200)),
                (
                    "quantity_2_start_val",
                    models.FloatField(blank=True, default=0.0, max_length=10, null=True),
                ),
                (
                    "quantity_2_end_val",
                    models.FloatField(blank=True, default=0.0, max_length=10, null=True),
                ),
                (
                    "quantity_2_default_val",
                    models.FloatField(blank=True, default=0.0, max_length=10, null=True),
                ),
                ("quantity_2_score_type", models.CharField(blank=True, max_length=200)),
                ("clock_position_required", models.BooleanField(default=False)),
                ("clock_spread_possible", models.BooleanField(default=False)),
                ("at_joint_required", models.BooleanField(default=False)),
                ("percentage_required", models.BooleanField(default=False)),
                ("start_survey", models.BooleanField(default=False)),
                ("end_survey", models.BooleanField(default=False)),
                ("fastpass_code", models.BooleanField(default=False)),
                ("continuous_score", models.BooleanField(default=False)),
                ("material_applied", models.CharField(blank=True, max_length=200)),
                (
                    "repair_category",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("Roots", "Roots"),
                            ("Debris", "Debris"),
                            ("Digup", "Digup"),
                        ],
                        max_length=20,
                        null=True,
                    ),
                ),
                (
                    "repair_priority",
                    models.IntegerField(blank=True, default=-1, null=True),
                ),
                (
                    "defect_key",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="defects.defectmodellist",
                    ),
                ),
                (
                    "standard_key",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="defects.standard",
                    ),
                ),
                (
                    "sub_standard",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="defects.standardsubcategory",
                    ),
                ),
            ],
            options={
                "db_table": "service_defectscores",
            },
        ),
        migrations.RunPython(populate_initial_standards, reverse_code=migrations.RunPython.noop),
    ]
