# Generated by Django 4.1.2 on 2024-07-15 06:55
from pathlib import Path

import pandas as pd

from django.db import migrations

MIGRATIONS_FOLDER = Path(__file__).parent
DATA_FOLDER = MIGRATIONS_FOLDER.parent / "migrations_data" / "0018_add_new_defects"


def add_new_defects(apps, schema_editor):
    Standard = apps.get_model("defects", "Standard")
    StandardSubcategory = apps.get_model("defects", "StandardSubcategory")
    DefectScores = apps.get_model("defects", "DefectScores")

    defects_to_create = []

    file_names = list(DATA_FOLDER.iterdir())
    for file_name in file_names:
        records = pd.read_csv(file_name)
        records = records.fillna("None")
        records = records.replace("None", None)

        for _, record in records.iterrows():
            standard = Standard.objects.get(id=record["standard_key_id"])
            subcategory = StandardSubcategory.objects.get(
                standard_key=standard,
                id=record["sub_standard_id"],
            )

            data = {
                **record.to_dict(),
                "standard_key": standard,
                "sub_standard": subcategory,
                "defect_score": (
                    str(record["defect_score"]) if isinstance(record["defect_score"], (float, int)) else 0
                ),
                "defect_type": (
                    str(record["defect_type"]) if isinstance(record["defect_type"], (float, int)) else "str"
                ),
            }

            defects_to_create.append(data)

    DefectScores.objects.bulk_create(
        [DefectScores(**data) for data in defects_to_create],
    )


def remove_new_defects(apps, schema_editor):
    Standard = apps.get_model("defects", "Standard")
    StandardSubcategory = apps.get_model("defects", "StandardSubcategory")
    DefectScores = apps.get_model("defects", "DefectScores")

    file_names = list(DATA_FOLDER.iterdir())
    for file_name in file_names:
        records = pd.read_csv(file_name)
        records = records.fillna("None")
        records = records.replace("None", None)

        for _, record in records.iterrows():
            standard = Standard.objects.get(id=record["standard_key_id"])
            subcategory = StandardSubcategory.objects.get(
                standard_key=standard,
                id=record["sub_standard_id"],
            )

            DefectScores.objects.filter(
                standard_key=standard,
                sub_standard=subcategory,
                defect_description=record["defect_description"],
                defect_code=record["defect_code"],
            ).delete()


class Migration(migrations.Migration):

    dependencies = [
        ("defects", "0017_update_defects"),
    ]

    operations = [
        migrations.RunPython(code=add_new_defects, reverse_code=remove_new_defects),
    ]
