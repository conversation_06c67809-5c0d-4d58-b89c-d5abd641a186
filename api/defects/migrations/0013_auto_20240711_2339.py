# Generated by Django 4.1.2 on 2024-07-10 00:29

from django.db import migrations


def populate_default_repair_param(apps, schema_editor):
    Standard = apps.get_model("defects", "Standard")

    for standard in Standard.objects.all():
        if standard.name == "PACP7":
            standard.default_repair_param = {
                "Minimum roots class": 15,
                "Debris build up class": 15,
                "Debris build up length m": 1,
                "Debris single instance class": 20,
                "Patch if score over length >=": 4,
                "Patch length for scoring m": 3,
                "Maximum number of patches over distance": 2,
                "Maximum distance for patch": 30,
                "Maximum number of patches in total": 3,
            }
        else:
            standard.default_repair_param = {
                "Minimum roots class": 15,
                "Debris build up class": 15,
                "Debris build up length m": 1,
                "Debris single instance class": 20,
                "Patch if score over length >=": 50,
                "Patch length for scoring m": 1,
                "Maximum number of patches over distance": 2,
                "Maximum distance for patch": 30,
                "Maximum number of patches in total": 3,
            }

        standard.save()


def reverse_populate_default_repair_param(apps, schema_editor):
    Standard = apps.get_model("defects", "Standard")

    for standard in Standard.objects.all():
        standard.default_repair_param = None
        standard.save()


class Migration(migrations.Migration):

    dependencies = [
        ("defects", "0012_standard_default_repair_param"),
    ]

    operations = [
        migrations.RunPython(code=populate_default_repair_param, reverse_code=reverse_populate_default_repair_param),
    ]
