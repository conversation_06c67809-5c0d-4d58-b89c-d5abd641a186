# Generated by Django 4.1.2 on 2024-07-12 05:24
import pandas as pd

from django.db import migrations
from pathlib import Path

MIGRATIONS_FOLDER = Path(__file__).parent
DATA_FOLDER = MIGRATIONS_FOLDER.parent / "migrations_data" / "0017_update_defects"


def update_defects(apps, schema_editor):
    DefectScores = apps.get_model("defects", "DefectScores")

    filename = list(DATA_FOLDER.iterdir())[0]

    records = pd.read_csv(filename)
    records = records.where(pd.notnull(records), None).astype(object).where(pd.notnull(records), None)
    for _, record in records.iterrows():
        defect = DefectScores.objects.get(id=record["id"])
        defect.defect_code = record["defect_code"]
        defect.defect_description = record["defect_description"]
        defect.save()


class Migration(migrations.Migration):

    dependencies = [
        ("defects", "0016_update_defect_is_shown"),
    ]

    operations = [
        migrations.RunPython(code=update_defects, reverse_code=migrations.RunPython.noop),
    ]
