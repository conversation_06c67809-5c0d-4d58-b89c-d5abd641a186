from pathlib import Path

import numpy as np
import pandas as pd
from django.core.management import call_command
from django.db import migrations


MIGRATIONS_FOLDER = Path(__file__).parent
DATA_FOLDER = MIGRATIONS_FOLDER.parent / "management" / "baseline_data"


def populate_baseline_defect_data():
    fixtures_folder = Path(__file__).parent.parent / "fixtures"
    # defect_scores = fixtures_folder / "defect_scores.json"
    defect_modellist = fixtures_folder / "defect_modellist.json"
    call_command("loaddata", str(defect_modellist))


def insert_defects_from_dataset(apps, schema_editor):
    populate_baseline_defect_data()
    DefectScores = apps.get_model("defects", "DefectScores")

    records = pd.read_csv(DATA_FOLDER / "service_defectscores_realignment.csv")
    # only select non-existing records (no 'id')
    records = records[records["id"].isnull()]
    records = records.replace({np.nan: None})
    records["defect_score"] = records["defect_score"].replace({None: 0})

    for _, record in records.iterrows():
        DefectScores.objects.create(**dict(record))


def delete_defects_from_dataset(apps, schema_editor):
    DefectScores = apps.get_model("defects", "DefectScores")

    records = pd.read_csv(DATA_FOLDER / "service_defectscores_realignment.csv")
    # only select non-existing records (no 'id')
    records = records[records["id"].isnull()]
    records = records.replace({np.nan: None})
    records["defect_score"] = records["defect_score"].replace({None: 0})

    for _, record in records.iterrows():
        DefectScores.objects.filter(
            defect_description=record["defect_description"], standard_key_id=record["standard_key_id"]
        ).last().delete()

    # # reset the sequence id
    # cursor = connection.cursor()
    # cursor.execute(
    #     """SELECT setval(pg_get_serial_sequence('service_defectscores', 'id'), coalesce(MAX(id), 1))
    #     FROM service_defectscores"""
    # )


class Migration(migrations.Migration):
    dependencies = [
        ("defects", "0008_alter_defectscores_characterisation_1_and_more"),
    ]

    operations = [
        migrations.RunPython(code=insert_defects_from_dataset, reverse_code=delete_defects_from_dataset),
    ]
