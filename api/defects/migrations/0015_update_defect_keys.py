# Generated by Django 4.1.2 on 2024-07-12 01:45

from django.db import migrations

defect_codes_old = ["STMH", "FHMH"]
defect_codes_new = ["STGJP", "FHGJP"]
sub_standard_comments = ["Australia 2013 Stormwater Rigid", "Australia 2020 Stormwater Rigid"]
start_node_name = "Node - Start node"
end_node_name = "Node - End node"


def update_defect_keys(apps, schema_editor):
    DefectScores = apps.get_model("defects", "DefectScores")
    DefectModelList = apps.get_model("defects", "DefectModelList")

    DefectScores.objects.filter(
        defect_code__in=defect_codes_old,
        sub_standard__comment__in=sub_standard_comments,
    ).update(defect_key=None)

    defect_models = DefectModelList.objects.filter(name__in=[start_node_name, end_node_name])
    defect_model_dict = {model.name: model for model in defect_models}

    DefectScores.objects.filter(defect_code=defect_codes_new[0]).filter(
        sub_standard__comment__in=sub_standard_comments
    ).update(defect_key=defect_model_dict.get(start_node_name))

    DefectScores.objects.filter(defect_code=defect_codes_new[1]).filter(
        sub_standard__comment__in=sub_standard_comments
    ).update(defect_key=defect_model_dict.get(end_node_name))


def reverse_defect_keys(apps, schema_editor):
    DefectScores = apps.get_model("defects", "DefectScores")
    DefectModelList = apps.get_model("defects", "DefectModelList")

    DefectScores.objects.filter(defect_code__in=defect_codes_new).update(defect_key=None)

    defect_models = DefectModelList.objects.filter(name__in=[start_node_name, end_node_name])
    defect_model_dict = {model.name: model for model in defect_models}

    DefectScores.objects.filter(defect_code=defect_codes_old[0]).filter(
        sub_standard__comment__in=sub_standard_comments
    ).update(defect_key=defect_model_dict.get(start_node_name))

    DefectScores.objects.filter(defect_code=defect_codes_old[1]).filter(
        sub_standard__comment__in=sub_standard_comments
    ).update(defect_key=defect_model_dict.get(end_node_name))


class Migration(migrations.Migration):

    dependencies = [
        ("defects", "0014_usa_repair_category_and_priority_changes"),
    ]

    operations = [
        migrations.RunPython(code=update_defect_keys, reverse_code=reverse_defect_keys),
    ]
