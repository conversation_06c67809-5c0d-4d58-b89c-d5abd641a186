from pathlib import Path

import numpy as np
import pandas as pd
from django.core.management import call_command
from django.db import migrations


MIGRATIONS_FOLDER = Path(__file__).parent
DATA_FOLDER = MIGRATIONS_FOLDER.parent / "management" / "baseline_data"


def populate_baseline_defect_data():
    fixtures_folder = Path(__file__).parent.parent / "fixtures"
    defect_scores = fixtures_folder / "defect_scores.json"
    call_command("loaddata", str(defect_scores))


def insert_and_update_defects(apps, schema_editor):
    populate_baseline_defect_data()
    DefectScores = apps.get_model("defects", "DefectScores")
    DefectModelList = apps.get_model("defects", "DefectModelList")

    records = pd.read_csv(DATA_FOLDER / "service_defectscores_realignment_post0009.csv")
    # only select non-existing records (no 'id')
    new_records = records[records["id"].isnull()]
    new_records = new_records.replace({np.nan: None})
    new_records["defect_score"] = new_records["defect_score"].replace({None: 0})

    for _, record in new_records.iterrows():
        DefectScores.objects.create(**dict(record))

    # updating existing records
    records.dropna(subset=["id"], inplace=True)
    records["id"] = records["id"].astype(int)

    for _, record in records.iterrows():
        defectscore = DefectScores.objects.get(pk=record["id"])
        defectscore.defect_key = DefectModelList.objects.get(pk=record["defect_key_id"])
        defectscore.save()


class Migration(migrations.Migration):
    dependencies = [
        ("defects", "0009_defect_scores_additions"),
    ]

    operations = [
        migrations.RunPython(code=insert_and_update_defects),
    ]
