from django.db import migrations


def add_setup_location_standard_header_options(apps, schema_editor):
    StandardHeader = apps.get_model("defects", "StandardHeader")
    nzv3_setup_location = StandardHeader.objects.filter(header__name="SetupLocation", standard_id=4)
    nzv3_setup_location.update(options_selections=["U", "D", "UD", "DU", "Q"])
    nzv4_setup_location = StandardHeader.objects.filter(header__name="SetupLocation", standard_id=5)
    nzv4_setup_location.update(options_selections=["U", "D", "UD", "DU", "Q"])


def remove_setup_location_standard_header_options(apps, schema_editior):
    StandardHeader = apps.get_model("defects", "StandardHeader")
    nzv3_setup_location = StandardHeader.objects.filter(header__name="SetupLocation", standard_id=4)
    nzv3_setup_location.update(options_selections=["U", "D", "UD", "DU"])
    nzv4_setup_location = StandardHeader.objects.filter(header__name="SetupLocation", standard_id=5)
    nzv4_setup_location.update(options_selections=["U", "D", "UD", "DU"])


class Migration(migrations.Migration):
    dependencies = [
        ("defects", "0024_pacp_root_ball_repair_priority"),
    ]

    operations = [
        migrations.RunPython(
            code=add_setup_location_standard_header_options, reverse_code=remove_setup_location_standard_header_options
        ),
    ]
