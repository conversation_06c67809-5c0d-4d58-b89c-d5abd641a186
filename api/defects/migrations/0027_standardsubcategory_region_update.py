from django.db import migrations


def add_region_codes_to_standard_subcategories(apps, schema_editor):
    StandardSubcategory = apps.get_model("defects", "StandardSubcategory")
    # AU Standards
    au_substandards = StandardSubcategory.objects.filter(standard_key__in=(1, 6))
    au_substandards.update(region="AU")
    # UK Standards
    uk_substandards = StandardSubcategory.objects.filter(standard_key=2)
    uk_substandards.update(region="UK")
    # US Standards
    us_substandards = StandardSubcategory.objects.filter(standard_key__in=(3, 7))
    us_substandards.update(region="US")
    # NZ Standards
    nz_substandards = StandardSubcategory.objects.filter(standard_key__in=(4, 5))
    nz_substandards.update(region="NZ")


def remove_region_codes_to_standard_subcategories(apps, schema_editor):
    StandardSubcategory = apps.get_model("defects", "StandardSubcategory")
    StandardSubcategory.objects.all().update(region=None)


class Migration(migrations.Migration):
    dependencies = [
        ("defects", "0026_standardsubcategory_region"),
    ]

    operations = [
        migrations.RunPython(
            code=add_region_codes_to_standard_subcategories, reverse_code=remove_region_codes_to_standard_subcategories
        ),
    ]
