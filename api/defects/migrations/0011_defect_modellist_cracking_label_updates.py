from django.db import migrations


def update_cracking_fractures_labels(apps, schema_editor):
    DefectModelList = apps.get_model("defects", "DefectModelList")

    # update 'crack' defect records
    cracking_records = DefectModelList.objects.filter(name__startswith="Crack")
    for record in cracking_records:
        record.name = str(record.name).replace("Crack -", "Cracking - Cracks -")
        record.save()

    # update 'fracture' defect records
    fracture_records = DefectModelList.objects.filter(name__startswith="Fracture")
    for record in fracture_records:
        record.name = str(record.name).replace("Fracture -", "Cracking - Fractures -")
        record.save()


class Migration(migrations.Migration):
    dependencies = [
        ("defects", "0010_defect_scores_realignment"),
    ]

    operations = [
        migrations.RunPython(code=update_cracking_fractures_labels),
    ]
