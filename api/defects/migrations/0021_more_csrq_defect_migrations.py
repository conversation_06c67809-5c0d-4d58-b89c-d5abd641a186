import pandas as pd

from django.db import migrations
from pathlib import Path

MIGRATIONS_FOLDER = Path(__file__).parent
DATA_FOLDER = MIGRATIONS_FOLDER.parent / "migrations_data" / "0021_more_csrq_defect_migrations"


def update_defectscores(apps, schema_editor):
    DefectScores = apps.get_model("defects", "DefectScores")

    for csv in list(DATA_FOLDER.iterdir()):
        records = pd.read_csv(csv)
        records = records.where(pd.notnull(records), None).astype(object).where(pd.notnull(records), None)

        if "roots default qt1 change" in str(csv):
            for _, record in records.iterrows():
                existing_ds = DefectScores.objects.filter(id=record["id"]).first()
                existing_ds.quantity_1_default_val = record["quantity_1_default_val"]
                existing_ds.save()

        elif "MGO - desc change" in str(csv) or "US Tap descriptions" in str(csv):
            for _, record in records.iterrows():
                existing_ds = DefectScores.objects.filter(id=record["id"]).first()
                existing_ds.defect_description = record["defect_description"]
                existing_ds.main_category = record["main_category"]
                existing_ds.save()

        elif "US Roots" in str(csv):
            for _, record in records.iterrows():
                existing_ds = DefectScores.objects.filter(id=record["id"]).first()
                existing_ds.repair_category = record["repair_category"]
                existing_ds.repair_priority = record["repair_priority"]
                existing_ds.save()

        elif "US Tap Qtys" in str(csv):
            for _, record in records.iterrows():
                existing_ds = DefectScores.objects.filter(id=record["id"]).first()
                existing_ds.quantity_1_units = record["quantity_1_units"]
                existing_ds.save()


class Migration(migrations.Migration):

    dependencies = [
        ("defects", "0020_defect_standard_mapping_liners"),
    ]

    operations = [
        migrations.RunPython(code=update_defectscores, reverse_code=migrations.RunPython.noop),
    ]
