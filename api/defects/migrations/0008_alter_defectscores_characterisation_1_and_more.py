# Generated by Django 4.1.2 on 2024-05-23 04:56

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("defects", "0007_standardheader_standard_header_data_type_allowed_constraint"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="defectscores",
            name="characterisation_1",
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name="defectscores",
            name="characterisation_2",
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name="defectscores",
            name="defect_code",
            field=models.Char<PERSON>ield(max_length=10, null=True),
        ),
        migrations.AlterField(
            model_name="defectscores",
            name="defect_score",
            field=models.Char<PERSON>ield(default=0, max_length=200),
        ),
        migrations.AlterField(
            model_name="defectscores",
            name="material_applied",
            field=models.<PERSON><PERSON><PERSON><PERSON>(blank=True, max_length=200, null=True),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name="defectscores",
            name="quantity_1_desc",
            field=models.Char<PERSON><PERSON>(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name="defectscores",
            name="quantity_1_score_type",
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name="defectscores",
            name="quantity_1_units",
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name="defectscores",
            name="quantity_2_desc",
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name="defectscores",
            name="quantity_2_score_type",
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name="defectscores",
            name="quantity_2_units",
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name="defectscores",
            name="quantity_units",
            field=models.CharField(blank=True, default=None, max_length=200, null=True),
        ),
    ]
