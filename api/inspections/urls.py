from django.urls import path

from .views.views import (
    BulkDeleteFiles,
    BulkInspectionValidation,
    BulkUpdateStatus,
    FileDownload,
    FolderDetail,
    FolderFilter,
    FolderList,
    FrameDefectUpdate,
    FrameDetail,
    FrameList,
    VideoFramesCreateUpdateDestroy,
    ImportedInspectionFileDetail,
    ImportedInspectionFileList,
    InspectionBulkMove,
    InspectionDetail,
    InspectionDetail2,
    InspectionMatch,
    InspectionFileUploadComplete,
    InspectionFileList,
    InspectionFilterDetail,
    InspectionList,
    InspectionList2,
    InspectionValueDetail,
    InspectionShareableLinks,
    PlannedInspectionList,
    ProcessingFileDetail,
    ProcessingFileListCreate,
    ProcessingFileCheckView,
    StandardInspectionDetail,
    ShareableImageLink,
    ShareablePDFLink,
    ShareableVideoDownloadLink,
    ShareableVideoPlayLink,
    AllFilesView,
    InspectionFileDetail,
    ProcessingFileRetryView,
    FileListView,
    FrameList2,
    FrameGlobalList,
    InspectionFileUploadMediaView,
    FrameListByVideo,
)

from .views.asset_views import (
    AssetListCreateDestroy,
    AssetRetrieveUpdate,
    AssetValueDetail,
    AssetInspectionsList,
    AssetMatchOrCreateView,
)

urlpatterns = [
    path("inspections", InspectionList.as_view(), name="inspection_list"),
    path("inspections2", InspectionList2.as_view(), name="inspection_list2"),
    path("inspections/<int:id>", InspectionDetail.as_view(), name="inspection_detail"),
    path(
        "inspections/filters",
        InspectionFilterDetail.as_view(),
        name="inspection_filter",
    ),
    path("inspections2/<uuid:uuid>", InspectionDetail2.as_view(), name="inspection_detail2"),
    path(
        "inspections/planned",
        PlannedInspectionList.as_view(),
        name="planned_inspections",
    ),
    path(
        "inspections/<int:id>/shareable-links",
        InspectionShareableLinks.as_view(),
        name="inspection_shareable_links",
    ),
    path("inspections/<int:id>/match", InspectionMatch.as_view(), name="inspection_match"),
    path(
        "inspections/<int:id>/standard-headers",
        StandardInspectionDetail.as_view(),
        name="inspection_standard_headers",
    ),
    path(
        "inspections/inspection-values/<uuid:uuid>",
        InspectionValueDetail.as_view(),
        name="inspection_value_detail",
    ),
    path(
        "inspections/asset-values/<uuid:uuid>",
        AssetValueDetail.as_view(),
        name="asset_value_detail",
    ),
    path("inspections/<int:inspection_id>/frames", FrameList.as_view(), name="frame_list"),
    path("inspections2/<uuid:uuid>/frames", FrameList2.as_view(), name="frame_list_by_inspection_2"),
    path(
        "inspections/videos/<int:video_id>/frames",
        VideoFramesCreateUpdateDestroy.as_view(),
        name="video_frames_create_destroy",
    ),
    path("inspections/frames/<int:pk>", FrameDetail.as_view(), name="frame_detail"),
    path("inspections/frames", FrameGlobalList.as_view(), name="frame_global_list"),
    path(
        "inspections/frames/<int:pk>/update-defect",
        FrameDefectUpdate.as_view(),
        name="frame_detail",
    ),
    path("inspections/folders", FolderList.as_view()),
    path("inspections/folders/<int:id>", FolderDetail.as_view()),
    path("inspections/folders/search", FolderFilter.as_view()),
    path("inspections/bulk-update-status", BulkUpdateStatus.as_view()),
    path("inspections/bulk-delete", BulkDeleteFiles.as_view()),
    path("inspections/move/<int:folder_id>", InspectionBulkMove.as_view()),
    path(
        "inspections/import",
        ImportedInspectionFileList.as_view(),
        name="import_inspection_file",
    ),
    path(
        "inspections/import/<uuid>/process",
        ImportedInspectionFileDetail.as_view(),
        name="import_inspection",
    ),
    path("inspections/validate", BulkInspectionValidation.as_view(), name="validate_inspections"),
    path("inspections/export/validate", BulkInspectionValidation.as_view(), name="validate_inspections_export"),
    path("files", FileListView.as_view(), name="files_list"),
    path("files/processing/<int:id>", ProcessingFileDetail.as_view()),
    path("files/download", FileDownload.as_view(), name="file_download"),
    path("files/processing", ProcessingFileListCreate.as_view(), name="file_processing_list_create"),
    path("files/processing/<int:file_id>/retry", ProcessingFileRetryView.as_view()),
    path("files/upload", InspectionFileList.as_view(), name="file_upload"),
    path("files/<int:pk>/upload-complete", InspectionFileUploadComplete.as_view(), name="file_upload_complete"),
    path("files/<int:id>", InspectionFileDetail.as_view(), name="file_detail"),
    path("files/<int:id>/upload", InspectionFileUploadMediaView.as_view(), name="file_upload_media"),
    path("files/<int:id>/frames", FrameListByVideo.as_view(), name="frame_list_by_video_file"),
    path("files/all", AllFilesView.as_view(), name="all_files"),
    path(
        "processing/check",
        ProcessingFileCheckView.as_view(),
        name="processing_file_check",
    ),
]

asset_urls = [
    path("assets", AssetListCreateDestroy.as_view(), name="asset_list_create_destroy"),
    path("assets/<uuid:uuid>", AssetRetrieveUpdate.as_view(), name="asset_retrieve_update"),
    path("assets/<uuid:uuid>/inspections", AssetInspectionsList.as_view(), name="asset_inspections_list"),
    path("assets/match-or-create", AssetMatchOrCreateView.as_view(), name="asset_match_or_create"),
]

external_share_links = [
    path(
        "api/playurl/<inspection_id>",
        ShareableVideoPlayLink.as_view(),
        name="shareable_video_play_url",
    ),
    path(
        "api/vids/<token>",
        ShareableVideoDownloadLink.as_view(),
        name="shareable_video_download_link",
    ),
    path("api/images/<token>", ShareableImageLink.as_view(), name="shareable_image_link"),
    path("api/assetpdf/<token>", ShareablePDFLink.as_view(), name="shareable_pdf_link"),
]
