from datetime import datetime

from dateutil.parser import parse
from django.db.models import Q
from django.utils import timezone

from api.inspections.pydantic_models.inspection_model import get_inspection_representation
from api.inspections.models import (
    Asset,
    AssetValue,
    Inspection,
    InspectionValue,
    StandardHeader,
    ImportedInspectionFile,
    MapPointList,
)
from api.inspections.serializers.asset_serializers import AssetValueSerializer
from api.inspections.utilities.validate_inspections import (
    validate_csv,
    get_duplicate_assets,
    get_duplicate_inspections,
    get_start_node_header,
    get_finish_node_header,
    get_start_node_value,
    get_finish_node_value,
)
from api.inspections.utilities.sync_mappointlist_values import sync_to_mappointlist


def create_inspection_records(csv_data, standard, folder, inspection_file, user, organisation, action=None):
    headers = csv_data.columns.values
    standard_headers = StandardHeader.objects.filter(standard=standard)
    file = ImportedInspectionFile.objects.filter(uuid=inspection_file).first()

    validation_feedback = validate_csv(csv_data, standard, folder)
    map_point_list = None
    unmatched_inspections = []
    existing_inspections = Inspection.objects.filter(folder=folder)
    asset_id_header = StandardHeader.objects.filter(header__name="AssetID", standard=standard).first()
    start_node_header = get_start_node_header(standard=standard)
    finish_node_header = get_finish_node_header(standard=standard)
    header_index_asset_id = list(headers).index(asset_id_header.name)
    duplicate_rows = []

    if validation_feedback:
        duplicate_rows = validation_feedback["duplicate_rows"]

    for i, row in csv_data.iterrows():
        asset = None
        inspection = None
        duplicate_assets = get_duplicate_assets(
            row=row,
            folder=folder,
            existing_inspections=existing_inspections,
            standard=standard,
            headers=headers,
        )
        duplicate_inspections = get_duplicate_inspections(
            row=row,
            folder=folder,
            existing_inspections=existing_inspections,
            standard=standard,
            headers=headers,
            duplicate_assets=duplicate_assets,
        )
        update_inspection_values = False
        update_asset_values = False

        if action == "create_duplicates":
            if len(duplicate_assets) > 0:
                asset_ids = [asset.uuid for asset in duplicate_assets]
                asset = Asset.objects.filter(uuid__in=asset_ids).order_by("-created_at").first()
                update_asset_values = True

        elif action == "ignore_duplicates":
            if (i + 1) in duplicate_rows:
                continue

        elif action == "overwrite_duplicates":
            if (i + 1) in duplicate_rows:
                if len(duplicate_assets) > 0:
                    asset_ids = [asset.uuid for asset in duplicate_assets]
                    asset = Asset.objects.filter(uuid__in=asset_ids).order_by("-created_at").first()
                    update_asset_values = True

                if len(duplicate_inspections) > 0:
                    inspection_ids = [inspection.uuid for inspection in duplicate_inspections]
                    inspection = Inspection.objects.filter(uuid__in=inspection_ids).order_by("-created_at").first()
                    update_inspection_values = True

        if not asset:
            asset_data = {"organisation": organisation, "created_at": timezone.now()}
            asset = Asset.objects.create(**asset_data)

        if not inspection:
            inspection_data = {"asset": asset, "folder": folder, "created_at": timezone.now(), "created_by": user}
            inspection = Inspection.objects.create(**inspection_data)

        map_point_list = (
            MapPointList.objects.filter(
                Q(asset_id=row[header_index_asset_id])
                & (Q(associated_file__job_tree__id=folder.id) | Q(inspection__folder=folder.id))
            )
            .order_by("id")
            .first()
        )

        start_node_value = get_start_node_value(header_uuid=start_node_header.uuid, inspection_uuid=inspection.uuid)
        finish_node_value = get_finish_node_value(header_uuid=finish_node_header.uuid, inspection_uuid=inspection.uuid)

        if not map_point_list:
            if start_node_value or finish_node_value:
                map_point_list = (
                    MapPointList.objects.filter(
                        Q(start_node=start_node_value.value) & Q(end_node=finish_node_value.value)
                        | Q(start_node=start_node_value.value) & Q(end_node__exact="")
                        | Q(start_node__exact="")
                        & Q(end_node=finish_node_value.value)
                        & (Q(associated_file__job_tree__id=folder.id) | Q(inspection__folder=folder.id))
                    )
                    .order_by("id")
                    .first()
                )

        mpl_data = []

        if asset and inspection:
            standard_headers_created_or_updated = []

            for j, value in enumerate(row.values):
                standard_header = standard_headers.filter(name=row.index[j]).first()
                value_type = standard_header.header.type

                if value_type == "asset":
                    asset_value = (
                        AssetValue.objects.filter(asset=asset.uuid, standard_header=standard_header)
                        .order_by("-created_at")
                        .first()
                    )

                    if update_asset_values:
                        if asset_value:
                            asset_value.value = value
                            asset_value.imported_inspection_file = file
                            asset_value.save()

                            mapped_field = standard_header.header.mapped_mpl_field

                            if mapped_field != "" and map_point_list:
                                inspection_model = get_inspection_representation(
                                    inspection=map_point_list.inspection,
                                )
                                sync_to_mappointlist(
                                    mappointlist=map_point_list,
                                    inspection=inspection_model,
                                    mapped_fields=[mapped_field],
                                )

                    if not asset_value:
                        data = {
                            "asset": asset.uuid,
                            "standard_header": standard_header,
                            "value": value,
                            "imported_inspection_file": file,
                        }
                        asset_value = AssetValueSerializer(data=data)
                        if asset_value.is_valid(raise_exception=True):
                            asset_value.save()

                    standard_headers_created_or_updated.append(standard_header)

                if value_type == "inspection":
                    inspection_value = None

                    if update_inspection_values:
                        inspection_value = (
                            InspectionValue.objects.filter(inspection=inspection, standard_header=standard_header)
                            .order_by("-created_at")
                            .first()
                        )
                        if inspection_value:
                            inspection_value.value = value
                            inspection_value.imported_inspection_file = file
                            inspection_value.save()

                            mapped_field = standard_header.header.mapped_mpl_field

                            if mapped_field != "" and map_point_list:
                                inspection_model = get_inspection_representation(
                                    inspection=map_point_list.inspection,
                                )
                                sync_to_mappointlist(
                                    mappointlist=map_point_list,
                                    inspection=inspection_model,
                                    mapped_fields=[mapped_field],
                                )

                    if not inspection_value:
                        inspection.create_inspection_value(
                            standard_header=standard_header, value=value, imported_inspection_file=file
                        )

                    standard_headers_created_or_updated.append(standard_header)

                if standard_header.header.mapped_mpl_field and value != "":
                    mpl_data.append({"mpl_field": standard_header.header.mapped_mpl_field, "value": value})

            for standard_header in standard_headers:
                if standard_header not in standard_headers_created_or_updated:
                    if standard_header.header.type == "asset":
                        data = {
                            "asset": asset.uuid,
                            "standard_header": standard_header,
                            "value": "",
                            "imported_inspection_file": file,
                        }
                        asset_value = AssetValueSerializer(data=data)
                        if asset_value.is_valid(raise_exception=True):
                            asset_value.save()

                    elif standard_header.header.type == "inspection":
                        inspection.create_inspection_value(
                            standard_header=standard_header, value="", imported_inspection_file=file
                        )

        if not map_point_list and len(mpl_data) > 0 or action == "create_duplicates":
            data = {}
            for item in mpl_data:
                data[item["mpl_field"]] = item["value"]
            # clean up data types before commiting to DB
            if data.get("date_captured", False):
                try:
                    data["date_captured"] = parse(str(data["date_captured"])).date()
                except Exception:
                    data["date_captured"] = None

            if data.get("review_timestamp"):
                try:
                    time_format = "%Y-%m-%d %I:%M%p"
                    adjusted_time_str = datetime.strptime(data.get("review_timestamp"), time_format).strftime("%H:%M")
                    date_string = parse(str(data["date_captured"])).date()
                    datetime_str = f"{date_string} {adjusted_time_str}"
                    parsed_datetime = datetime.strptime(datetime_str, "%Y-%m-%d %H:%M")
                    data["review_timestamp"] = parsed_datetime
                except Exception:
                    data["review_timestamp"] = None

            if data.get("direction", False):
                if data["direction"] == "U":
                    data["direction"] = "Upstream"
                elif data["direction"] == "D":
                    data["direction"] = "Downstream"
                else:
                    data["direction"] = "Unknown"

            elif data.get("opposite of direction", False):
                if data["opposite of direction"] == "D":
                    data["direction"] = "Upstream"
                elif data["opposite of direction"] == "U":
                    data["direction"] = "Downstream"
                else:
                    data["direction"] = "Unknown"

            if data.get("chainage_unit", None) is not None:
                if data["chainage_unit"]:
                    data["chainage_unit"] = "ft"
                else:
                    data["chainage_unit"] = "m"

            data["inspection"] = inspection
            data["standard_key"] = folder.standard_key
            data["status"] = "Planned"
            create_mappointlist = MapPointList.objects.create(**data)
            create_mappointlist.inspection = inspection
            if create_mappointlist.start_node is not None or create_mappointlist.end_node is not None:
                create_mappointlist.set_up_down_node()
            create_mappointlist.save()
        else:
            unmatched_inspections.append(inspection)
