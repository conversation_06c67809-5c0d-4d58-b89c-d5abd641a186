from uuid import UUID

import pydantic
from django.db.models import Q
from django.conf import settings
from django.core.cache import caches
from rest_framework import serializers

from api.base.models import Header
from api.inspections.models import (
    Asset,
    AssetValue,
    StandardHeader,
)
from api.inspections.pydantic_models.asset_model import AssetModel, AssetPostModel
from api.inspections.serializers.mixins import HeaderMixin, StandardHeaderMixin

CACHE_TIMEOUT = settings.DEFAULT_ENTITY_CACHE_TIMEOUT


class AssetSerializer(serializers.ModelSerializer):
    inspections_count = serializers.IntegerField(source='inspection_set.count', read_only=True)
    
    def create(self, validated_data):
        standard = self.context.get("standard")

        asset = super().create(self.validated_data)

        # Adds defaults for missing / optional headers
        try:
            full_creation_data = AssetPostModel(**self.initial_data).model_dump(by_alias=True, mode="json")
        except pydantic.ValidationError as e:
            raise serializers.ValidationError(e.json())

        asset_values_keys = set(full_creation_data).difference(validated_data)
        asset_values_keys.discard("standard")  # Standard is neither a header nor a field on the Asset entity

        standard_headers = StandardHeader.objects.filter(header__name__in=asset_values_keys).filter(standard=standard)

        for key in asset_values_keys:
            standard_header = standard_headers.filter(header__name=key).first()

            if not standard_header:
                raise serializers.ValidationError(f"Unable to create asset for {key}")

            value = full_creation_data.get(key, "")
            if value is None:
                value = ""
            data = {
                "asset": asset.uuid,
                "standard_header": standard_header.uuid,
                "value": value,
            }
            asset_value = AssetValueSerializer(data=data)

            asset_value.is_valid(raise_exception=True)
            asset_value.save()

        return asset

    def update(self, instance, validated_data):
        asset = super().update(instance, validated_data)
        request = self.context.get("request")
        asset_values_keys = set(request.data).difference(validated_data)

        for key in asset_values_keys:
            if key == "standard":
                continue
            asset_value = AssetValue.objects.filter(asset__uuid=asset.uuid, standard_header__header__name=key).first()
            if not asset_value:
                standard_header = StandardHeader.objects.filter(
                    header__name=key, header__type=Header.HeaderType.ASSET, standard_id=asset.standard
                ).first()
                if not standard_header:
                    raise serializers.ValidationError(f"Unable to create asset value for {key}")

                data = {
                    "asset": asset.uuid,
                    "standard_header": standard_header.uuid,
                    "value": request.data.get(key, ""),
                }
                asset_value = AssetValueSerializer(data=data)

                if asset_value.is_valid(raise_exception=True):
                    asset_value.save()
                continue

            instance = AssetValueSerializer(
                instance=asset_value,
                data={"value": request.data.get(key), "standard_header": asset_value.standard_header.uuid},
                partial=True,
            )
            if instance.is_valid(raise_exception=True):
                instance.save()

        return asset

    def to_representation(self, instance: Asset):
        representation = super().to_representation(instance)

        asset_data = {
            **representation,
        }
        asset_values = instance.assetvalue_set.all()
        for value in asset_values:
            asset_data[value.standard_header.header.name] = value.value if value.value else None

        asset = AssetModel.model_validate(asset_data)

        if self.context.get("skip_cache", False):  # So we can avoid n queries to the cache if using with a list
            return asset

        cache = caches["assets"]
        cache.set(
            asset.uuid,
            asset.model_dump_json(
                exclude_none=True, exclude_defaults=True, exclude_unset=True, exclude=set(asset.model_computed_fields)
            ),
            timeout=CACHE_TIMEOUT,
        )

        return asset

    class Meta:
        model = Asset
        fields = ["uuid", "type", "organisation", "standard", "created_at", "inspections_count"]
        read_only_fields = ["uuid", "created_at"]


class AssetValueBaseSerializer(serializers.ModelSerializer):
    def create(self, validated_data):
        validated_data["original_value"] = validated_data["value"]
        return super().create(validated_data)

    def update(self, instance, validated_data):
        header_name = instance.standard_header.header.name

        if header_name == "AssetID" and instance.value:
            raise serializers.ValidationError(
                "You cannot update an asset id unless it is blank, create a new asset instead."
            )

        return super().update(instance, validated_data)

    def validate(self, data):
        instance = self.instance
        asset = getattr(instance, "asset", data.get("asset"))
        organisation = asset.organisation
        standard_header = getattr(instance, "standard_header", data.get("standard_header"))
        header = standard_header.header

        exclude = Q()

        if getattr(instance, "uuid", None):
            exclude = Q(uuid=instance.uuid)

        if header.name == "AssetID" and data.get("value"):
            case_insensitive_duplicate_exists = (
                AssetValue.objects.filter(
                    asset__organisation=organisation,
                    standard_header=standard_header,
                    value__iexact=data.get("value"),
                )
                .exclude(exclude)
                .exists()
            )

            if case_insensitive_duplicate_exists:
                raise serializers.ValidationError("AssetID value already exists for this organisation")

        return super().validate(data)

    class Meta:
        model = AssetValue
        fields = "__all__"


class AssetValueSerializer(AssetValueBaseSerializer, StandardHeaderMixin, serializers.ModelSerializer):
    # Field removed from model
    # imported_inspection_file = serializers.PrimaryKeyRelatedField(read_only=True)

    class Meta:
        model = AssetValue
        fields = [
            "uuid",
            "value",
            "standard_header",
            "standard_header_name",
            "asset",
            # "imported_inspection_file",
        ]


class AssetValueListSerializer(StandardHeaderMixin, HeaderMixin, serializers.ModelSerializer):
    header_uuid = serializers.SerializerMethodField(read_only=True)
    header_name = serializers.SerializerMethodField(read_only=True)
    is_editable = serializers.SerializerMethodField(read_only=True)
    data_type = serializers.SerializerMethodField(read_only=True)

    def get_header_uuid(self, data: AssetValue) -> UUID:
        return data.standard_header.header.uuid

    def get_header_name(self, data: AssetValue) -> str:
        return data.standard_header.header.name

    def get_is_editable(self, data: AssetValue) -> bool:
        return data.standard_header.header.is_editable

    def get_data_type(self, data: AssetValue) -> str:
        return data.standard_header.data_type

    class Meta:
        model = AssetValue
        fields = [
            "uuid",
            "standard_header",
            "standard_header_name",
            "value",
            "header_uuid",
            "header_name",
            "is_editable",
            "data_type",
        ]


class AssetDeleteSerializer(serializers.Serializer):
    asset_uuids = serializers.ListField(
        child=serializers.UUIDField(),
        required=True,
        allow_empty=False
    )
