from rest_framework import serializers

from api.defects.models import StandardHeader


class Header<PERSON><PERSON>in(serializers.Serializer):
    header_uuid = serializers.SerializerMethodField(read_only=True)
    header_name = serializers.SerializerMethodField(read_only=True)

    def get_header_uuid(self, data):
        return data.standard_header.header.uuid

    def get_header_name(self, data):
        return data.standard_header.header.name


class StandardHeaderMixin(serializers.Serializer):
    standard_header = serializers.PrimaryKeyRelatedField(queryset=StandardHeader.objects.all())
    standard_header_name = serializers.SerializerMethodField(read_only=True)

    def get_standard_header_name(self, data) -> str:
        return data.standard_header.name
