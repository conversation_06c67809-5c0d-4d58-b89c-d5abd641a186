from datetime import datetime
from typing import Optional
from uuid import UUID
from enum import Enum

from django.conf import settings
from pydantic import BaseModel
from vapar.clients.events import AbstractEventClient
from vapar.core.assets.event_types import (
    ASSET_CREATED_EVENT,
    ASSET_LINKED_EVENT,
    ASSET_UPDATED_EVENT,
    AssetValuesSummary,
    AssetCreatedEvent,
    AssetLinkedToInspectionEvent,
    AssetUpdatedEvent,
    OrgSummary as AssetOrgSummary,
)
from vapar.core.inspections.event_types import (
    INSPECTION_CREATED_EVENT,
    INSPECTION_UPDATED_EVENT,
    InspectionCreatedEvent,
    InspectionUpdatedEvent,
    OrgSummary as InspectionOrgSummary,
    InspectionValuesSummary,
)
from api.common.analytics import get_event_client
from api.common.enums import StatusEnum
from api.defects.models import DefectScores
from api.inspections.models import VideoFrames
from api.inspections.pydantic_models.asset_model import AssetModel
from api.inspections.pydantic_models.inspection_model import InspectionModel
from api.organisations.models import Organisations
from api.users.models import CustomUser


class FramesPageViewModeEnum(str, Enum):
    ALL = "all"
    REPORTED = "reported"


class FrameDefectSummary(BaseModel):

    defect_model_name: str | None = None
    defect_description: str | None = None
    standard_name: str | None = None


class OrgSummary(BaseModel):
    org_id: int
    org_type: str | None = None
    full_name: str
    country_code: str | None = None


class FrameStateSummary(BaseModel):
    is_hidden: bool = False
    chainage_number: float | None = None
    at_clock: int | None = None
    to_clock: int | None = None
    quantity_1_value: float | None = None
    quantity_2_value: float | None = None
    quantity_1_units: str | None = None
    quantity_2_units: str | None = None
    is_continuous_defect: bool = False
    continuous_defect_end_chainage: float | None = None
    at_joint: bool | None = None


class FrameDefectUpdatedEvent(BaseModel):
    frame_id: int  # The position of the frame in the video, not its DB ID
    file_name: str
    video_id: int
    user_id: int
    owner_org: OrgSummary
    updater_org: OrgSummary
    updated_at: datetime
    frame_state: FrameStateSummary
    previous_defect: FrameDefectSummary | None = None
    new_defect: FrameDefectSummary | None = None
    inspection_status: StatusEnum | None = None
    frames_page_view_mode: FramesPageViewModeEnum | None = None


def send_frame_defect_updated_event(
    frame: VideoFrames,
    updated_at: datetime,
    user: CustomUser,
    owner_org: Organisations,
    updater_org: Organisations,
    previous_defect: Optional[DefectScores],
    new_defect: Optional[DefectScores],
    inspection_status: StatusEnum | None = None,
    frames_page_view_mode: FramesPageViewModeEnum | None = None,
    client: AbstractEventClient | None = None,
):
    """
    Send a frame defect updated event to the event client

    :param frame: The video frame being updated
    :param user: The user who updated the defect
    :param owner_org: The organisation that owns the video
    :param updater_org: The organisation that updated the defect
    :param updated_at: The time the update occurred
    :param previous_defect: The previous defect
    :param new_defect: The updated defect
    :param inspection_status: The status of the inspection when the update occurred
    :param frames_page_view_mode: The view of the frames page the user was on when they updated the defect
    :param client: Override the client to use. If None, the default client will be used.
    """

    client = client or get_event_client(settings.DEFECT_UPDATED_EVENTHUB_NAME)

    frame_state = FrameStateSummary(
        is_hidden=frame.is_hidden,
        chainage_number=frame.chainage_number,
        at_clock=frame.at_clock,
        to_clock=frame.to_clock,
        quantity_1_value=frame.quantity1_value,  # noqa - can be converted by pydantic
        quantity_2_value=frame.quantity2_value,  # noqa
        quantity_1_units=frame.quantity1_units,
        quantity_2_units=frame.quantity2_units,
        is_continuous_defect=frame.cont_defect_start,
        continuous_defect_end_chainage=frame.cont_defect_end,  # noqa
        at_joint=frame.at_joint,
    )

    previous_defect_summary = (
        FrameDefectSummary(
            defect_model_name=previous_defect.defect_key.name if previous_defect.defect_key else None,
            defect_description=previous_defect.defect_description,
            standard_name=previous_defect.standard_key.name,
        )
        if previous_defect
        else None
    )

    new_defect_summary = (
        FrameDefectSummary(
            defect_model_name=new_defect.defect_key.name if new_defect.defect_key else None,
            defect_description=new_defect.defect_description,
            standard_name=new_defect.standard_key.name,
        )
        if new_defect
        else None
    )

    event = FrameDefectUpdatedEvent(
        frame_id=frame.frame_id,
        file_name=frame.image_location,
        video_id=frame.parent_video.id,
        user_id=user.id,
        frame_state=frame_state,
        owner_org=OrgSummary(
            org_id=owner_org.id,
            org_type=owner_org.org_type,
            full_name=owner_org.full_name,
            country_code=owner_org.country.code,
        ),
        updater_org=OrgSummary(
            org_id=updater_org.id,
            org_type=updater_org.org_type,
            full_name=updater_org.full_name,
            country_code=updater_org.country.code,
        ),
        updated_at=updated_at,
        previous_defect=previous_defect_summary,
        new_defect=new_defect_summary,
        inspection_status=inspection_status,
        frames_page_view_mode=frames_page_view_mode,
    )

    client.sync_send_events(event.model_dump_json())


def send_asset_created_event(
    asset: AssetModel,
    pipe_type: str,
    creator: CustomUser | None,
    owner_org: Organisations,
    created_at: datetime | None = None,
    client: AbstractEventClient | None = None,
):
    """
    Send an event to the client indicating that an asset was created.

    :param asset: The asset that was created
    :param pipe_type: SS or SW
    :param creator: The user who created the asset. None if not created by a user.
    :param owner_org: The organisation that owns the asset
    :param created_at: The time the creation occurred
    :param client: Override the client to use. If None, the default client will be used.
    """
    client = client or get_event_client(settings.ASSET_EVENTHUB_NAME)
    created_at = created_at or datetime.utcnow()

    creator_org = (
        AssetOrgSummary(
            org_id=creator.organisation.id,
            org_type=creator.organisation.org_type,
            full_name=creator.organisation.full_name,
            country_code=creator.organisation.country.code,
        )
        if creator and creator.organisation
        else None
    )

    event = AssetCreatedEvent(
        id=asset.uuid,
        created_at=created_at,
        user_id=creator.id if creator else None,
        creator_org=creator_org,
        owner_org=AssetOrgSummary(
            org_id=owner_org.id,
            org_type=owner_org.org_type,
            full_name=owner_org.full_name,
            country_code=owner_org.country.code,
        ),
        values=AssetValuesSummary(
            location_street=asset.location_street or "",
            location_town=asset.location_town or "",
            use_of_drain_sewer=pipe_type,
            material=asset.material or "",
            upstream_node=asset.upstream_node or "",
            downstream_node=asset.downstream_node or "",
            diameter=asset.diameter or 0.0,
        ),
    )
    serialized = event.model_dump_json(by_alias=True)
    client.sync_send_events(serialized, event_types=ASSET_CREATED_EVENT)


def send_asset_updated_event(
    asset: AssetModel,
    pipe_type: str,
    updater: CustomUser | None,
    owner_org: Organisations,
    updated_at: datetime | None = None,
    client: AbstractEventClient | None = None,
):
    """
    Send an event to the client indicating that an asset was updated.

    :param asset: The asset that was updated
    :param pipe_type: SS or SW
    :param updater: The user who updated the asset. None if not updated by a user.
    :param owner_org: The organisation that owns the asset
    :param updated_at: The time the update occurred
    :param client: Override the client to use. If None, the default client will be used.
    """
    client = client or get_event_client(settings.ASSET_EVENTHUB_NAME)
    updated_at = updated_at or datetime.utcnow()

    updater_org = (
        AssetOrgSummary(
            org_id=updater.organisation.id,
            org_type=updater.organisation.org_type,
            full_name=updater.organisation.full_name,
            country_code=updater.organisation.country.code,
        )
        if updater and updater.organisation
        else None
    )

    event = AssetUpdatedEvent(
        id=asset.uuid,
        updated_at=updated_at,
        user_id=updater.id if updater else None,
        updater_org=updater_org,
        owner_org=AssetOrgSummary(
            org_id=owner_org.id,
            org_type=owner_org.org_type,
            full_name=owner_org.full_name,
            country_code=owner_org.country.code,
        ),
        values=AssetValuesSummary(
            location_street=asset.location_street or "",
            location_town=asset.location_town or "",
            use_of_drain_sewer=pipe_type,
            material=asset.material or "",
            upstream_node=asset.upstream_node or "",
            downstream_node=asset.downstream_node or "",
            diameter=asset.diameter or 0.0,
        ),
    )

    serialized = event.model_dump_json(by_alias=True)
    client.sync_send_events(serialized, event_types=ASSET_UPDATED_EVENT)


def send_asset_linked_to_inspection_event(
    asset_id: UUID,
    inspection_id: UUID,
    owner_org: Organisations,
    user: CustomUser | None = None,
    linked_at: datetime | None = None,
    client: AbstractEventClient | None = None,
):
    """
    Send an event to the client indicating that an inspection was linked to an asset.

    :param asset_id: The ID of the asset that was linked
    :param inspection_id: The ID of the inspection that was linked
    :param owner_org: The organisation that owns the asset
    :param user: The user who linked the inspection. None if not linked by a user.
    :param linked_at: The time the link occurred
    :param client: Override the client to use. If None, the default client will be used.
    """
    client = client or get_event_client(settings.ASSET_EVENTHUB_NAME)

    linked_at = linked_at or datetime.utcnow()
    creator_org = (
        AssetOrgSummary(
            org_id=user.organisation.id,
            org_type=user.organisation.org_type,
            full_name=user.organisation.full_name,
            country_code=user.organisation.country.code,
        )
        if user and user.organisation
        else None
    )

    event = AssetLinkedToInspectionEvent(
        asset_id=asset_id,
        inspection_id=inspection_id,
        user_id=user.id if user else None,
        linked_at=linked_at,
        creator_org=creator_org,
        owner_org=AssetOrgSummary(
            org_id=owner_org.id,
            org_type=owner_org.org_type,
            full_name=owner_org.full_name,
            country_code=owner_org.country.code,
        ),
    )
    serialized = event.model_dump_json(by_alias=True)
    client.sync_send_events(serialized, event_types=ASSET_LINKED_EVENT)


def send_inspection_created_event(
    inspection: InspectionModel,
    owner_org: Organisations,
    user: CustomUser | None = None,
    created_at: datetime | None = None,
    client: AbstractEventClient | None = None,
):

    client = client or get_event_client(settings.INSPECTION_EVENTHUB_NAME)

    created_at = created_at or datetime.utcnow()
    creator_org = (
        InspectionOrgSummary(
            org_id=user.organisation.id,
            org_type=user.organisation.org_type,
            full_name=user.organisation.full_name,
            country_code=user.organisation.country.code,
        )
        if user and user.organisation
        else None
    )

    date = (
        inspection.date_captured.isoformat()
        if isinstance(inspection.date_captured, datetime)
        else inspection.date_captured
    )

    file_id = (inspection.file or {}).get("id")

    event = InspectionCreatedEvent(
        id=inspection.inspection_id,
        created_at=created_at,
        file_id=file_id,
        user_id=user.id if user else None,
        creator_org=creator_org,
        owner_org=InspectionOrgSummary(
            org_id=owner_org.id,
            org_type=owner_org.org_type,
            full_name=owner_org.full_name,
            country_code=owner_org.country.code,
        ),
        values=InspectionValuesSummary(
            chainage=inspection.chainage, date=date, direction=inspection.direction, notes=inspection.inspection_notes
        ),
    )

    serialized = event.model_dump_json(by_alias=True)
    client.sync_send_events(serialized, event_types=INSPECTION_CREATED_EVENT)


def send_inspection_updated_event(
    inspection: InspectionModel,
    owner_org: Organisations,
    user: CustomUser | None = None,
    updated_at: datetime | None = None,
    client: AbstractEventClient | None = None,
):

    client = client or get_event_client(settings.INSPECTION_EVENTHUB_NAME)

    updated_at = updated_at or datetime.utcnow()

    updater_org = (
        InspectionOrgSummary(
            org_id=user.organisation.id,
            org_type=user.organisation.org_type,
            full_name=user.organisation.full_name,
            country_code=user.organisation.country.code,
        )
        if user and user.organisation
        else None
    )

    date = (
        inspection.date_captured.isoformat()
        if isinstance(inspection.date_captured, datetime)
        else inspection.date_captured
    )

    file_id = (inspection.file or {}).get("id")

    event = InspectionUpdatedEvent(
        id=inspection.inspection_id,
        file_id=file_id,
        updated_at=updated_at,
        user_id=user.id if user else None,
        updater_org=updater_org,
        owner_org=InspectionOrgSummary(
            org_id=owner_org.id,
            org_type=owner_org.org_type,
            full_name=owner_org.full_name,
            country_code=owner_org.country.code,
        ),
        values=InspectionValuesSummary(
            chainage=inspection.chainage, date=date, direction=inspection.direction, notes=inspection.inspection_notes
        ),
    )

    serialized = event.model_dump_json(by_alias=True)
    client.sync_send_events(serialized, event_types=INSPECTION_UPDATED_EVENT)
