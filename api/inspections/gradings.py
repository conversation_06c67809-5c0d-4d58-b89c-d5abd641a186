from vapar.constants.pipes import StandardEnum
from vapar.core.standards import get_standard_static_class

from api.base.models import Header
from api.defects.models import Standard
from api.inspections.models import InspectionValue, VideoFrames, Inspection


def update_gradings(inspection: Inspection):
    """Updates the inspection's (and currently the related mappointlist) condition rating scores.

    :param inspection: The Inspection to update scores on.
    """
    frames = (
        VideoFrames.objects.filter(parent_video=inspection.file)
        .filter(is_hidden=False)
        .prefetch_related("defect_scores")
    )
    structural_scores = [int(frame.defect_scores.structural_score) for frame in frames if frame.defect_scores] or [0]
    service_scores = [int(frame.defect_scores.service_score) for frame in frames if frame.defect_scores] or [0]

    inspection_length_value = InspectionValue.objects.filter(
        inspection=inspection, standard_header__header=Header.objects.filter(name="LengthSurveyed").first()
    ).first()

    try:
        inspection_length = float(inspection_length_value.value)
    except (TypeError, ValueError):
        inspection_length = 0.0

    standard = Standard.objects.get(pk=inspection.standard)
    standard_class = get_standard_static_class(StandardEnum(standard.display_name))

    str_grade = standard_class.get_structural_grading_from_defects(structural_scores, inspection_length)
    ser_grade = standard_class.get_service_grading_from_defects(service_scores, inspection_length)

    inspection.mappointlist.condition_rating = str_grade
    inspection.mappointlist.service_condition_rating = ser_grade
    inspection.mappointlist.save()

    inspection.structural_grade = str_grade
    inspection.service_grade = ser_grade
    inspection.save()

    return str_grade, ser_grade
