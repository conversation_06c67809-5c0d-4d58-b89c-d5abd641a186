from api.common.enums import StatusEnum

# SCHEMA PROPERTIES

inspection_ids = {
    "type": "array",
    "items": {"type": "string"},
    "minItems": 1,
    "description": "list of vapar ids",
}

inspection_uuids = {
    "type": "array",
    "items": {"type": "string"},
    "minItems": 1,
    "description": "list of inspection uuids",
}

# REQUEST SCHEMAS

inspection_id_schema = {
    "type": "object",
    "properties": {"inspection_id": {"type": "string"}},
    "required": ["inspection_id"],
}


base_bulk_inspection_request_schema = {
    "type": "object",
    "properties": {
        "inspection_ids": inspection_uuids,
    },
    "required": ["inspection_ids"],
}


bulk_export_pdf_request_schema = {
    "type": "object",
    "properties": {
        "export_type": {
            "type": "string",
            "enum": ["GROUPED", "ZIP"],
            "description": "GROUPED: all files in one pdf, ZIP: each file in a separate pdf",
        },
        "inspection_ids": inspection_ids,
        "validate": {"type": "boolean"},
    },
    "required": ["export_type", "inspection_ids"],
}


bulk_export_csv_request_schema = {
    "type": "object",
    "properties": {
        "inspection_ids": inspection_ids,
        "type": {
            "type": "string",
            "enum": ["ASSETS", "LINKS", "POINTS"],
            "description": "ASSETS: export asset info only, LINKS: export simplified asset info with links to PDF and"
            "videos, POINTS: export asset info and defect info for each asset",
        },
    },
    "required": ["inspection_ids", "type"],
}


bulk_export_infoasset_request_schema = {
    "type": "object",
    "properties": {
        "inspection_ids": inspection_ids,
        "type": {
            "type": "string",
            "enum": ["ASSETS", "POINTS"],
            "description": "ASSETS: export asset info only, POINTS: export asset info and defect info for each asset",
        },
    },
    "required": ["inspection_ids", "type"],
}


file_upload_request_schema = {
    "type": "object",
    "properties": {
        "job_id": {"type": "string"},
        "file_name": {"type": "string"},
        "upload_file_name": {"type": "string"},
    },
    "required": ["job_id", "file_name", "upload_file_name"],
}


bulk_update_status_request_schema = {
    "type": "object",
    "properties": {
        "inspection_ids": inspection_ids,
        "status": {"type": "string", "enum": StatusEnum.get_status_list()},
    },
}


update_value_request_schema = {
    "type": "object",
    "properties": {
        "value": {"type": "string"},
    },
    "required": ["value"],
}

inspection_filter_request_schema = {
    "type": "object",
    "properties": {
        "organisation": {"type": "integer"},
        "filter_model": {
            "type": "array",
            "items": {"type": "object"},
        },
        "folder_filter_model": {
            "type": "array",
            "items": {"type": "object"},
        },
    },
}


# RESPONSE SCHEMAS
file_download_response_schema = {
    "type": "object",
    "properties": {
        "name": {"type": "string"},
        "url": {"type": "string"},
        "play": {"type": "string"},
    },
    "required": ["name", "url", "play"],
}

file_response_schema = {"type": "string", "format": "binary", "description": "File"}

# TODO: define items for errors
bulk_validation_response_schema = {
    "type": "object",
    "properties": {
        "is_valid": {"type": "boolean"},
        "errors": {
            "type": "array",
            "items": {"type": "object"},
        },
    },
}


create_xml_output_response_schema = {
    "type": "object",
    "properties": {
        "task_id": {"type": "string"},
    },
}


inspection_filter_response_schema = {
    "type": "object",
    "properties": {
        "filter_model": {
            "type": "array",
            "items": {"type": "object"},
        },
        "folder_filter_model": {
            "type": "array",
            "items": {"type": "object"},
        },
    },
}
