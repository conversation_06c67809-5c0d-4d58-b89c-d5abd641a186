# pylint: disable=unused-argument
import uuid
from collections import Counter

from django.contrib.gis.db import models as geomodels
from django.contrib.gis.geos import Point
from django.core.validators import MaxValueValidator, MinValueValidator
from django.db import models
from django.db.models import CheckConstraint, Q, Prefetch
from django.db.models.signals import post_save, m2m_changed
from django.dispatch import receiver
from django.utils import timezone
from treebeard.mp_tree import MP_Node
from vapar.constants.processing import ProcessingStatusReasonEnum, ProcessingStatusEnum

from api.base.models import Header
from api.common.enums import OperatorEnum, StatusEnum, ProcessingRetryableEnum
from api.common.errors import CustomValidationError
from api.common.storage import get_platform_storage_region_base_url
from api.defects.models import (
    DefectModelList,
    DefectScores,
    Standard,
    StandardHeader,
)
from api.organisations.models import Organisations, AssetOwners, Contractors
from api.users.models import CustomUser
from config import settings


def default_filter_model():
    return {"common_filters": {"status": {"value": [], "operator": "IN"}}, "header_filters": []}


def default_folder_filter_model():
    return []


class InspectionFilter(models.Model):
    FILTER_MODEL_SCHEMA = {
        "type": "object",
        "properties": {
            "common_filters": {
                "type": "object",
                "properties": {
                    "status": {
                        "type": "object",
                        "properties": {
                            "value": {"type": "array", "items": {"type": "string"}},
                            "operator": {
                                "type": "string",
                                "enum": [operator_enum.name for operator_enum in OperatorEnum],
                            },
                        },
                        "required": ["value", "operator"],
                    },
                },
                "required": ["status"],
            },
            "header_filters": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "field": {"type": "string"},
                        "operator": {
                            "type": "string",
                            "enum": [operator_enum.name for operator_enum in OperatorEnum],
                        },
                        "value": {"type": "array", "items": {"type": "string"}},
                        "standard": {"type": "integer"},
                    },
                    "required": ["field", "operator", "value"],
                },
            },
        },
    }
    FOLDER_FILTER_MODEL_SCHEMA = {
        "type": "array",
        "items": {
            "type": "object",
            "properties": {
                "id": {"type": "integer"},
                "name": {"type": "string"},
            },
            "required": ["id"],
        },
    }

    uuid = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        CustomUser,
        on_delete=models.CASCADE,
        related_name="inspection_filter",
    )
    organisation = models.ForeignKey(Organisations, on_delete=models.CASCADE)
    filter_model = models.JSONField(default=default_filter_model)
    folder_filter_model = models.JSONField(default=default_folder_filter_model)
    last_modified = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ("user", "organisation")


@receiver(post_save, sender=CustomUser)
def user_post_save(sender, instance, created, *args, **kwargs):
    if created and instance.organisation:
        InspectionFilter.objects.get_or_create(user=instance, organisation=instance.organisation)


@receiver(m2m_changed, sender=AssetOwners.contractor.through)
def linked_org_post_save(sender, instance, action, *args, **kwargs):
    if action == "post_add":
        link_record = AssetOwners.contractor.through.objects.filter(assetowners=instance).order_by("-id").first()
        contractor_org = Organisations.objects.get(id=Contractors.objects.get(id=link_record.contractors_id).org.id)
        asset_owner_org = Organisations.objects.get(id=instance.org.id)
        contractor_users = CustomUser.objects.filter(organisation=contractor_org)

        for user in contractor_users:
            InspectionFilter.objects.get_or_create(user=user, organisation=asset_owner_org)


# Table not used - to be removed with database redesign (referenced in map point list and filelist)
class Jobs(models.Model):
    Organisation = models.ForeignKey(Organisations, on_delete=models.SET_NULL, null=True)
    job_name = models.CharField(max_length=200, blank=False)
    Created_by = models.CharField(max_length=200, blank=False)
    Created_date = models.DateTimeField(blank=False)
    pipe_type_sewer = models.BooleanField(null=True, blank=True)
    standard_key = models.ForeignKey(Standard, on_delete=models.SET_NULL, null=True)

    def __str__(self):
        return str(self.job_name)

    class Meta:
        db_table = "service_jobs"


class JobsTree(MP_Node):
    """
    Represents a node in a folder tree

    The 'primary_org' is the organisation that the folder tree is rooted in. (It should only be set on root nodes)
    All non contractor folders are owned by this organisation.

    The 'secondary_org' is the contractor organisation that owns a particular folder and its children
    """

    primary_org = models.ForeignKey(Organisations, on_delete=models.SET_NULL, null=True, related_name="root_folders")

    secondary_org = models.ForeignKey(
        Organisations,
        on_delete=models.SET_NULL,
        null=True,
        related_name="secondary_org",
    )
    job_name = models.CharField(max_length=200, blank=False)
    created_date = models.DateTimeField(blank=False)
    pipe_type_sewer = models.BooleanField(null=True, blank=True)
    standard_key = models.ForeignKey(Standard, on_delete=models.SET_NULL, null=True)

    def allows_uploads_from(self, upload_org: Organisations) -> bool:
        """
        :returns: Whether the given organisation is allowed to create files + inspections in this folder
        """

        if upload_org.is_asset_owner:
            if self.secondary_org or self.get_ancestors().exclude(secondary_org=None).exists():
                return False  # Asset owners cannot upload to contractor folders

        if upload_org.is_contractor:
            root_folder = self.get_root()
            if (
                root_folder.primary_org != upload_org
                and self.secondary_org != upload_org
                and not self.get_ancestors().filter(secondary_org=upload_org).exists()
            ):  # Contractors can only upload to their own folders
                return False

        return True

    @property
    def can_contain_inspections(self) -> bool:
        """
        :returns: Whether this folder can contain inspections
        """
        if self.job_name == "Recycle Bin":
            return False
        return not self.is_root() and self.is_leaf()

    @property
    def owning_org(self) -> Organisations:
        """
        :returns: The organisation that this folder belongs to
        """
        root = self.get_root()
        return root.primary_org if root.primary_org else self.primary_org

    @property
    def job_full_path(self):
        ancs = self.get_ancestors().values("job_name")
        job_path = [anc["job_name"] for anc in ancs]
        job_path.append(self.job_name)
        job_full_path = " > ".join(job_path)

        return job_full_path

    @property
    def show_path_and_files(self):
        file_name_list = []
        files = FileList.objects.filter(job_tree=self)

        for f in files:
            file_name_list.append(f.filename)

        return {"path": self.job_full_path, "files": file_name_list}

    def __str__(self):
        return self.job_full_path

    class Meta:
        db_table = "service_jobstree"


class ResultsFile(models.Model):
    filename = models.CharField(max_length=200, blank=True)
    file_location = models.CharField(max_length=1000, blank=True)
    file_size = models.CharField(max_length=200, blank=True)
    organisation = models.ForeignKey(Organisations, on_delete=models.SET_NULL, null=True)
    header_only = models.BooleanField(default=False)

    class Meta:
        db_table = "service_resultsfile"


class FileListQuerySet(models.QuerySet):
    def for_org(self, org: Organisations):
        return self.filter(Q(upload_org=org) | Q(target_org=org))


class FileList(models.Model):
    filename = models.CharField(max_length=200, blank=True)
    url = models.CharField(max_length=1000, blank=True)
    file_size = models.CharField(max_length=200, blank=True)
    file_type = models.CharField(max_length=50, blank=True)
    created_time = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    upload_user = models.CharField(max_length=200, blank=False)
    target_org = models.ForeignKey(Organisations, on_delete=models.SET_NULL, null=True, related_name="target_org")
    upload_org = models.ForeignKey(Organisations, on_delete=models.SET_NULL, null=True, related_name="upload_org")
    total_frames = models.IntegerField(default=0)
    hidden = models.BooleanField(default=False)
    job = models.ForeignKey(Jobs, on_delete=models.SET_NULL, null=True)
    results_file = models.ForeignKey(ResultsFile, on_delete=models.SET_NULL, null=True)
    request_endpoint = models.CharField(max_length=500, blank=True, default="")
    job_tree = models.ForeignKey(JobsTree, related_name="jt", on_delete=models.SET_NULL, null=True)
    play_url = models.CharField(max_length=1000, blank=True)
    uploaded_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True)
    storage_region = models.CharField(max_length=20, null=False, default="AU")
    upload_completed = models.BooleanField(default=False)
    upload_completed_time = models.DateTimeField(null=True, blank=True)
    processing_started_time = models.DateTimeField(null=True, blank=True)
    processing_completed_time = models.DateTimeField(null=True, blank=True)

    objects = FileListQuerySet.as_manager()

    def is_accessible_by_org(self, org: Organisations) -> bool:
        """
        :returns: Whether the file is accessible by the given organisation
        """
        return self.target_org == org or self.upload_org == org

    class Meta:
        db_table = "service_filelist"


class VideoFrameQuerySet(models.QuerySet):
    def only_reported(self):
        """
        Only return reportable frames
        """
        return self.filter(is_hidden=False).filter(defect_scores__is_shown=True)

    def with_defect_score_severity(self):
        """
        Prefetch related defect scores, annotated with severity
        """

        defects = DefectScores.objects.with_severity().all()
        qs = self.prefetch_related(Prefetch("defect_scores", queryset=defects))
        return qs


class VideoFrames(models.Model):
    image_location = models.CharField(max_length=200, blank=True)
    parent_video = models.ForeignKey(FileList, on_delete=models.CASCADE)
    frame_id = models.IntegerField(default=0)
    time_reference = models.CharField(max_length=20, default="", blank=True, null=True)
    chainage = models.CharField(max_length=10, default="0", blank=True, null=True)
    chainage_number = models.FloatField(
        validators=[MinValueValidator(0.0), MaxValueValidator(settings.MAX_CHAINAGE)],
        default=0.0,
        blank=False,
        null=False,
    )
    class_label = models.CharField(max_length=200, blank=True)
    class_certainty = models.DecimalField(max_digits=5, decimal_places=2, blank=True)
    duplicate_of = models.IntegerField(default=-1)
    all_class_breakdown = models.CharField(max_length=1000, blank=True)
    fix_user = models.CharField(max_length=200, blank=True, null=True)
    original_fixed_label = models.CharField(max_length=200, blank=True, null=True)
    is_accepted = models.BooleanField(default=False)
    reviewed_by = models.CharField(max_length=200, blank=False, null=True)
    review_timestamp = models.DateTimeField(blank=True, null=True)
    is_hidden = models.BooleanField(default=False)
    at_joint = models.BooleanField(blank=True, null=True, default=None)
    at_clock = models.IntegerField(blank=True, null=True, default=None)
    to_clock = models.IntegerField(blank=True, null=True, default=None)
    cont_defect_start = models.BooleanField(default=False)
    cont_defect_end = models.DecimalField(blank=True, null=True, default=None, max_digits=10, decimal_places=2)
    quantity1_value = models.DecimalField(blank=True, null=True, default=None, max_digits=10, decimal_places=2)
    quantity1_units = models.CharField(max_length=200, blank=True, null=True, default=None)
    quantity2_value = models.DecimalField(blank=True, null=True, default=None, max_digits=10, decimal_places=2)
    quantity2_units = models.CharField(max_length=200, blank=True, null=True, default=None)
    is_matched = models.BooleanField(blank=True, null=True, default=None)
    remarks = models.TextField(blank=True, null=True)
    defect_model = models.ForeignKey(DefectModelList, on_delete=models.SET_NULL, null=True)
    defect_scores = models.ForeignKey(DefectScores, on_delete=models.SET_NULL, null=True)

    objects = VideoFrameQuerySet.as_manager()

    class Meta:
        ordering = ["frame_id"]
        db_table = "service_videoframes"

        constraints = [
            CheckConstraint(
                check=Q(at_clock__gte=1, at_clock__lte=12) | Q(at_clock__isnull=True),
                name="video_frame_at_clock_range_constraint",
            ),
            CheckConstraint(
                check=Q(to_clock__gte=1, to_clock__lte=12) | Q(to_clock__isnull=True),
                name="video_frame_to_clock_range_constraint",
            ),
        ]

    def validate(self):
        errors = []

        # Common validation checks
        if self.defect_scores.clock_position_required and self.at_clock is None:
            error = CustomValidationError(
                title="Clock Start",
                message="'Clock Start' value missing for frame at chainage {}".format(self.chainage_number),
                field_name="atClock",
                entity="defect",
                metadata={"frame_id": self.id},
            )
            errors.append(error.serialize())

        if self.defect_scores.clock_spread_possible and self.to_clock is not None and self.at_clock is None:
            error = CustomValidationError(
                title="Clock Start",
                message="'Clock Start' value missing for frame at chainage {}".format(self.chainage_number),
                field_name="atClock",
                entity="defect",
                metadata={"frame_id": self.id},
            )
            errors.append(error.serialize())

        if not self.defect_scores.clock_spread_possible and self.to_clock is not None:
            error = CustomValidationError(
                title="Clock End",
                message="'Clock End' value should be empty for frame at chainage {}".format(self.chainage_number),
                field_name="toClock",
                entity="defect",
                metadata={"frame_id": self.id},
            )
            errors.append(error.serialize())

        if not self.defect_scores.at_joint_required and self.at_joint:
            error = CustomValidationError(
                title="At Joint",
                message="'At Joint' should be unchecked for frame at chainage {}".format(self.chainage_number),
                field_name="atJoint",
                entity="defect",
                metadata={"frame_id": self.id},
            )
            errors.append(error.serialize())

        if self.defect_scores.percentage_required:
            if self.quantity1_units != "%":
                error = CustomValidationError(
                    title="Quantity 1 Units",
                    message="'Quantity 1 Units' value should be '%' for frame at chainage {}".format(
                        self.chainage_number
                    ),
                    field_name="quantity1Units",
                    entity="defect",
                    metadata={"frame_id": self.id},
                )
                errors.append(error.serialize())

            if self.quantity1_value == "" or self.quantity1_value is None:
                error = CustomValidationError(
                    title="Quantity 1",
                    message="'Quantity 1' value missing for frame at chainage {}".format(self.chainage_number),
                    field_name="quantity1Value",
                    entity="defect",
                    metadata={"frame_id": self.id},
                )
                errors.append(error.serialize())

        return errors


class ImportedInspectionFile(models.Model):
    PROCESS_ACTIONS = [
        ("ignore_duplicates", "Ignore Duplicates"),
        ("overwrite_duplicates", "Overwrite Duplicates"),
        ("create_duplicates", "Create Duplicates"),
        ("awaiting_action", "Awaiting Action"),
    ]

    uuid = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    blob_storage = models.CharField(max_length=250, null=True)
    file_name = models.CharField(max_length=50, null=True)
    folder = models.ForeignKey(JobsTree, on_delete=models.SET_NULL, null=True)
    organisation = models.ForeignKey(Organisations, on_delete=models.CASCADE, null=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=False)
    created_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True)
    imported = models.BooleanField(null=True)
    import_action = models.CharField(choices=PROCESS_ACTIONS, null=True, max_length=25)

    def save(self, *args, **kwargs):
        base_url = get_platform_storage_region_base_url()
        self.blob_storage = f"{base_url}{settings.BLOB_STORAGE_DRT_CONTAINER}/{self.uuid}.csv"
        super().save(*args, **kwargs)


class Asset(models.Model):
    class AssetType(models.TextChoices):
        PIPE = "pipe", "Pipe"
        MANHOLE = "manhole", "Manhole"

    uuid = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    type = models.CharField(choices=AssetType.choices, default=AssetType.PIPE, max_length=7)
    organisation = models.ForeignKey(Organisations, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True, blank=False)

    def get_asset_values(self, get_value_dict: bool = False, use_header_names: bool = False):
        asset_values = self.assetvalue_set.all()

        if get_value_dict:
            asset_details = {}
            for asset_value in asset_values:
                detail_key = (
                    asset_value.standard_header.header.name if use_header_names else asset_value.standard_header.name
                )
                asset_details[detail_key] = asset_value.value

            return asset_details
        else:
            return asset_values

    @property
    def standard(self) -> int | None:
        asset_values = self.get_asset_values()
        if len(asset_values) > 0:
            cnt = Counter([av.standard_header.standard_id for av in asset_values])
            asset_standard_id = cnt.most_common()[0][0]
            return asset_standard_id
        return None


class InspectionQuerySet(models.QuerySet):
    def visible_to_org(self, organisation: Organisations):
        """
        Filter to inspections that are visible to the given organisation
        """
        qs = self.exclude(file__hidden=True)
        if organisation.is_contractor:
            qs = qs.filter(
                Q(file__target_org=organisation)
                | Q(asset__organisation=organisation)
                | Q(file__upload_org=organisation)
            )
        else:
            qs = qs.filter(Q(file__target_org=organisation) | Q(asset__organisation=organisation))
        return qs


class InspectionModelManager(models.Manager):
    def _generate_vapar_id(self, file_id: int | None) -> str | None:
        if not file_id:
            return
        # footage_owner = self.file.target_org
        footage_owner = FileList.objects.get(pk=file_id).target_org
        org_inspections = Inspection.objects.filter(file__target_org=footage_owner).exclude(legacy_id__isnull=True)
        inpsection_legacy_ids = org_inspections.values_list("legacy_id", flat=True)
        legacy_ids = [0]
        for legacy_id in inpsection_legacy_ids:
            # check old style id (<auto-increment>) or new (<org_id>-<org_specific_incremental>)
            try:
                legacy_ids.append(int(legacy_id))
            except ValueError:
                legacy_ids.append(int(legacy_id.split("-")[-1]))
        legacy_ids.sort(reverse=True)

        # build new style ID always
        increment = 1
        new_vapar_id = f"{footage_owner.id}-{legacy_ids[0] + increment:08d}"
        # ensure the legacy ID doesn't exist
        while Inspection.objects.filter(legacy_id=new_vapar_id).exists():
            increment += 1
            new_vapar_id = f"{footage_owner.id}-{legacy_ids[0] + increment:08d}"

        return new_vapar_id

    def get_queryset(self):
        return InspectionQuerySet(self.model, using=self._db)

    def create(self, **obj_data):
        if "legacy_id" not in obj_data or not obj_data["legacy_id"]:
            obj_data["legacy_id"] = self._generate_vapar_id(obj_data.get("file_id"))
        return super().create(**obj_data)

    def visible_to_org(self, organisation: Organisations):
        """
        Filter to inspections that are visible to the given organisation
        """
        return self.get_queryset().visible_to_org(organisation)


class Inspection(models.Model):
    uuid = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    legacy_id = models.CharField(unique=True, null=True, max_length=20)
    asset = models.ForeignKey(Asset, on_delete=models.SET_NULL, null=True)
    folder = models.ForeignKey(JobsTree, on_delete=models.SET_NULL, null=True)
    status = models.CharField(
        max_length=50,
        null=False,
        default=StatusEnum.UPLOADED.value,
        choices=[(status.value, status.value) for status in StatusEnum],
    )
    file = models.OneToOneField(FileList, on_delete=models.SET_NULL, null=True, blank=True)
    service_grade = models.IntegerField(default=1, null=False)
    structural_grade = models.IntegerField(default=1, null=False)
    created_at = models.DateTimeField(auto_now_add=True, blank=False)
    created_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True)
    # A timestamp field which is updated through signals when frames, values, etc. are updated
    last_related_update = models.DateTimeField(auto_now=True)

    objects = InspectionModelManager.from_queryset(InspectionQuerySet)()

    @property
    def standard(self) -> int | None:
        inspection_values = self.get_inspection_values()
        if len(inspection_values) > 0:
            cnt = Counter([iv.standard_header.standard_id for iv in inspection_values])
            inspection_standard_id = cnt.most_common()[0][0]
            return inspection_standard_id
        return None

    def __str__(self):
        return str(self.uuid)

    def generate_vapar_id(self) -> str:
        footage_owner = self.file.target_org
        org_inspections = type(self).objects.filter(file__target_org=footage_owner).exclude(legacy_id__isnull=True)
        inpsection_legacy_ids = org_inspections.values_list("legacy_id", flat=True)
        legacy_ids = [0]
        for legacy_id in inpsection_legacy_ids:
            # check old style id (<auto-increment>) or new (<org_id>-<org_specific_incremental>)
            try:
                legacy_ids.append(int(legacy_id))
            except ValueError:
                legacy_ids.append(int(legacy_id.split("-")[-1]))
        legacy_ids.sort(reverse=True)

        # build new style ID always
        increment = 1
        new_vapar_id = f"{footage_owner.id}-{legacy_ids[0] + increment:08d}"
        # ensure the legacy ID doesn't exist
        while type(self).objects.filter(legacy_id=new_vapar_id).exists():
            increment += 1
            new_vapar_id = f"{footage_owner.id}-{legacy_ids[0] + increment:08d}"

        return new_vapar_id

    def create_inspection_value(
        self,
        standard_header: StandardHeader,
        value: str,
    ) -> "InspectionValue":
        inspection_value = InspectionValue(
            inspection=self,
            standard_header=standard_header,
            value=value,
            original_value=value,
            created_at=timezone.now(),
        )
        inspection_value.save()
        return inspection_value

    def get_inspection_values(self, get_value_dict=False, use_header_names=False):
        inspection_values = InspectionValue.objects.filter(inspection=self).prefetch_related(
            "standard_header", "standard_header__header"
        )

        if get_value_dict:
            inspection_details = {}

            for inspection_value in inspection_values:
                detail_key = (
                    inspection_value.standard_header.header.name
                    if use_header_names
                    else inspection_value.standard_header.name
                )
                inspection_details[detail_key] = inspection_value.value

            return inspection_details
        else:
            return inspection_values

    def combine_asset_inspection_values(self, get_value_dict=False):
        inspection_details = self.get_inspection_values(get_value_dict=get_value_dict)
        asset_details = self.asset.get_asset_values(get_value_dict=get_value_dict)
        inspection_details.update(asset_details)

        return inspection_details

    def get_start_node_inspection_value(self):
        start_node_header = Header.objects.filter(name="StartNodeRef").first()
        return InspectionValue.objects.filter(inspection=self, standard_header__header=start_node_header.uuid).first()

    def get_end_node_inspection_value(self):
        end_node_header = Header.objects.filter(name="FinishNodeRef").first()
        return InspectionValue.objects.filter(inspection=self, standard_header__header=end_node_header.uuid).first()


class AssetValue(models.Model):
    MAX_VALUE_LENGTH = 255
    ENFORCE_UNIQUE = False

    uuid = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    asset = models.ForeignKey(Asset, on_delete=models.CASCADE)
    standard_header = models.ForeignKey(StandardHeader, on_delete=models.CASCADE)
    value = models.CharField(max_length=MAX_VALUE_LENGTH, null=True, blank=True)
    original_value = models.CharField(max_length=MAX_VALUE_LENGTH, null=True)
    created_at = models.DateTimeField(auto_now_add=True)


class InspectionValue(models.Model):
    uuid = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    inspection = models.ForeignKey(Inspection, on_delete=models.CASCADE)
    standard_header = models.ForeignKey(StandardHeader, on_delete=models.CASCADE)
    value = models.TextField(max_length=1000)
    original_value = models.TextField(max_length=1000, null=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=False)

    def __str__(self):
        return f"InspectionValue<standard_header_name='{self.standard_header.name}' header_name='{self.standard_header.header.name}' value='{self.value}'>"


class MapPointList(models.Model):
    # TODO we need a better solution here for storing enums.
    PLANNED_STATUS = "Planned"

    STATUS_CHOICE_LIST = [
        "Planned",
        "Uploaded",
        "Reviewed",
        "Decision",
        "Repair Plan",
        "Actioned",
        "Complete",
        "Archived",
    ]

    STATUS_CHOICES = [
        ("Planned", "Planned"),
        ("Uploaded", "Uploaded"),
        ("Reviewed", "Reviewed"),
        ("Decision", "Decision"),
        ("Repair Plan", "Repair Plan"),
        ("Actioned", "Actioned"),
        ("Complete", "Complete"),
        ("Archived", "Archived"),
    ]

    DIRECTION_CHOICES = [
        ("Downstream", "Downstream"),
        ("Upstream", "Upstream"),
        ("Unknown", "Unknown"),
    ]

    name = models.CharField(max_length=200, blank=False)
    geometry = geomodels.PointField(blank=True, null=True)
    chainage = models.FloatField(
        validators=[MinValueValidator(0.0), MaxValueValidator(settings.MAX_CHAINAGE)],
        default=0.0,
        blank=False,
        null=False,
    )
    chainage_unit = models.CharField(max_length=10, default="m", blank=True, null=True)
    diameter = models.CharField(max_length=10, default="0", blank=True, null=True)
    condition_rating = models.IntegerField(choices=list(zip(range(1, 10), range(1, 10))), null=True)
    service_condition_rating = models.IntegerField(choices=list(zip(range(1, 5), range(1, 5))), null=True)
    date_captured = models.DateField(blank=True, null=True)
    asset_id = models.CharField(max_length=100, blank=True, null=True)
    first_frame = models.CharField(max_length=1000, blank=True, null=True)
    associated_file = models.OneToOneField(FileList, on_delete=models.SET_NULL, null=True)
    material = models.CharField(max_length=100, blank=True, null=True)
    start_node = models.CharField(max_length=100, blank=True, null=True)
    end_node = models.CharField(max_length=100, blank=True, null=True)
    direction = models.CharField(max_length=100, choices=DIRECTION_CHOICES, null=True)
    upstream_node = models.CharField(max_length=100, blank=True, null=True)
    downstream_node = models.CharField(max_length=100, blank=True, null=True)
    process_model_id = models.CharField(max_length=100, blank=True, null=True)
    cr_model_id = models.CharField(max_length=1000, blank=True, null=True)
    ser_cr_model_id = models.CharField(max_length=1000, blank=True, null=True)
    pipe_type = models.CharField(max_length=10, default="SS")
    is_accepted = models.BooleanField(default=False)
    reviewed_by = models.CharField(max_length=200, blank=False, null=True)
    review_timestamp = models.DateTimeField(blank=True, null=True)
    water_level_warning = models.BooleanField(blank=False, default=False)
    loss_of_vision_warning = models.BooleanField(blank=False, default=False)
    water_level_url = models.CharField(max_length=200, default="", blank=True)
    job = models.ForeignKey(Jobs, on_delete=models.SET_NULL, null=True)
    sewer_data = models.BooleanField(default=True, null=True)
    standard_key = models.ForeignKey(Standard, on_delete=models.SET_NULL, null=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default="Uploaded")
    inspection_notes = models.CharField(max_length=1000, blank=True, null=True)
    ocr_error_detect = models.FloatField(max_length=10, default=0.0, blank=True, null=True)
    ocr_error_recog = models.FloatField(max_length=10, default=0.0, blank=True, null=True)
    ocr_used_azure = models.BooleanField(default=True, null=True)
    orig_str_grade = models.IntegerField(blank=True, null=True)
    orig_ser_grade = models.IntegerField(blank=True, null=True)
    repair_completed_date = models.DateField(blank=True, null=True)
    inspection = models.OneToOneField(Inspection, on_delete=models.SET_NULL, null=True, blank=True)
    deleted_at = models.DateTimeField(blank=True, null=True)

    def set_up_down_node(self, *args, **kwargs):
        if self.direction == "Upstream":
            self.upstream_node = self.end_node
            self.downstream_node = self.start_node

        elif self.direction == "Downstream":
            self.upstream_node = self.start_node
            self.downstream_node = self.end_node

    def set_start_end_node(self, *args, **kwargs):
        if self.direction == "Upstream":
            self.end_node = self.upstream_node
            self.start_node = self.downstream_node

        elif self.direction == "Downstream":
            self.start_node = self.upstream_node
            self.end_node = self.downstream_node

    def set_geometry(self, *args, **kwargs):
        self.geometry = Point(x=float(kwargs["longitude"]), y=float(kwargs["latitude"]))

    def get_inspection_with_standard_headers(self, *args, **kwargs):
        inspection = {}
        headers = StandardHeader.objects.filter(standard=self.standard_key)
        for header in headers:
            if header.header.mapped_mpl_field:
                inspection[header.name] = getattr(self, header.header.mapped_mpl_field)
        return inspection

    def validate(self):
        validation = {
            "is_valid": True,
            "inspection_id": self.id,
            "inspection_name": self.name,
            "errors": [],
        }
        is_valid = True
        standard = self.standard_key

        # --- INSPECTION VALIDATION ---

        # --- Common inspection validation checks ---

        # Retrieve all frames
        frames = (
            VideoFrames.objects.prefetch_related("parent_video", "defect_model", "defect_scores")
            .filter(
                parent_video=self.associated_file,
                is_hidden=False,
                defect_scores__isnull=False,
                defect_scores__is_shown=True,
            )
            .order_by("frame_id")
        )

        # if len(frames) > 0:
        #     # start_survey → exactly 1 present per inspection, must be the very first code
        #     if not frames.first().defect_scores.start_survey:
        #         error = CustomValidationError(
        #             title="Start Survey",
        #             message="First defect not marked as 'Start Survey'",
        #             field_name="defectDescription",
        #             entity="defect",
        #             metadata={
        #                 "frame_id": frames.first().id,
        #             },
        #         )
        #         validation["errors"].append(error.serialize())

        #     if frames.filter(defect_scores__start_survey=True).count() > 1:
        #         error = CustomValidationError(
        #             title="Start Survey",
        #             message="Multiple defects marked as 'Start Survey'",
        #             field_name="defectDescription",
        #             entity="defect",
        #             metadata={
        #                 "frame_id": frames.first().id,
        #             },
        #         )
        #         validation["errors"].append(error.serialize())

        #     # end_survey → exactly 1 present per inspection, must be the very last code
        #     if not frames.last().defect_scores.end_survey:
        #         error = CustomValidationError(
        #             title="End Survey",
        #             message="Finish node or Survey Abandoned code missing / is not the last code",
        #             field_name="defectDescription",
        #             entity="defect",
        #             metadata={
        #                 "frame_id": frames.last().id,
        #             },
        #         )
        #         validation["errors"].append(error.serialize())

        #     if frames.filter(defect_scores__end_survey=True).count() > 1:
        #         error = CustomValidationError(
        #             title="End Survey",
        #             message="Multiple defects marked as 'End Survey'",
        #             field_name="defectDescription",
        #             entity="defect",
        #             metadata={
        #                 "frame_id": frames.last().id,
        #             },
        #         )
        #         validation["errors"].append(error.serialize())

        if self.inspection is not None:
            validation["errors"].extend(self.inspection.validate())

        # --- ASSET VALIDATION ---

        if self.inspection is not None:
            asset = self.inspection.asset
            asset_values = AssetValue.objects.filter(asset=asset)
            validation["errors"].extend(standard.validate_asset(asset=asset, asset_values=asset_values))

        # --- DEFECT VALIDATION ---

        for frame in frames:
            validation["errors"].extend(frame.validate())

        is_valid = len(validation["errors"]) == 0
        validation["is_valid"] = is_valid
        return is_valid, validation

    def get_folder_id(self):
        if self.associated_file and self.associated_file.job_tree:
            return self.associated_file.job_tree.id
        elif self.inspection:
            return self.inspection.folder.id

    def soft_delete(self):
        self.deleted_at = timezone.now()
        self.save()

    class Meta:
        verbose_name_plural = "points"
        db_table = "service_mappointlist"


class ProcessingList(models.Model):
    RETRYABLE_STATUS_REASONS = {
        ProcessingStatusReasonEnum.GENERIC_ERROR,
        ProcessingStatusReasonEnum.TIMEOUT_WHILE_WAITING,
        ProcessingStatusReasonEnum.TIMEOUT_WHILE_PROCESSING,
    }

    MAX_RETRIES = 3

    filename = models.CharField(max_length=200, blank=True)
    file_size = models.CharField(max_length=200, blank=True)

    status = models.CharField(
        max_length=200,
        blank=True,
        default=ProcessingStatusEnum.UPLOADING,
        choices=ProcessingStatusEnum.as_choices(),
    )
    status_reason = models.CharField(
        max_length=2,
        blank=True,
        null=True,
        choices=ProcessingStatusReasonEnum.as_choices(),
        default=None,
    )
    times_retried = models.PositiveIntegerField(default=0)

    created_time = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    upload_user = models.CharField(max_length=200, blank=False)

    target_org = models.ForeignKey(Organisations, on_delete=models.SET_NULL, null=True)
    associated_file = models.OneToOneField(
        FileList, on_delete=models.SET_NULL, null=True, related_name="processing_record"
    )

    request_endpoint = models.CharField(max_length=500, blank=True, default="")
    manual_qa_required = models.BooleanField(default=False)
    sewer_data = models.BooleanField(default=True)
    standard_key = models.ForeignKey(Standard, on_delete=models.SET_NULL, null=True)
    upload_completed = models.BooleanField(default=False)

    @property
    def retryable_state(self) -> ProcessingRetryableEnum:
        if self.times_retried >= self.MAX_RETRIES:
            return ProcessingRetryableEnum.MAX_RETRIES_EXCEEDED

        if (
            self.status == ProcessingStatusEnum.FAILED_TO_PROCESS
            and self.status_reason in self.RETRYABLE_STATUS_REASONS
        ):
            return ProcessingRetryableEnum.AVAILABLE

        return ProcessingRetryableEnum.BAD_STATE

    @property
    def is_retryable(self) -> bool:
        return self.retryable_state == ProcessingRetryableEnum.AVAILABLE

    def retry(self):
        """
        Mark the processing record as retrying a processing attempt.
        """
        self.times_retried += 1
        self.status = ProcessingStatusEnum.WAITING_TO_PROCESS
        self.status_reason = None
        self.save()

    class Meta:
        db_table = "service_processinglist"


class ResultsFileAssets(models.Model):
    results_file = models.ForeignKey(ResultsFile, on_delete=models.SET_NULL, null=True)
    associated_file = models.ForeignKey(FileList, on_delete=models.SET_NULL, null=True)
    video_filename = models.CharField(max_length=200, blank=True)
    asset_id = models.CharField(max_length=100, blank=True, null=True)
    start_node = models.CharField(max_length=100, blank=True, null=True)
    end_node = models.CharField(max_length=100, blank=True, null=True)
    direction = models.CharField(max_length=100, blank=True, null=True)
    chainage = models.CharField(max_length=10, default="0", blank=True, null=True)
    date_captured = models.DateField(blank=True, null=True)

    class Meta:
        db_table = "service_resultsfileassets"


class ResultsFileDefects(models.Model):
    results_file_asset = models.ForeignKey(ResultsFileAssets, on_delete=models.SET_NULL, null=True)
    associated_video_frame = models.ForeignKey(VideoFrames, on_delete=models.SET_NULL, null=True)
    time_reference = models.CharField(max_length=20, default="", blank=True, null=True)
    chainage_number = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    comment = models.CharField(max_length=200, blank=True)
    defect_code = models.CharField(max_length=200, blank=True)

    class Meta:
        db_table = "service_resultsfiledefects"


# TODO: the following are tables to be removed with database redesign
class File(models.Model):
    filename = models.CharField(max_length=200, blank=True)
    url = models.CharField(max_length=200, blank=True)
    file_size = models.CharField(max_length=200, blank=True)
    file_type = models.CharField(max_length=50, blank=True)
    created_time = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = "service_file"


class ConditionRatingChanges(models.Model):
    associated_file = models.OneToOneField(FileList, on_delete=models.SET_NULL, null=True)
    original_condition_rating = models.IntegerField(choices=list(zip(range(1, 10), range(1, 10))), null=True)
    new_condition_rating = models.IntegerField(choices=list(zip(range(1, 10), range(1, 10))), null=True)
    fix_user = models.CharField(max_length=200, blank=False, default="")
    fix_time = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = "service_conditionratingchanges"
