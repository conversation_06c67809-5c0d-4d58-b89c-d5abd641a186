"""Queue trigger for AI processing function

Sets up the queue trigger for newly uploaded files, so that they can be processed.
"""

import urllib.parse

from azure.storage.queue import (
    QueueClient,
    QueueMessage,
    BinaryBase64EncodePolicy,
    BinaryBase64DecodePolicy,
)
from django.conf import settings


def get_queue_client(region: str = "default") -> QueueClient:
    # Setup Base64 encoding and decoding functions
    base64_queue_client = QueueClient.from_connection_string(
        conn_str=settings.PLATFORM_STORAGE_ACCOUNT_REGION_CONN_STRS[region],
        queue_name=settings.VIDEO_PROCESSING_AZURE_STORAGE_QUEUE_NAME,
        message_encode_policy=BinaryBase64EncodePolicy(),
        message_decode_policy=BinaryBase64DecodePolicy(),
    )
    return base64_queue_client


def enqueue_message(blob_path: str, region: str = "default") -> QueueMessage:
    base_url = settings.PROCESSING_BLOB_STORAGE_REGION_URLS[region]
    url = urllib.parse.urljoin(base_url, blob_path)
    client = get_queue_client(region)
    encoded_url = url.encode()
    res = client.send_message(encoded_url)
    return res
