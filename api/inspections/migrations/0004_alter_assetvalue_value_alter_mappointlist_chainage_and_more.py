# Generated by Django 4.1.2 on 2024-05-01 05:13

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("inspections", "0003_remove_assetvalue_imported_inspection_file_and_more"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="assetvalue",
            name="value",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="mappointlist",
            name="chainage",
            field=models.FloatField(
                default=0.0,
                validators=[
                    django.core.validators.MinValueValidator(0.0),
                    django.core.validators.MaxValueValidator(9999),
                ],
            ),
        ),
        migrations.AlterField(
            model_name="videoframes",
            name="chainage_number",
            field=models.FloatField(
                default=0.0,
                validators=[
                    django.core.validators.MinValueValidator(0.0),
                    django.core.validators.MaxValueValidator(9999),
                ],
            ),
        ),
    ]
