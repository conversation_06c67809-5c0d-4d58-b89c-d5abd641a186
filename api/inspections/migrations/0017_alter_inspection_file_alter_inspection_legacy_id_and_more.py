# Generated by Django 4.1.2 on 2024-10-11 00:48

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("organisations", "0001_initial"),
        ("inspections", "0016_auto_20240801_0602"),
    ]

    operations = [
        migrations.AlterField(
            model_name="inspection",
            name="file",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="inspections.filelist",
            ),
        ),
        migrations.AlterField(
            model_name="inspection",
            name="legacy_id",
            field=models.CharField(max_length=20, null=True, unique=True),
        ),
        migrations.AlterField(
            model_name="jobstree",
            name="primary_org",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="root_folders",
                to="organisations.organisations",
            ),
        ),
        migrations.AlterField(
            model_name="mappointlist",
            name="inspection",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="inspections.inspection",
            ),
        ),
        migrations.AlterField(
            model_name="processinglist",
            name="status",
            field=models.CharField(
                blank=True,
                choices=[
                    ("Uploading", "Uploading"),
                    ("Failed To Process", "Failed To Process"),
                    ("Analysing Video", "Analysing Video"),
                    ("Storing Results", "Storing Results"),
                    ("Waiting to process", "Waiting To Process"),
                    ("Upload Failed", "Upload Failed"),
                    ("Processing", "Processing"),
                ],
                default="Uploading",
                max_length=200,
            ),
        ),
    ]
