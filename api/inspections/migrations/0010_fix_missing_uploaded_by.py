from django.db import migrations


def populate_uploaded_by(apps, schema_editor):
    CustomUser = apps.get_model("users", "CustomUser")
    FileList = apps.get_model("inspections", "FileList")

    objects_to_update = FileList.objects.filter(uploaded_by__isnull=True)
    created_users, seen_emails = {}, set()

    for file in objects_to_update:
        user = CustomUser.objects.filter(organisation=file.upload_org).filter(full_name=file.upload_user).first()

        if not user:
            potential_users = CustomUser.objects.filter(full_name__iexact=file.upload_user)
            if potential_users.count() == 1:
                user = potential_users.first()
            else:
                if file.upload_user in created_users:
                    user = created_users[file.upload_user]
                else:
                    slug = file.upload_user.replace(" ", "").lower()
                    email = f"{slug}@fixmissinguploadedby.mig"
                    # Ensure the email is unique
                    count = 1
                    while email in seen_emails or CustomUser.objects.filter(email=email).exists():
                        email = f"{slug}{count}@fixmissinguploadedby.mig"
                        count += 1
                    seen_emails.add(email)
                    user = CustomUser.objects.create(
                        full_name=file.upload_user, organisation=None, group=2, email=email
                    )
                    created_users[file.upload_user] = user
        file.uploaded_by = user
        file.save()


class Migration(migrations.Migration):
    atomic = False

    dependencies = [
        ("inspections", "0009_alter_inspection_status"),
    ]

    operations = [
        migrations.RunPython(populate_uploaded_by, migrations.RunPython.noop),
    ]
