# Generated by Django 4.1.2 on 2024-05-17 00:48

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        (
            "inspections",
            "0012_auto_20240701_0523",
        ),
    ]

    operations = [
        migrations.AddField(
            model_name="processinglist",
            name="status_reason",
            field=models.CharField(
                blank=True,
                choices=[("GE", "Generic Error"), ("FC", "File Corrupted")],
                default=None,
                max_length=2,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="processinglist",
            name="times_retried",
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AlterField(
            model_name="processinglist",
            name="associated_file",
            field=models.OneToOneField(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="processing_record",
                to="inspections.filelist",
            ),
        ),
        migrations.AlterField(
            model_name="processinglist",
            name="status",
            field=models.CharField(
                blank=True,
                choices=[
                    ("Uploading", "Uploading"),
                    ("Failed To Process", "Failed To Process"),
                    ("Analysing Video", "Analysing Video"),
                    ("Storing Results", "Storing Results"),
                    ("Waiting to process", "Waiting To Process"),
                    ("Upload Failed", "Upload Failed"),
                ],
                default="Uploading",
                max_length=200,
            ),
        ),
    ]
