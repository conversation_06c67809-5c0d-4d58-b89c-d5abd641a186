# Generated by Django 4.1.2 on 2024-10-24 05:02

from django.db import migrations
from django.db.models import F


def populate_asset_pipe_types(apps, schema_editor):
    """
    Create UseOfDrainSewer AssetValue records for assets that are missing them.
    """

    Asset = apps.get_model("inspections", "Asset")
    AssetValue = apps.get_model("inspections", "AssetValue")
    StandardHeader = apps.get_model("defects", "StandardHeader")

    standard_id_to_standard_header = {
        sh.standard.id: sh for sh in StandardHeader.objects.filter(header__name="UseOfDrainSewer", header__type="asset")
    }

    # Delete blank AssetValue records
    AssetValue.objects.filter(
        standard_header__header__name="UseOfDrainSewer",
        standard_header__header__type="asset",
        value="",
    ).delete()

    assets_with_missing_header = (
        Asset.objects.filter(type="pipe")
        .exclude(assetvalue__standard_header__header__name="UseOfDrainSewer")
        .distinct()
    )

    # For assets that have an inspection, we can use its folder's pipe_type_sewer field to determine the pipe type.
    assets_with_inspection = assets_with_missing_header.filter(inspection__folder__isnull=False).annotate(
        pipe_type_sewer=F("inspection__folder__pipe_type_sewer"),
        standard_id=F("inspection__folder__standard_key_id"),
    )

    new_asset_values = []
    for asset in assets_with_inspection:
        sh = standard_id_to_standard_header.get(asset.standard_id)
        if not sh:
            continue  # No UseOfDrainSewer header for this standard?

        val = "SS" if asset.pipe_type_sewer else "SW"
        av = AssetValue(
            asset=asset,
            standard_header=sh,
            value=val,
            original_value=val,
        )
        new_asset_values.append(av)

    # For assets without an inspection, fallback to using the org's sewer_data field.
    assets_without_inspection = assets_with_missing_header.exclude(pk__in=assets_with_inspection).annotate(
        sewer_data=F("organisation__sewer_data"),
        standard_id=F("organisation__standard_key_id"),
    )

    for asset in assets_without_inspection:
        sh = standard_id_to_standard_header.get(asset.standard_id)
        if not sh:
            continue

        val = "SS" if asset.sewer_data else "SW"
        av = AssetValue(
            asset=asset,
            standard_header=sh,
            value=val,
            original_value=val,
        )
        new_asset_values.append(av)

    AssetValue.objects.bulk_create(new_asset_values, batch_size=5_000)


class Migration(migrations.Migration):

    dependencies = [
        ("inspections", "0019_alter_processinglist_status_and_more"),
    ]

    operations = [
        migrations.RunPython(populate_asset_pipe_types, reverse_code=migrations.RunPython.noop),
    ]
