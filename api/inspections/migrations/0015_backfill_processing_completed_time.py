from django.db import migrations, models


def fill_missing_values(apps, schema_editor):
    FileList = apps.get_model("inspections", "FileList")
    Inspection = apps.get_model("inspections", "Inspection")

    file_ids = Inspection.objects.all().values_list("file_id", flat=True)
    FileList.objects.filter(
        processing_started_time__isnull=True, processing_completed_time__isnull=True, id__in=file_ids
    ).update(
        processing_completed_time=models.F("created_time"),
        processing_started_time=models.F("created_time"),
    )


class Migration(migrations.Migration):

    dependencies = [("inspections", "0014_backfill_file_upload_completed_time")]

    operations = [
        migrations.RunPython(
            code=fill_missing_values,
            reverse_code=migrations.RunPython.noop,
        )
    ]
