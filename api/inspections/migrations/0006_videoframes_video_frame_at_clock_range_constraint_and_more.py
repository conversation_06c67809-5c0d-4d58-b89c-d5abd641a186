# Generated by Django 4.1.2 on 2024-05-07 03:12

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("inspections", "0005_coerce_clock_to_range"),
    ]

    operations = [
        migrations.AddConstraint(
            model_name="videoframes",
            constraint=models.CheckConstraint(
                check=models.Q(
                    models.Q(("at_clock__gte", 1), ("at_clock__lte", 12)),
                    ("at_clock__isnull", True),
                    _connector="OR",
                ),
                name="video_frame_at_clock_range_constraint",
            ),
        ),
        migrations.AddConstraint(
            model_name="videoframes",
            constraint=models.CheckConstraint(
                check=models.Q(
                    models.Q(("to_clock__gte", 1), ("to_clock__lte", 12)),
                    ("to_clock__isnull", True),
                    _connector="OR",
                ),
                name="video_frame_to_clock_range_constraint",
            ),
        ),
    ]
