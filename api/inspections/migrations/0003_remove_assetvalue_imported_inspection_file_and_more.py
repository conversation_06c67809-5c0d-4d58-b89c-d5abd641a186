# Generated by Django 4.1.2 on 2024-04-22 02:19

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("inspections", "0002_remove_inspectionvalue_frame_and_more"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="assetvalue",
            name="imported_inspection_file",
        ),
        migrations.RemoveField(
            model_name="inspectionvalue",
            name="imported_inspection_file",
        ),
        migrations.AlterField(
            model_name="inspection",
            name="legacy_id",
            field=models.CharField(max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name="inspectionvalue",
            name="original_value",
            field=models.TextField(max_length=1000, null=True),
        ),
        migrations.AlterField(
            model_name="inspectionvalue",
            name="value",
            field=models.TextField(max_length=1000),
        ),
    ]
