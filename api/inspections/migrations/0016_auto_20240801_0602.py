# Generated by Django 4.1.2 on 2024-08-01 06:02

from django.db import migrations


def create_missing_inspection_filter_records(apps, schema_editor):
    InspectionFilter = apps.get_model("inspections", "InspectionFilter")
    CustomUser = apps.get_model("users", "CustomUser")

    users_without_filter_records = CustomUser.objects.exclude(organisation__isnull=True).exclude(
        id__in=InspectionFilter.objects.values("user_id")
    )

    for user in users_without_filter_records:
        InspectionFilter.objects.get_or_create(user=user, organisation=user.organisation)


class Migration(migrations.Migration):

    dependencies = [
        ("inspections", "0015_backfill_processing_completed_time"),
    ]

    operations = [
        migrations.RunPython(code=create_missing_inspection_filter_records, reverse_code=migrations.RunPython.noop),
    ]
