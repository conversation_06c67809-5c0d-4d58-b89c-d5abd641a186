from django.db import migrations, models


def fill_missing_values(apps, schema_editor):
    FileList = apps.get_model("inspections", "FileList")
    FileList.objects.filter(
        upload_completed=True,
        upload_completed_time__isnull=True,
    ).update(upload_completed_time=models.F("created_time"))


class Migration(migrations.Migration):

    dependencies = [("inspections", "0013_processinglist_status_reason_and_more")]

    operations = [
        migrations.RunPython(
            code=fill_missing_values,
            reverse_code=migrations.RunPython.noop,
        )
    ]
