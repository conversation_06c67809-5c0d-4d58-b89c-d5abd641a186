from django.db import migrations
from django.utils import timezone
from rest_framework.exceptions import NotFound


def populate_folder(apps, schema_editor):
    JobsTree = apps.get_model("inspections", "JobsTree")
    FileList = apps.get_model("inspections", "FileList")

    files_to_update = FileList.objects.filter(job_tree__isnull=True)
    created_folders, seen_orgs = {}, set()

    for f in files_to_update:
        folder = JobsTree.objects.filter(primary_org=f.target_org).filter(job_name__iexact="Uncategorized").first()

        if not folder:
            if f.target_org in seen_orgs:
                folder = created_folders[f.target_org]
            else:
                seen_orgs.add(f.target_org)
                root = JobsTree.objects.filter(primary_org=f.target_org).first()
                if not root:
                    raise NotFound("Cannot create linked folders. Root folder not found for linked asset owner")
                folder = root.add_child(
                    secondary_org=f.upload_org, job_name="Uncategorized", created_date=timezone.now()
                )
                created_folders[f.target_org] = folder
        f.job_tree = folder
        f.save()


def populate_file(apps, schema_editor):
    Inspection = apps.get_model("inspections", "Inspection")
    MapPointList = apps.get_model("inspections", "MapPointList")

    ins_to_update = Inspection.objects.filter(file__isnull=True)
    mpl_to_update = MapPointList.objects.filter(associated_file__isnull=True)

    for i in ins_to_update:
        related_mpl = MapPointList.objects.filter(inspection=i).first()
        if related_mpl and related_mpl.associated_file:
            i.file = related_mpl.associated_file
            i.save()
        else:
            # We assume this is either a planned inspection (no issue)
            # or junk UAT data
            continue

    for m in mpl_to_update:
        if m.inspection and m.inspection.file:
            m.associated_file = i.file
            m.save()
        else:
            # We assume this is either a planned inspection (no issue)
            # or junk UAT data
            continue


def populate_structural_condition_rating(apps, schema_editor):
    Inspection = apps.get_model("inspections", "Inspection")
    MapPointList = apps.get_model("inspections", "MapPointList")

    ins_to_update = Inspection.objects.filter(structural_grade__isnull=True)
    mpl_to_update = MapPointList.objects.filter(condition_rating__isnull=True)

    for i in ins_to_update:
        related_mpl = MapPointList.objects.filter(inspection=i).first()
        if related_mpl and related_mpl.condition_rating:
            i.structural_grade = related_mpl.condition_rating
            i.save()
        else:
            i.structural_grade = Inspection._meta_get_field("structural_grade").get_default()
            i.save()

    for m in mpl_to_update:
        if m.inspection and m.inspection.structural_grade:
            m.condition_rating = m.inspection.structural_grade
            m.save()
        else:
            m.condition_rating = MapPointList._meta_get_field("condition_rating").get_default()
            m.save()


def populate_service_condition_rating(apps, schema_editor):
    Inspection = apps.get_model("inspections", "Inspection")
    MapPointList = apps.get_model("inspections", "MapPointList")

    ins_to_update = Inspection.objects.filter(service_grade__isnull=True)
    mpl_to_update = MapPointList.objects.filter(service_condition_rating__isnull=True)

    for i in ins_to_update:
        related_mpl = MapPointList.objects.filter(inspection=i).first()
        if related_mpl and related_mpl.service_condition_rating:
            i.service_grade = related_mpl.service_condition_rating
            i.save()
        else:
            i.service_grade = Inspection._meta_get_field("service_grade").get_default()
            i.save()

    for m in mpl_to_update:
        if m.inspection and m.inspection.service_grade:
            m.service_condition_rating = m.inspection.service_grade
            m.save()
        else:
            m.service_condition_rating = MapPointList._meta_get_field("service_condition_rating").get_default()
            m.save()


def populate_status(apps, schema_editor):
    Inspection = apps.get_model("inspections", "Inspection")
    MapPointList = apps.get_model("inspections", "MapPointList")

    ins_to_update = Inspection.objects.filter(status__isnull=True)
    mpl_to_update = MapPointList.objects.filter(status__isnull=True)

    for i in ins_to_update:
        related_mpl = MapPointList.objects.filter(inspection=i).first()
        if related_mpl and related_mpl.status:
            i.status = related_mpl.status
            i.save()
        else:
            i.status = Inspection._meta_get_field("status").get_default()
            i.save()

    for m in mpl_to_update:
        if m.inspection and m.inspection.status:
            m.status = m.inspection.status
            m.save()
        else:
            m.status = MapPointList._meta_get_field("status").get_default()
            m.save()


def populate(apps, schema_editor):
    populate_status(apps, schema_editor)
    populate_service_condition_rating(apps, schema_editor)
    populate_structural_condition_rating(apps, schema_editor)
    populate_file(apps, schema_editor)
    populate_folder(apps, schema_editor)


class Migration(migrations.Migration):
    atomic = False

    dependencies = [
        ("inspections", "0010_fix_missing_uploaded_by"),
    ]

    operations = [
        migrations.RunPython(populate, migrations.RunPython.noop),
    ]
