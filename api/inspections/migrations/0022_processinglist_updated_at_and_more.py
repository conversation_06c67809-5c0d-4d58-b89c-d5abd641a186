# Generated by Django 4.1.2 on 2024-10-21 23:38

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "inspections",
            "0021_inspection_last_related_update",
        ),
    ]

    operations = [
        migrations.AddField(
            model_name="processinglist",
            name="updated_at",
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AlterField(
            model_name="processinglist",
            name="status_reason",
            field=models.CharField(
                blank=True,
                choices=[
                    ("GE", "Generic Error"),
                    ("FC", "File Corrupted"),
                    ("TW", "Timeout while waiting"),
                    ("TP", "Timeout while processing"),
                ],
                default=None,
                max_length=2,
                null=True,
            ),
        ),
    ]
