# Generated by Django 4.1.2 on 2024-10-22 03:53

from django.db import migrations, models
import vapar.constants.processing


class Migration(migrations.Migration):

    dependencies = [
        ("inspections", "0018_filelist_updated_at"),
    ]

    operations = [
        migrations.AlterField(
            model_name="processinglist",
            name="status",
            field=models.CharField(
                blank=True,
                choices=[
                    ("Uploading", "UPLOADING"),
                    ("Failed To Process", "FAILED_TO_PROCESS"),
                    ("Analysing Video", "ANALYSING_VIDEO"),
                    ("Storing Results", "STORING_RESULTS"),
                    ("Waiting to process", "WAITING_TO_PROCESS"),
                    ("Upload Failed", "UPLOAD_FAILED"),
                    ("Processing", "PROCESSING"),
                ],
                default=vapar.constants.processing.ProcessingStatusEnum["UPLOADING"],
                max_length=200,
            ),
        ),
        migrations.AlterField(
            model_name="processinglist",
            name="status_reason",
            field=models.Char<PERSON>ield(
                blank=True,
                choices=[
                    ("GE", "GENERIC_ERROR"),
                    ("FC", "FILE_CORRUPTED"),
                    ("TW", "TIMEOUT_WHILE_WAITING"),
                    ("TP", "TIMEOUT_WHILE_PROCESSING"),
                ],
                default=None,
                max_length=2,
                null=True,
            ),
        ),
    ]
