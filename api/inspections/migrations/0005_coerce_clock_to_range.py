from django.db import migrations
from django.db.models import Q


def coerce_out_of_range_clock_to_null(apps, schema_editor):
    VideoFrames = apps.get_model("inspections", "VideoFrames")

    VideoFrames.objects.filter(Q(at_clock__lt=1) | Q(at_clock__gt=12)).update(at_clock=None)
    VideoFrames.objects.filter(Q(to_clock__lt=1) | Q(to_clock__gt=12)).update(to_clock=None)


class Migration(migrations.Migration):

    dependencies = [("inspections", "0004_alter_assetvalue_value_alter_mappointlist_chainage_and_more")]

    operations = [
        migrations.RunPython(
            code=coerce_out_of_range_clock_to_null,
            reverse_code=migrations.RunPython.noop,
        )
    ]
