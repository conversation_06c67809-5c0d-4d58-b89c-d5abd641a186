# Generated by Django 4.1.2 on 2024-07-01 05:23

from django.db import migrations


def clean_up_assets_no_assetvalues(apps, schema_editor):
    """
    Remove assets that have no asset values records linked to them.
    """
    Asset = apps.get_model("inspections", "Asset")
    Asset.objects.exclude(assetvalue__isnull=False).exclude(inspection__isnull=False).delete()


class Migration(migrations.Migration):

    dependencies = [
        ("inspections", "0011_populate_empty_fields"),
    ]

    operations = [
        migrations.RunPython(code=clean_up_assets_no_assetvalues, reverse_code=migrations.RunPython.noop),
    ]
