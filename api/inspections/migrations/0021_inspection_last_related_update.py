# Generated by Django 4.1.2 on 2024-11-05 05:17

from django.db import migrations, models


def set_initial_last_related_update(apps, schema_editor):
    Inspection = apps.get_model("inspections", "Inspection")

    inspections_qs = (
        Inspection.objects.filter(last_related_update=None)
        .annotate(proc_completed_at=models.F("file__processing_completed_time"))
        .only("created_at")
    )
    objs_to_update = []
    for insp in inspections_qs:
        insp.last_related_update = (
            insp.proc_completed_at
            if insp.proc_completed_at and insp.proc_completed_at > insp.created_at
            else insp.created_at
        )
        objs_to_update.append(insp)
    Inspection.objects.bulk_update(objs_to_update, ["last_related_update"], batch_size=5_000)


class Migration(migrations.Migration):

    dependencies = [
        ("inspections", "0020_backfill_missing_asset_pipe_types"),
    ]

    operations = [
        migrations.AddField(
            model_name="inspection",
            name="last_related_update",
            field=models.DateTimeField(null=True),
        ),
        migrations.RunPython(set_initial_last_related_update, migrations.RunPython.noop),
        migrations.AlterField(
            model_name="inspection",
            name="last_related_update",
            field=models.DateTimeField(auto_now=True),
        ),
    ]
