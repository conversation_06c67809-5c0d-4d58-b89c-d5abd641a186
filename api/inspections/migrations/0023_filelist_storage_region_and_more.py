# Generated by Django 4.1.2 on 2024-11-11 00:39

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("inspections", "0022_processinglist_updated_at_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="filelist",
            name="storage_region",
            field=models.CharField(default="AU", max_length=20),
        ),
        migrations.AlterField(
            model_name="processinglist",
            name="status_reason",
            field=models.CharField(
                blank=True,
                choices=[
                    ("GE", "GENERIC_ERROR"),
                    ("FC", "FILE_CORRUPTED"),
                    ("TW", "TIMEOUT_WHILE_WAITING"),
                    ("TP", "TIMEOUT_WHILE_PROCESSING"),
                ],
                default=None,
                max_length=2,
                null=True,
            ),
        ),
    ]
