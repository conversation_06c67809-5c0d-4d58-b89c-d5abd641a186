import logging

from django.core.management import BaseCommand
from django.db import models

from api.inspections.models import MapPointList, InspectionValue
from api.inspections.utilities.sync_mappointlist_values import convert_direction

log = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Sync direction values from inspection to mappointlist for a particular org"

    def add_arguments(self, parser):
        parser.add_argument("org_id", type=int, help="Organisation ID")

    def handle(self, *args, **options):
        org_id = options["org_id"]

        to_update = []
        for mpl in MapPointList.objects.filter(associated_file__target_org=org_id).annotate(
            inspection_direction=models.Subquery(
                InspectionValue.objects.filter(
                    inspection__mappointlist=models.OuterRef("pk"), standard_header__header__name="Direction"
                ).values("value")[:1],
            )
        ):
            target_dir = convert_direction(mpl.inspection_direction)
            if target_dir != mpl.direction:
                log.info(f"Changing direction on {mpl.id} from {mpl.direction} to {target_dir}")
                mpl.direction = target_dir
                to_update.append(mpl)

        if to_update:
            MapPointList.objects.bulk_update(to_update, ["direction"])
            log.info(f"Updated {len(to_update)} MapPointList records")
        else:
            log.info("No records needed updating")
