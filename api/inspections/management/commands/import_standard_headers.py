import pandas as pd
import django
from django.utils import timezone
from django.core.management.base import BaseCommand
from api.inspections.models import StandardHeader
from api.base.models import Header
from api.defects.models import Standard

django.setup()


class Command(BaseCommand):
    help = "Imports the headers for each standard into the database"

    def add_arguments(self, parser):
        parser.add_argument("files", nargs="+", type=str, help="Path to the excel file")

    def handle(self, *args, **options):
        files = options["files"]

        if not len(files) > 0:
            return "You must pass in a standard file to be imported"

        # handle whitespace that wasn't stripped when populating header table
        headers = Header.objects.all()

        for h in headers:
            h.name = h.name.strip()
            h.type = h.type.strip()
            h.mapped_mpl_field = h.mapped_mpl_field.strip()
            h.save()

        for file in options["files"]:
            data = pd.read_excel(f"vapar/inspections/management/commands/{file}")

            data["options_selections"] = data["options_selections"].fillna("")
            data["options_description"] = data["options_description"].fillna("")
            data["description"] = data["description"].fillna("")

            standard_name = file.split("-headers")[0]

            standard = Standard.objects.filter(name=standard_name).first()

            number_of_rows_added = 0
            number_of_rows_updated = 0
            number_of_rows_removed = 0

            # remove headers that are no longer in the standard
            if standard_name in ("WSA-05 2020", "WSA-05 2013"):
                # define headers to remove
                remove_headers_wsa20 = [
                    "Longitudinal location of start of lateral",
                    "Circumferential location of start of lateral",
                    "Original coding standard",
                    "Conduit unit length",
                    "Jointing method",
                    "Ambient temperature",
                    "Tidal influence",
                    "Longitudinal reference point",
                    "Conduit location risk factor observed by operator",
                    "Time of inspection",
                    "Structural grade",
                    "Number of structural defects",
                    "Structural peak score",
                    "Structural mean score",
                    "Service grade",
                    "Number of service defects",
                    "Service peak score",
                    "Service mean score",
                    "Mapping grid datum system",
                    "Mapping grid zone",
                    "Video image location system",
                    "Start node reference",
                    "Finish node reference",
                ]

                # fetch headers to remove
                headers = StandardHeader.objects.filter(standard_id=standard.id, name__in=remove_headers_wsa20)
                number_of_rows_removed = len(headers)
                headers.delete()

            if standard_name == "PACP7":
                remove_headers_pacp = ["Pipe Segment Reference"]
                headers = StandardHeader.objects.filter(standard_id=standard.id, name__in=remove_headers_pacp)
                number_of_rows_removed = len(headers)
                headers.delete()

            for i, row in enumerate(data.itertuples()):
                header = Header.objects.filter(name=row.header).first()

                if header is not None:
                    try:
                        data = {
                            "name": row.name,
                            "header": header,
                            "code": row.code,
                            "required": row.required,
                            "description": row.description,
                            "data_type": row.data_type,
                            "shown_by_default": True,
                            "created_at": timezone.now(),
                            "standard": standard,
                            "options_description": row.options_description,
                            "options_selections": row.options_selections,
                        }

                        existing_header = StandardHeader.objects.filter(header=header, standard=standard).first()

                        if existing_header:
                            for key, value in data.items():
                                setattr(existing_header, key, value)
                            existing_header.save()
                            number_of_rows_updated += 1
                            continue

                        StandardHeader.objects.create(**data)
                        number_of_rows_added += 1
                    except Exception as error:
                        raise Exception(error)

            print(f"successfully created {number_of_rows_added} records for {standard.name}")
            print(f"successfully updated {number_of_rows_updated} records for {standard.name}")
            print(f"successfully removed {number_of_rows_removed} records for {standard.name}")
