from pathlib import Path

import pandas as pd
from django.conf import settings
from django.core.management import BaseCommand, CommandError
from django.db import transaction
from django.db.models import Count
from tqdm import tqdm

from api.base.models import Header
from api.defects.models import StandardHeader, Standard
from api.inspections.models import AssetValue, InspectionValue

ORG_ID = 87  # Northumbrian Water
STANDARD_NAME = "MSCC5"


class Command(BaseCommand):
    help = """
        Assign new Asset IDs and Work Orders to inspections from Excel data. This is intended as a one-off operation
        for Northumbrian Water. [CSRQ-905]
    """

    def add_arguments(self, parser):
        parser.add_argument(
            "file_path", type=Path, help="Path to the Excel file containing the new Asset IDs and Work Orders"
        )

    @transaction.atomic
    def handle(self, *args, **options):
        file_path = options["file_path"]
        if not file_path.is_absolute():
            file_path = settings.BASE_DIR / file_path

        df = pd.read_excel(file_path)

        standard = Standard.objects.get(name=STANDARD_NAME)
        work_order_sh = StandardHeader.objects.get(
            standard=standard,
            header__name="WorkOrder",
            header__type=Header.HeaderType.INSPECTION,
        )

        # Set new Asset ID per inspection
        with tqdm(df.iterrows(), total=len(df), desc="Updating Asset IDs") as progress:
            for _, row in progress:
                # Update asset id
                AssetValue.objects.filter(asset__inspection=row["inspection_id"], asset__organisation=ORG_ID).filter(
                    standard_header__header__name="AssetID", standard_header__header__type=Header.HeaderType.ASSET
                ).update(value=row["New Asset_id"])
                progress.write(f"Inspection '{row['inspection_id']}' -> Asset ID '{row['New Asset_id']}'")

        # Check asset ids are still unique within org by counting unique values
        most_frequent = (
            AssetValue.objects.filter(
                asset__organisation=ORG_ID,
                standard_header__header__name="AssetID",
                standard_header__header__type=Header.HeaderType.ASSET,
            )
            .exclude(value="")
            .values("value")
            .annotate(count=Count("value"))
            .order_by("-count")
        )
        top_most_frequent = list(most_frequent[:10])
        if top_most_frequent and top_most_frequent[0]["count"] > 1:
            self.stdout.write("Found non-unique asset ids:")
            self.stdout.write(str(top_most_frequent))
            raise CommandError("Non-unique asset ids found, aborting and rolling back changes.")

        # Set new Work Order per inspection
        with tqdm(df.iterrows(), total=len(df), desc="Updating inspection Work Orders") as progress:
            for _, row in progress:
                work_order, is_created = InspectionValue.objects.filter(
                    inspection=row["inspection_id"],
                    inspection__asset__organisation=ORG_ID,
                    standard_header__header__name="WorkOrder",
                    standard_header__header__type=Header.HeaderType.INSPECTION,
                ).get_or_create(
                    defaults={
                        "value": row["New Work_Order"],
                        "original_value": row["New Work_Order"],
                        "inspection_id": row["inspection_id"],
                        "standard_header": work_order_sh,
                    }
                )
                if not is_created:
                    work_order.value = row["New Work_Order"]
                    work_order.save()

                progress.write(f"Inspection '{row['inspection_id']}' -> Work Order '{row['New Work_Order']}'")
