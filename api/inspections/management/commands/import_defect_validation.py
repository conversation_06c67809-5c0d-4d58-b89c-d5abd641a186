import django
import pandas as pd
from django.core.management.base import BaseCommand
from api.defects.models import DefectScores

django.setup()


class Command(BaseCommand):
    help = "Imports the values used for defect validation into the database"

    def add_arguments(self, parser):
        parser.add_argument("files", nargs="+", type=str, help="Path to the csv file")

    def handle(self, *args, **options):
        files = options["files"]

        if not len(files) > 0:
            return "You must pass in a standards defect file to be imported"

        for file in files:
            data = pd.read_csv(f"vapar/inspections/management/commands/defect_standards/{file}")

            records_updated = 0
            record_does_not_exist = []

            for i, row in enumerate(data.itertuples()):
                record_id = data.at[i, "id"]
                defect_score_record = DefectScores.objects.filter(id=record_id)
                if defect_score_record.exists():
                    defect_score_record.update(
                        clock_position_required=data.at[i, "clock_position_reqd"],
                        clock_spread_possible=data.at[i, "clock_spread_allowed"],
                        percentage_required=data.at[i, "percentage_required"],
                        at_joint_required=data.at[i, "at_joint_allowed"],
                        start_survey=data.at[i, "is_start_code"],
                        end_survey=data.at[i, "is_end_code"],
                        fastpass_code=data.at[i, "is_fastpass_code"],
                        defect_code="" if pd.isna(data.at[i, "defect_code"]) else data.at[i, "defect_code"],
                    )
                    records_updated += 1
                else:
                    record_does_not_exist.append(record_id)

            print(f"Successfully updated {records_updated} defect records from {file}")
            print(f"Records that do not exist in {file}: {record_does_not_exist}")
