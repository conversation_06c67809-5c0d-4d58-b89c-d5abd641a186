import logging
import uuid
from datetime import datetime, date
from typing import Any
from typing_extensions import TypedDict

from django.core.cache import caches
from django.conf import settings
from django.forms.models import model_to_dict
from pydantic import (
    ConfigDict,
    BaseModel,
    Field,
    SerializationInfo,
    ValidationInfo,
    computed_field,
    field_serializer,
    field_validator,
    model_validator,
)

from api.common.enums import StatusEnum, UnitEnum
from api.inspections.models import Inspection
from api.inspections.pydantic_models.asset_model import LocationInfo, AssetModel

from vapar.constants.pipes import DirectionEnum, PipeTypeEnum, StandardEnum
from vapar.constants.conversion import StandardValueConverter

from api.inspections.serializers.asset_serializers import AssetSerializer
from api.inspections.serializers.inspection_serializers import FileSerializer

log = logging.getLogger(__name__)


INSPECTION_CACHE = caches["inspections"]
ASSET_CACHE = caches["assets"]

# TODO: improve this data source
REGION_MAP = {
    StandardEnum.MSCC5: "UK",
    StandardEnum.NZ_PIPE_MANUAL_3: "NZ",
    StandardEnum.NZ_PIPE_MANUAL_4: "NZ",
    StandardEnum.WSA05_2013: "AU",
    StandardEnum.WSA05_2020: "AU",
    StandardEnum.PACP7: "USA",
}


class ChainageInfo(BaseModel):
    """
    Representation of chainage information

    :param value: the value of the chainage
    :param unit: the unit of the chainage
    """

    model_config = ConfigDict(extra="ignore")

    value: float = 0
    unit: UnitEnum = UnitEnum.METRE

    @property
    def value_with_unit(self):
        return f"{self.value} {self.unit.value}"

    @classmethod
    def from_details(cls, data):
        value = data.get("LengthSurveyed", 0)

        try:
            value = float(value)
        except (TypeError, ValueError):
            value = 0

        unit = UnitEnum.FT if data.get("IsImperial") else UnitEnum.METRE

        return cls(value=value, unit=unit)


class FileInfo(TypedDict):
    """
    Logical representation of a 'file'.
    """

    id: int
    url: str
    hidden: bool
    filename: str
    file_size: str
    file_type: str
    upload_org: int | None
    target_org: int
    upload_user: str
    uploaded_by: int | None
    created_time: datetime | str


class InspectionCreateModel(BaseModel):
    direction: DirectionEnum = Field(..., alias="Direction")
    chainage: float = Field(..., ge=0.0, alias="LengthSurveyed")
    date_captured: date | None = Field(..., alias="Date")
    inspection_notes: str = Field(..., alias="GeneralRemarks")
    work_order: str | None = Field(None, alias="WorkOrder")


class InspectionCreateResponseModel(BaseModel):
    uuid: uuid.UUID


class InspectionModel(BaseModel):
    """
    Representation of an inspection decoupled from the database
    """

    model_config = ConfigDict(extra="allow", populate_by_name=True)

    # inspection fields
    inspection_id: uuid.UUID | None = Field(None, alias="uuid")
    status: StatusEnum = Field(StatusEnum.UPLOADED, alias="status")
    condition_rating: int | None = Field(1, alias="structuralGrade", ge=0, le=5)
    service_condition_rating: int | None = Field(1, alias="serviceGrade", ge=0, le=5)
    created_by: int | None = Field(None, alias="createdBy")
    created_at: datetime | None = Field(None, alias="createdAt")
    last_related_update: datetime | None = Field(None, alias="lastRelatedUpdate")
    id: int | str | None = Field(None, alias="legacyId")

    # Inspection values
    direction: DirectionEnum = Field(DirectionEnum.UNKNOWN, alias="Direction")
    date_captured: datetime | str | None = Field(None, alias="Date")
    chainage: float | None = Field(0.0, alias="LengthSurveyed")
    inspection_notes: str = Field("", alias="GeneralRemarks")
    is_imperial: bool = Field(False, alias="IsImperial")
    standard: StandardEnum | None = Field(None, alias="Standard")
    reviewed_by: str | None = Field(None, alias="ReviewedBy")
    review_timestamp: str | datetime | None = Field(None, alias="Time")
    work_order: str | None = Field(None, alias="WorkOrder")

    # extra objects
    folder: dict | None = Field(None, alias="folder")
    asset: AssetModel | dict | None = Field(None, alias="asset")
    file: FileInfo | None = Field(None, alias="file")
    extra_fields: dict = Field({}, alias="extraFields")
    to_be_matched: bool = Field(False, alias="toBeMatched")

    @model_validator(mode="before")
    @classmethod
    def align_input_field_names(cls, data: Any) -> Any:
        if not isinstance(data, dict):
            return data

        # Service and structural grades are loaded from the inspection object itself - they must be renamed to match
        # the InspectionModel fields.
        if svc_grade := data.pop("service_grade", None):
            data["service_condition_rating"] = svc_grade
        if structural_grade := data.pop("structural_grade", None):
            data["condition_rating"] = structural_grade

        # Avoiding renaming the field itself to 'legacy_id' to avoid breaking compatibility with the FE
        if legacy_id := data.pop("legacy_id", None):
            data["id"] = legacy_id

        return data

    @classmethod
    def get_cache_excluded_fields(cls):
        model_computed = {field: True for field in cls.model_computed_fields}
        asset_excluded = {
            "asset": {
                "asset_id": True,
                "upstream_node": True,
                "downstream_node": True,
                "diameter": True,
                "material": True,
                "location_street": True,
                "location_town": True,
            }
        }
        return model_computed | asset_excluded

    def model_post_init(self, __context: Any):
        """
        This separates extra fields from the defined ones and validated ones
        """
        defined_fields = self.model_fields.keys()
        alias_fields = {v.alias for v in self.model_fields.values() if v.alias}
        data = self.__pydantic_extra__
        self.extra_fields = {k: v for k, v in data.items() if k not in defined_fields and k not in alias_fields}

    @computed_field(return_type=str)
    @property
    def start_node(self):
        start_node = self.downstream_node

        if self.direction == DirectionEnum.DOWNSTREAM:
            start_node = self.upstream_node

        return start_node

    @computed_field(return_type=str)
    @property
    def end_node(self):
        end_node = self.upstream_node

        if self.direction == DirectionEnum.DOWNSTREAM:
            end_node = self.downstream_node

        return end_node

    @computed_field(return_type=LocationInfo)
    @property
    def location(self):
        location = LocationInfo.model_validate(
            {
                "street": self.location_street,
                "town": self.location_town,
                "country": REGION_MAP.get(self.standard),
            }
        )
        return location

    @computed_field(return_type=str)
    @property
    def name(self):
        if self.location is not None:
            return self.location.value
        return ""

    @computed_field(return_type=str)
    @property
    def filename(self):
        if self.file and isinstance(self.file, dict):
            return self.file.get("filename", "")
        return ""

    @computed_field(return_type=DirectionEnum, alias="SetupLocation")
    @property
    def setup_location(self):
        if self.direction is not DirectionEnum.UNKNOWN:
            return DirectionEnum.UPSTREAM if self.direction is DirectionEnum.DOWNSTREAM else DirectionEnum.DOWNSTREAM
        return DirectionEnum.UNKNOWN

    # TODO: this field should be used once MPL migration is complete.
    # the current attribute then becomes 'length'
    # @computed_field(return_type=ChainageInfo)
    # @property
    # def chainage(self):
    #     chainage = ChainageInfo(value=self.length, unit=UnitEnum.FT if self.is_imperial else UnitEnum.METRE)
    #     return chainage

    # @computed_field(return_type=float)
    # @property
    # def chainage_number(self):
    #     return self.chainage.value

    @computed_field(return_type=str)
    @property
    def chainage_unit(self):
        return UnitEnum.FT.value if self.is_imperial else UnitEnum.METRE.value

    # TODO: update frontend so the following computed fields are not required
    # Asset fields:
    @computed_field(return_type=str, alias="AssetID")
    @property
    def asset_id(self):
        return self.asset.asset_id if self.asset else ""

    @computed_field(return_type=str, alias="Material")
    @property
    def material(self):
        return self.asset.material if self.asset else ""

    @computed_field(return_type=int, alias="HeightDiameter")
    @property
    def diameter(self):
        return self.asset.diameter if self.asset else 0

    @computed_field(return_type=str, alias="LocationStreet")
    @property
    def location_street(self):
        return self.asset.location_street if self.asset else ""

    @computed_field(return_type=str, alias="LocationTown")
    @property
    def location_town(self):
        return self.asset.location_town if self.asset else ""

    @computed_field(return_type=str, alias="UpstreamNode")
    @property
    def upstream_node(self):
        return self.asset.upstream_node if self.asset else ""

    @computed_field(return_type=str, alias="DownstreamNode")
    @property
    def downstream_node(self):
        return self.asset.downstream_node if self.asset else ""

    @computed_field(return_type=PipeTypeEnum, alias="UseOfDrainSewer")
    @property
    def pipe_type(self):
        return self.asset.pipe_type if self.asset else PipeTypeEnum.SEWER

    @computed_field(return_type=datetime, alias="YearRenewed")
    @property
    def repair_completed_date(self):
        return self.asset.repair_completed_date if self.asset else None

    # end Asset fields

    @field_serializer("date_captured")
    @classmethod
    def serialize_date_captured(self, value: datetime | str) -> str:
        if isinstance(value, datetime):
            return value.strftime("%Y-%m-%d")
        return value

    @field_serializer("direction")
    @classmethod
    def serialize_direction(self, value: DirectionEnum, info: SerializationInfo) -> str:
        if context := info.context:
            standard = context.get("standard")
            converter = StandardValueConverter(header="Direction", standard=standard)
            value = converter.get_standard_value(value)
            return value.value
        return value.value

    @field_serializer("asset_id", "upstream_node", "downstream_node", "material")
    @classmethod
    def serialize_str_values(self, value) -> str:
        if isinstance(value, str):
            return value.strip()
        if value is None:
            return ""
        return value

    @field_validator("direction", mode="before")
    @classmethod
    def validate_direction(cls, val: str, info: ValidationInfo) -> DirectionEnum:
        try:
            direction = DirectionEnum(val)
        except ValueError:
            # try standard value conversion if standard context is provided
            if (context := info.context) and val:
                standard = context.get("standard")
                converter = StandardValueConverter(header="Direction", standard=standard)
                direction = converter.get_common_value(val)
                if not isinstance(direction, DirectionEnum):
                    direction = DirectionEnum.UNKNOWN
            else:
                # set default value
                direction = DirectionEnum.UNKNOWN

        return direction

    @field_validator(
        "created_at",
        "date_captured",
        "review_timestamp",
        mode="before",
    )
    def coerce_empty_str_to_none(cls, val: str):
        if val == "":
            return None
        return val

    @field_validator("chainage", mode="before")
    @classmethod
    def coerce_str_to_zero(cls, val: str) -> float:
        try:
            val = float(val)
        except (TypeError, ValueError):
            return 0.0
        return val

    @field_validator("inspection_notes", mode="before")
    def coerce_none_to_empty_str(cls, val: str | None) -> str:
        if val is None:
            return ""
        return val

    @field_validator("is_imperial", mode="before")
    @classmethod
    def coerce_null_or_str_to_bool(cls, val: bool | str | None) -> bool | str:
        if val is None:
            return False
        if isinstance(val, str):
            if val.lower() == "true":
                return True
            if val.lower() == "false" or not val:
                return False
        return val

    @field_validator("condition_rating", "service_condition_rating", mode="before")
    @classmethod
    def validate_grades(cls, val: int | None):
        if val is None:
            return 1
        return val

    @field_validator("standard", mode="before")
    @classmethod
    def validate_standard(cls, val: StandardEnum | None | str) -> StandardEnum | None:
        if val == "":
            return None
        return val

    def model_dump(self, **kwargs):
        inspection = super().model_dump(**kwargs)
        if self.asset and isinstance(self.asset, AssetModel):
            kwargs = {"by_alias": True, **kwargs}
            asset = self.asset.model_dump(**kwargs)
            inspection["asset"] = asset
        return inspection


def get_inspection_details(inspection: Inspection) -> dict:
    inspection_details = {}

    inspection_values = inspection.inspectionvalue_set.all()

    for inspection_value in inspection_values:
        inspection_details[inspection_value.standard_header.header.name] = inspection_value.value

    # handles SetupLocation conversion to Direction for NZ standards
    # TODO: relocate this. perhaps an override of the Inspection.model_validate method?
    if "SetupLocation" in inspection_details:
        standard = inspection_details.get("Standard")
        if standard is None:
            standard = inspection_values[0].standard_header.standard.display_name
        converter = StandardValueConverter(header="Direction", standard=standard)
        setup_location_value = inspection_details["SetupLocation"]
        opposite_of_direction = converter.get_common_value(setup_location_value)
        inspection_details["Direction"] = opposite_of_direction.value
        if opposite_of_direction is not DirectionEnum.UNKNOWN:
            inspection_details["Direction"] = (
                DirectionEnum.UPSTREAM.value
                if opposite_of_direction is DirectionEnum.DOWNSTREAM
                else DirectionEnum.DOWNSTREAM.value
            )

    return inspection_details


def get_inspection_representation(
    inspection: Inspection,
    asset: AssetModel | None = None,
    skip_cache: bool = False,
) -> InspectionModel:
    """
    Build an inspection representation from an inspection record

    :param inspection: the Inspection data record to be converted to an InspectionModel
    :param asset: the AssetModel linked to the Inspection
    :param skip_cache: Whether or not to save the generated InspectionModel to cache.
    """

    inspection_details = get_inspection_details(inspection=inspection)

    file = FileSerializer(inspection.file).data if inspection.file else None
    if file:
        file["upload_org_type"] = inspection.file.upload_org.org_type

    if asset is None:
        asset = AssetSerializer().to_representation(instance=inspection.asset)

    mpl = getattr(inspection, "mappointlist", None)

    data = {
        **model_to_dict(inspection),
        **inspection_details,
        "uuid": str(inspection),
        "file": file,
        "folder": model_to_dict(inspection.folder) if inspection.folder else None,
        "asset": asset,
        "created_at": inspection.created_at,
        "last_related_update": inspection.last_related_update,
        "to_be_matched": False,  # feature disabled (currently)
        "reviewed_by": mpl.reviewed_by if mpl else None,
    }

    context = {}
    if inspection.folder or inspection.file:
        folder = inspection.file.job_tree if inspection.file else inspection.folder
        if data["folder"] is not None:
            data["folder"]["path"] = folder.job_name
        standard = folder.standard_key.display_name
        data["standard"] = standard
        context["standard"] = standard

    inspection = InspectionModel.model_validate(data, context=context)
    if not skip_cache:
        INSPECTION_CACHE.set(
            inspection.inspection_id,
            inspection.model_dump_json(
                # Note: We can't exclude none because FileInfo is a typed dict, so can't populate defaults
                exclude_defaults=True,
                exclude_unset=True,
                exclude=InspectionModel.get_cache_excluded_fields(),
            ),
            timeout=settings.DEFAULT_ENTITY_CACHE_TIMEOUT,
        )

    return inspection


def restore_cached_inspections(cached_inspections: dict[str, str | bytes]) -> dict[str, InspectionModel]:
    """
    Restore Inspection data from cached JSON objects.

    :param cached_inspections: The dictionary of cached JSON objects, by uuid.
    :return: A dictionary of Inspection models, with inspection ids as keys.
    """
    return {k: InspectionModel.model_validate_json(i) for k, i in cached_inspections.items()}
