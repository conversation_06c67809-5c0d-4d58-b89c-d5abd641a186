from enum import Enum

from django.db.models import Q, <PERSON>Ref, Exists, Subquery, Value
from django.db.models.functions import Coalesce, Cast
from pydantic import BaseModel

from api.common.enums import OperatorEnum, QueryFilterDataTypeEnum
from api.inspections.models import AssetValue, Asset


class AssetFilterEntityEnum(str, Enum):
    """
    The entity that contains the field for a filter
    """

    ASSET_VALUE = "asset"
    ASSET = "asset_entity"  # On the asset object itself, rather than as a value


class AssetSearchFilterModel(BaseModel):
    """
    Represents a filter criteria to be applied to an organisation's assets
    """

    name: str
    value: list[str] = []
    filter_type: AssetFilterEntityEnum = AssetFilterEntityEnum.ASSET_VALUE
    operator: OperatorEnum = OperatorEnum.CT
    data_type: QueryFilterDataTypeEnum = QueryFilterDataTypeEnum.STRING

    def to_query(self) -> Q:
        """
        Convert the search filter to a django query filter node for an Asset queryset
        """

        match self.filter_type:
            case AssetFilterEntityEnum.ASSET_VALUE:
                value_subquery = build_asset_value_subquery(self.value, self.name, self.data_type, self.operator)
            case _:  # ASSET
                value_subquery = build_asset_subquery(self.value, self.name, self.data_type, self.operator)

        return Q(Exists(value_subquery))


def build_asset_value_subquery(
    values: list[str], header_name: str, data_type: QueryFilterDataTypeEnum, operator: OperatorEnum
) -> Q:
    """
    Build a subquery to filter assets by a specific asset value
    """

    lookup_suffix = operator.as_lookup_suffix()
    val_qs = AssetValue.objects.annotate(typed_val=data_type.build_type_cast_expression("value"))
    val_qs = val_qs.filter(
        asset=OuterRef("pk"),
        standard_header__header__name=header_name,
        **{f"typed_val{lookup_suffix}": data_type.get_converted_value_list(values, operator)},
    )
    return val_qs


def build_asset_subquery(
    values: list[str], header_name: str, data_type: QueryFilterDataTypeEnum, operator: OperatorEnum
) -> Q:
    """
    Build a subquery to filter assets by a field on the asset object
    """

    lookup_suffix = operator.as_lookup_suffix()
    val_qs = Asset.objects.annotate(typed_val=data_type.build_type_cast_expression(header_name))
    val_qs = val_qs.filter(
        pk=OuterRef("pk"),
        **{f"typed_val{lookup_suffix}": data_type.get_converted_value_list(values, operator)},
    )
    return val_qs


def build_asset_order_subquery(
    field_name: str,
    data_type: QueryFilterDataTypeEnum,
    fallback_value,
):
    order_qs = Asset.objects.annotate(
        typed_val=data_type.build_type_cast_expression(field_name),
    )
    order_qs = order_qs.filter(pk=OuterRef("pk"))
    order_qs = order_qs.values("typed_val")
    order_expr = Coalesce(
        Cast(Subquery(order_qs), output_field=data_type.as_db_field_type()),
        Value(fallback_value, output_field=data_type.as_db_field_type()),
    )
    return order_expr


def build_asset_value_order_subquery(header_name: str, data_type: QueryFilterDataTypeEnum, fallback_value):
    order_qs = AssetValue.objects.annotate(
        typed_val=data_type.build_type_cast_expression("value"),
    )
    order_qs = order_qs.filter(
        asset=OuterRef("pk"),
        standard_header__header__name=header_name,
    )
    order_qs = order_qs.values("typed_val")
    order_expr = Coalesce(
        Subquery(order_qs),
        Value(fallback_value, output_field=data_type.as_db_field_type()),
    )

    return order_expr
