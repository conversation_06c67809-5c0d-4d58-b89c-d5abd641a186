from enum import Enum

from django.db.models import Q, <PERSON><PERSON><PERSON>, Exists, QuerySet
from pydantic import BaseModel, Field, ConfigDict
from vapar.constants.conversion import DIRECTION_MAP

from api.common.enums import OperatorEnum, QueryFilterDataTypeEnum
from api.inspections.models import InspectionValue, AssetValue, Inspection, FileList


class InspectionFilterEntityEnum(str, Enum):
    """
    The entity that contains the field for a filter
    """

    INSPECTION_VALUE = "inspection"
    ASSET_VALUE = "asset"
    FILE = "file"
    INSPECTION = "inspection_entity"  # On the inspection object itself, rather than as a value


class InspectionSearchFilterModel(BaseModel):
    """
    Represents a filter criteria to be applied to an organisation's inspections
    """

    name: str
    value: list[str] = []
    filter_type: InspectionFilterEntityEnum = Field(InspectionFilterEntityEnum.INSPECTION_VALUE, alias="type")
    options: list[str] | None = None
    operator: OperatorEnum = OperatorEnum.CT
    data_type: QueryFilterDataTypeEnum = QueryFilterDataTypeEnum.STRING

    model_config = ConfigDict(populate_by_name=True, extra="ignore")

    @classmethod
    def from_header_filter(cls, header_filter: dict) -> "InspectionSearchFilterModel":
        props = {**header_filter, "operator": OperatorEnum[header_filter["operator"]]}

        # Special case for these names as the frontend writes them as headers even though they aren't...
        if header_filter["name"] == "StructuralGrade":
            props["type"] = InspectionFilterEntityEnum.INSPECTION
            props["name"] = "structural_grade"
        elif header_filter["name"] == "ServiceGrade":
            props["type"] = InspectionFilterEntityEnum.INSPECTION
            props["name"] = "service_grade"
        return cls.model_validate(props)

    def _is_direction_equality_search(self) -> bool:
        return (
            self.name == "Direction"
            and self.filter_type == InspectionFilterEntityEnum.INSPECTION_VALUE
            and self.operator in (OperatorEnum.IN, OperatorEnum.EQ)
        )

    def _expand_standard_specific_search(self) -> "InspectionSearchFilterModel":
        expanded = self.model_copy()

        if self._is_direction_equality_search():
            # We use the exact query entered, but also add the standard specific values
            expanded.operator = OperatorEnum.IN
            new_op_set = set(self.value)
            for search_value in self.value:
                for standard_map in DIRECTION_MAP.values():
                    if search_value in standard_map:
                        new_op_set.add(standard_map[search_value])

            expanded.value = list(new_op_set)

        return expanded

    def to_query(self) -> Q:
        """
        Convert the search filter to a django query filter node for an Inspection queryset
        """

        expanded = self._expand_standard_specific_search()
        return expanded._to_query()

    def _to_query(self) -> Q:

        match self.filter_type:
            case InspectionFilterEntityEnum.INSPECTION_VALUE:
                value_subquery = build_inspection_value_subquery(self.value, self.name, self.data_type, self.operator)
            case InspectionFilterEntityEnum.ASSET_VALUE:
                value_subquery = build_asset_value_subquery(self.value, self.name, self.data_type, self.operator)
            case InspectionFilterEntityEnum.INSPECTION:
                value_subquery = build_inspection_subquery(self.value, self.name, self.data_type, self.operator)
            case _:  # FILE
                value_subquery = build_file_subquery(self.value, self.name, self.data_type, self.operator)

        return Q(Exists(value_subquery))


def build_inspection_value_subquery(
    values: list[str],
    header_name: str,
    data_type: QueryFilterDataTypeEnum,
    operator: OperatorEnum,
) -> QuerySet[InspectionValue]:
    val_qs = InspectionValue.objects.annotate(
        typed_val=data_type.build_type_cast_expression("value"),
    )
    lookup_suffix = operator.as_lookup_suffix()
    val_qs = val_qs.filter(
        inspection=OuterRef("pk"),
        standard_header__header__name=header_name,
        **{f"typed_val{lookup_suffix}": data_type.get_converted_value_list(values, operator)},
    )
    return val_qs


def build_asset_value_subquery(
    values: list[str],
    header_name: str,
    data_type: QueryFilterDataTypeEnum,
    operator: OperatorEnum,
) -> QuerySet[AssetValue]:
    val_qs = AssetValue.objects.annotate(
        typed_val=data_type.build_type_cast_expression("value"),
    )
    lookup_suffix = operator.as_lookup_suffix()
    val_qs = val_qs.filter(
        asset__inspection=OuterRef("pk"),
        standard_header__header__name=header_name,
        **{f"typed_val{lookup_suffix}": data_type.get_converted_value_list(values, operator)},
    )
    return val_qs


def build_inspection_subquery(
    values: list[str],
    field_name: str,
    data_type: QueryFilterDataTypeEnum,
    operator: OperatorEnum,
) -> QuerySet[Inspection]:
    val_qs = Inspection.objects.annotate(
        typed_val=data_type.build_type_cast_expression(field_name),
    )
    lookup_suffix = operator.as_lookup_suffix()
    val_qs = val_qs.filter(
        pk=OuterRef("pk"),
        **{f"typed_val{lookup_suffix}": data_type.get_converted_value_list(values, operator)},
    )
    return val_qs


def build_file_subquery(
    values: list[str],
    field_name: str,
    data_type: QueryFilterDataTypeEnum,
    operator: OperatorEnum,
) -> QuerySet[FileList]:
    val_qs = FileList.objects.annotate(
        typed_val=data_type.build_type_cast_expression(field_name),
    )
    lookup_suffix = operator.as_lookup_suffix()
    val_qs = val_qs.filter(
        inspection=OuterRef("pk"),
        **{f"typed_val{lookup_suffix}": data_type.get_converted_value_list(values, operator)},
    )
    return val_qs


def build_asset_value_order_subquery(
    header_name: str,
    data_type: QueryFilterDataTypeEnum,
) -> QuerySet[AssetValue]:
    order_qs = AssetValue.objects.annotate(
        typed_val=data_type.build_type_cast_expression("value"),
    )
    order_qs = order_qs.filter(
        asset__inspection=OuterRef("pk"),
        standard_header__header__name=header_name,
    )
    order_qs = order_qs.values("typed_val")
    return order_qs


def build_inspection_value_order_subquery(
    header_name: str,
    data_type: QueryFilterDataTypeEnum,
) -> QuerySet[InspectionValue]:
    order_qs = InspectionValue.objects.annotate(
        typed_val=data_type.build_type_cast_expression("value"),
    )
    order_qs = order_qs.filter(
        inspection=OuterRef("pk"),
        standard_header__header__name=header_name,
    )
    order_qs = order_qs.values("typed_val")
    return order_qs
