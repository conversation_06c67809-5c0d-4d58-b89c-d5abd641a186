import pytest
from rest_framework import status
from rest_framework.test import APIRequestFactory

from api.common.enums import RepairPlanItemTypeEnum
from api.recommendations.models import RepairRecommendation, Custom_Repair_Types
from api.recommendations.serializers import RepairPlanSerializer, RepairItemSerializer


pytestmark = [pytest.mark.django_db]


@pytest.fixture
def custom_repair_types(asset_owner_org):
    org = asset_owner_org
    t1 = Custom_Repair_Types.objects.create(
        organisations=org,
        type="boolean",
        name="Custom Boolean Type",
    )
    t2 = Custom_Repair_Types.objects.create(
        organisations=org,
        type="text",
        name="Custom Text Type",
    )
    t3 = Custom_Repair_Types.objects.create(
        organisations=org,
        type="number",
        name="Custom Number Type",
    )
    return [t1, t2, t3]


class TestSerializedRepresentation:
    """
    Basic tests that a repair recommendation can be serialized into a repair plan representation.
    """

    def test_no_items(self, asset_owner_org, standard_user, single_inspection):
        rr = RepairRecommendation.objects.create(
            target=single_inspection.mappointlist,
            inspection_frequency="Monthly",
            action_summary="A",
            c_action_summary="B",
            o_action_summary="C",
        )

        serializer = RepairPlanSerializer(instance=rr)
        data = serializer.data
        assert data["inspection_id"] == str(single_inspection.uuid)
        assert data["frequency"] == "Monthly"

        assert data["vapar_plan"] == {
            "actor": "vapar",
            "items": [],
            "general_notes": "",
            "action_summary": "A",
        }

        assert data["contractor_plan"] == {
            "actor": "contractor",
            "items": [],
            "general_notes": "",
            "action_summary": "B",
        }

        assert data["owner_plan"] == {
            "actor": "owner",
            "items": [],
            "general_notes": "",
            "action_summary": "C",
        }

    def test_all_repair_types_vapar_actor(self, asset_owner_org, standard_user, single_inspection):
        rr = RepairRecommendation.objects.create(
            target=single_inspection.mappointlist,
            dig_up=True,
            dig_up_details="Some details...",
            patching_details=True,
            patches_counted=3,
            cleaning_required=True,
            full_relining=True,
            root_treatment=True,
        )

        serializer = RepairPlanSerializer(instance=rr)
        data = serializer.data
        items = data["vapar_plan"]["items"]

        assert len(items) == 5
        items_by_type = {item["repair_type"]: item for item in items}

        assert items_by_type["Dig Up"]["actor"] == "vapar"
        assert items_by_type["Dig Up"]["repair_type"] == "Dig Up"
        assert items_by_type["Dig Up"]["metadata"] == [{"key": "Details", "value": "Some details..."}]
        assert items_by_type["Dig Up"]["custom_type_id"] is None
        assert items_by_type["Dig Up"]["custom_data_type"] is None

        assert items_by_type["Patch"]["actor"] == "vapar"
        assert items_by_type["Patch"]["repair_type"] == "Patch"
        assert items_by_type["Patch"]["metadata"] == [{"key": "Count", "value": 3}]
        assert items_by_type["Patch"]["custom_type_id"] is None
        assert items_by_type["Patch"]["custom_data_type"] is None

        assert items_by_type["Cleaning"]["actor"] == "vapar"
        assert items_by_type["Cleaning"]["repair_type"] == "Cleaning"
        assert items_by_type["Cleaning"]["metadata"] == []
        assert items_by_type["Cleaning"]["custom_type_id"] is None
        assert items_by_type["Cleaning"]["custom_data_type"] is None

        assert items_by_type["Lining"]["actor"] == "vapar"
        assert items_by_type["Lining"]["repair_type"] == "Lining"
        assert items_by_type["Lining"]["metadata"] == []
        assert items_by_type["Lining"]["custom_type_id"] is None
        assert items_by_type["Lining"]["custom_data_type"] is None

    def test_all_parties(self, asset_owner_org, standard_user, single_inspection):
        rr = RepairRecommendation.objects.create(
            target=single_inspection.mappointlist,
            dig_up=True,
            dig_up_details="Vapar details...",
            c_dig_up=True,
            c_dig_up_details="Contractor details...",
            o_dig_up=True,
            o_dig_up_details="Owner details...",
        )

        serializer = RepairPlanSerializer(instance=rr)
        data = serializer.data
        assert len(data["vapar_plan"]["items"]) == 1
        assert len(data["contractor_plan"]["items"]) == 1
        assert len(data["owner_plan"]["items"]) == 1

        vapar_item = data["vapar_plan"]["items"][0]
        contractor_item = data["contractor_plan"]["items"][0]
        owner_item = data["owner_plan"]["items"][0]

        assert vapar_item["repair_type"] == "Dig Up"
        assert vapar_item["metadata"] == [{"key": "Details", "value": "Vapar details..."}]

        assert contractor_item["repair_type"] == "Dig Up"
        assert contractor_item["metadata"] == [{"key": "Details", "value": "Contractor details..."}]

        assert owner_item["repair_type"] == "Dig Up"
        assert owner_item["metadata"] == [{"key": "Details", "value": "Owner details..."}]

    def test_custom_values(self, asset_owner_org, standard_user, single_inspection, custom_repair_types):
        rr = RepairRecommendation.objects.create(
            target=single_inspection.mappointlist,
            dig_up=True,
            dig_up_details="Vapar details...",
        )
        number_type = Custom_Repair_Types.objects.get(name="Custom Number Type", organisations=asset_owner_org)
        text_type = Custom_Repair_Types.objects.get(name="Custom Text Type", organisations=asset_owner_org)

        number_type.custom_repair_values_set.create(
            point=rr.target,
            c_value_number=15,
        )
        text_type.custom_repair_values_set.create(
            point=rr.target,
            c_value_text="Some text...",
            o_value_text="Some other text...",
        )

        serializer = RepairPlanSerializer(instance=rr)
        data = serializer.data
        assert len(data["vapar_plan"]["items"]) == 1
        vapar_item = data["vapar_plan"]["items"][0]

        assert vapar_item["repair_type"] == "Dig Up"
        assert vapar_item["metadata"] == [{"key": "Details", "value": "Vapar details..."}]

        contractor_items = data["contractor_plan"]["items"]
        assert len(contractor_items) == 2
        assert contractor_items[0]["repair_type"] == "Custom"
        assert contractor_items[1]["repair_type"] == "Custom"

        number_val = next(v for v in contractor_items if v["custom_type_id"] == number_type.id)
        text_val = next(v for v in contractor_items if v["custom_type_id"] == text_type.id)

        assert number_val["metadata"] == [{"key": "CustomValue", "value": "15"}]
        assert number_val["custom_data_type"] == "number"
        assert text_val["metadata"] == [{"key": "CustomValue", "value": "Some text..."}]
        assert text_val["custom_data_type"] == "text"

        owner_items = data["owner_plan"]["items"]
        assert len(owner_items) == 1
        assert owner_items[0]["repair_type"] == "Custom"
        assert owner_items[0]["metadata"] == [{"key": "CustomValue", "value": "Some other text..."}]
        assert owner_items[0]["custom_data_type"] == "text"


class TestSerializerCreate:
    """
    Test that the RepairPlanSerializer can correctly create a RepairRecommendation instance.
    """

    def test_blank(self, asset_owner_org, standard_user, single_inspection, client):
        request = APIRequestFactory().post("inspections/repair-plan/the-inspection-id")
        request.user = standard_user

        serializer = RepairPlanSerializer(
            data={},
            context={
                "inspection_id": single_inspection.uuid,
                "request": request,
            },
        )
        serializer.is_valid(raise_exception=True)
        rr = serializer.save()

        assert rr.target == single_inspection.mappointlist

        assert rr.dig_up is False
        assert rr.cleaning_required is False
        # etc..

    def test_with_payload(self, asset_owner_org, standard_user, single_inspection, client):
        request = APIRequestFactory().post("inspections/repair-plan/the-inspection-id")
        request.user = standard_user

        serializer = RepairPlanSerializer(
            data={
                "likelihood": "Unlikely",
                "frequency": "Monthly",
                "consequence": "Severe",
            },
            context={
                "inspection_id": single_inspection.uuid,
                "request": request,
            },
        )
        serializer.is_valid(raise_exception=True)
        rr = serializer.save()

        assert rr.risk_likelihood.name == "Unlikely"
        assert rr.risk_consequence.name == "Severe"
        assert rr.inspection_frequency == "Monthly"


class TestRepairItemSerializerCreate:
    def test_create_dig_up(self, repair_recommendation, single_inspection):
        data = {
            "repair_type": "Dig Up",
            "actor": "vapar",
            "metadata": [{"key": "Details", "value": "Some details..."}],
        }
        serializer = RepairItemSerializer(data=data, context={"inspection_id": single_inspection.uuid})
        serializer.is_valid(raise_exception=True)
        serializer.save()

        repair_recommendation.refresh_from_db()
        assert repair_recommendation.dig_up is True
        assert repair_recommendation.dig_up_details == "Some details..."
        assert repair_recommendation.c_dig_up is False, "Should be unaffected"

    def test_create_contractor_dig_up(self, repair_recommendation, single_inspection):
        data = {
            "repair_type": "Dig Up",
            "actor": "contractor",
            "metadata": [{"key": "Details", "value": "Some details..."}],
        }
        serializer = RepairItemSerializer(data=data, context={"inspection_id": single_inspection.uuid})
        serializer.is_valid(raise_exception=True)
        serializer.save()

        repair_recommendation.refresh_from_db()
        assert repair_recommendation.c_dig_up is True
        assert repair_recommendation.c_dig_up_details == "Some details..."
        assert repair_recommendation.dig_up is False, "Should be unaffected"

    def test_create_owner_dig_up(self, repair_recommendation, single_inspection):
        data = {
            "repair_type": "Dig Up",
            "actor": "owner",
            "metadata": [{"key": "Details", "value": "Some details..."}],
        }
        serializer = RepairItemSerializer(data=data, context={"inspection_id": single_inspection.uuid})
        serializer.is_valid(raise_exception=True)
        serializer.save()

        repair_recommendation.refresh_from_db()
        assert repair_recommendation.o_dig_up is True
        assert repair_recommendation.o_dig_up_details == "Some details..."
        assert repair_recommendation.dig_up is False, "Should be unaffected"
        assert repair_recommendation.c_dig_up is False, "Should be unaffected"

    def test_create_patch(self, repair_recommendation, single_inspection):
        data = {
            "repair_type": "Patch",
            "actor": "vapar",
            "metadata": [{"key": "Count", "value": "3"}],
        }
        serializer = RepairItemSerializer(data=data, context={"inspection_id": single_inspection.uuid})
        serializer.is_valid(raise_exception=True)
        serializer.save()

        repair_recommendation.refresh_from_db()
        assert repair_recommendation.patching_details is True
        assert repair_recommendation.patches_counted == 3

    def test_create_custom_text_type(
        self, asset_owner_org, repair_recommendation, custom_repair_types, single_inspection
    ):
        org = asset_owner_org
        text_type = Custom_Repair_Types.objects.get(name="Custom Text Type", organisations=org)
        data = {
            "repair_type": "Custom",
            "actor": "contractor",
            "custom_type_id": text_type.id,
            "metadata": [{"key": "CustomValue", "value": "The custom text value"}],
        }
        serializer = RepairItemSerializer(data=data, context={"inspection_id": single_inspection.uuid})
        serializer.is_valid(raise_exception=True)
        serializer.save()

        val = text_type.custom_repair_values_set.first()
        assert val.c_value_text == "The custom text value"
        assert val.point.repairrecommendation == repair_recommendation

    def test_create_custom_number_type(
        self, asset_owner_org, repair_recommendation, custom_repair_types, single_inspection
    ):
        org = asset_owner_org
        number_type = Custom_Repair_Types.objects.get(name="Custom Number Type", organisations=org)
        data = {
            "repair_type": "Custom",
            "actor": "contractor",
            "custom_type_id": number_type.id,
            "metadata": [{"key": "CustomValue", "value": "15"}],
        }
        serializer = RepairItemSerializer(data=data, context={"inspection_id": single_inspection.uuid})
        serializer.is_valid(raise_exception=True)
        serializer.save()

        val = number_type.custom_repair_values_set.first()
        assert val.c_value_number == 15
        assert val.point.repairrecommendation == repair_recommendation

    def test_create_custom_boolean_type(
        self, asset_owner_org, repair_recommendation, custom_repair_types, single_inspection
    ):
        org = asset_owner_org
        boolean_type = Custom_Repair_Types.objects.get(name="Custom Boolean Type", organisations=org)
        data = {
            "repair_type": "Custom",
            "actor": "contractor",
            "custom_type_id": boolean_type.id,
            "metadata": [{"key": "CustomValue", "value": "true"}],
        }
        serializer = RepairItemSerializer(data=data, context={"inspection_id": single_inspection.uuid})
        serializer.is_valid(raise_exception=True)
        serializer.save()

        val = repair_recommendation.target.custom_repair_values_set.first()
        assert val.c_value_bool is True
        assert val.point.repairrecommendation == repair_recommendation

    def test_create_custom_when_already_exists(
        self, asset_owner_org, repair_recommendation, custom_repair_types, single_inspection
    ):
        org = asset_owner_org
        boolean_type = Custom_Repair_Types.objects.get(name="Custom Boolean Type", organisations=org)

        boolean_type.custom_repair_values_set.create(
            point=repair_recommendation.target,
            o_value_bool=False,
        )  # Create a pre-existing value value

        data = {
            "repair_type": "Custom",
            "actor": "contractor",
            "custom_type_id": boolean_type.id,
            "custom_data_type": "boolean",
            "metadata": [{"key": "CustomValue", "value": "true"}],
        }
        serializer = RepairItemSerializer(data=data, context={"inspection_id": single_inspection.uuid})
        serializer.is_valid(raise_exception=True)
        serializer.save()

        assert repair_recommendation.target.custom_repair_values_set.count() == 1, "Should not create a new value"
        val = repair_recommendation.target.custom_repair_values_set.first()
        assert val.c_value_bool is True
        assert val.o_value_bool is False, "Should not be updated"
        assert val.point.repairrecommendation == repair_recommendation

    def test_invalid_custom_value(self, asset_owner_org, repair_recommendation, custom_repair_types, single_inspection):
        org = asset_owner_org
        boolean_type = Custom_Repair_Types.objects.get(name="Custom Boolean Type", organisations=org)
        data = {
            "repair_type": "Custom",
            "actor": "contractor",
            "custom_type_id": boolean_type.id,
            "metadata": [{"key": "CustomValue", "value": "not a bool"}],
        }
        serializer = RepairItemSerializer(data=data, context={"inspection_id": single_inspection.uuid})
        assert serializer.is_valid() is False

    def test_remove_custom_value_with_null(
        self, asset_owner_org, repair_recommendation, custom_repair_types, single_inspection
    ):
        """Test that sending null value for CustomValue removes the junction count from CustomRepairType"""
        org = asset_owner_org
        number_type = Custom_Repair_Types.objects.get(name="Custom Number Type", organisations=org)

        # First, create a custom repair value
        data = {
            "repair_type": "Custom",
            "actor": "contractor",
            "custom_type_id": number_type.id,
            "metadata": [{"key": "CustomValue", "value": "15"}],
        }
        serializer = RepairItemSerializer(data=data, context={"inspection_id": single_inspection.uuid})
        serializer.is_valid(raise_exception=True)
        serializer.save()

        # Verify the value was set
        val = number_type.custom_repair_values_set.first()
        assert val.c_value_number == 15

        # Now send null to remove the value
        data_null = {
            "repair_type": "Custom",
            "actor": "contractor",
            "custom_type_id": number_type.id,
            "metadata": [{"key": "CustomValue", "value": None}],
        }
        serializer_null = RepairItemSerializer(data=data_null, context={"inspection_id": single_inspection.uuid})
        serializer_null.is_valid(raise_exception=True)
        serializer_null.save()

        # Verify the value was removed (set to None)
        val.refresh_from_db()
        assert val.c_value_number is None


class TestRepairPlanView:
    """
    Basic tests that ensure function of the repair plan view (retrieve, create, update)
    """

    def test_get_repairplan(
        self, client, single_inspection, standard_user, repair_recommendation, repairrecommendation_settings
    ):
        """
        Test that a standard user with inspection access can get a repairplan
        """
        client.force_login(user=standard_user)

        response = client.get(
            path=repairrecommendation_settings.get_repairplan_url(inspection_id=single_inspection.uuid)
        )

        assert response.status_code == status.HTTP_200_OK

        response_data = response.json()
        assert response_data["inspectionId"] == str(repair_recommendation.target.inspection.uuid)

    def test_create_repairplan(self, client, single_inspection, standard_user, repairrecommendation_settings):
        """
        Test that a standard user with inspection access can create a repairplan
        """
        client.force_login(user=standard_user)

        # First, make sure no repairplans already exist
        RepairRecommendation.objects.filter(target=single_inspection.mappointlist).delete()

        response = client.post(
            path=repairrecommendation_settings.get_repairplan_url(inspection_id=single_inspection.uuid),
            data={
                "consequence": "Not Significant",
                "likelihood": "Rare",
                "consequenceComment": "Test comment 1",
                "likelihoodComment": "Test comment 2",
            },
            content_type="application/json",
        )
        assert response.status_code == status.HTTP_201_CREATED

        response_data = response.json()
        assert response_data["consequence"] == "Not Significant"
        assert response_data["likelihood"] == "Rare"
        assert response_data["consequenceComment"] == "Test comment 1"
        assert response_data["likelihoodComment"] == "Test comment 2"

        created_repairplan = RepairRecommendation.objects.filter(
            target__inspection__uuid=single_inspection.uuid
        ).first()
        assert created_repairplan.risk_consequence.name == "Not Significant"
        assert created_repairplan.risk_likelihood.name == "Rare"
        assert created_repairplan.consequence_comment == "Test comment 1"
        assert created_repairplan.likelihood_comment == "Test comment 2"
        # We want these values to update to null if null/empty/not given in payload
        assert created_repairplan.inspection_frequency is None

    def test_update_repairplan(
        self, client, single_inspection, standard_user, repair_recommendation, repairrecommendation_settings
    ):
        """
        Test that a standard user with inspection access can update a repairplan
        """
        client.force_login(user=standard_user)

        response = client.patch(
            path=repairrecommendation_settings.get_repairplan_url(inspection_id=single_inspection.uuid),
            data={
                "consequence": "Minor",
                "likelihood": "Unlikely",
                "consequenceComment": "Test comment 3",
                "likelihoodComment": "Test comment 4",
            },
            content_type="application/json",
        )
        assert response.status_code == status.HTTP_200_OK

        response_data = response.json()
        assert response_data["consequence"] == "Minor"
        assert response_data["likelihood"] == "Unlikely"
        assert response_data["consequenceComment"] == "Test comment 3"
        assert response_data["likelihoodComment"] == "Test comment 4"

        updated_repairplan = RepairRecommendation.objects.filter(
            target__inspection__uuid=single_inspection.uuid
        ).first()
        assert updated_repairplan.risk_consequence.name == "Minor"
        assert updated_repairplan.risk_likelihood.name == "Unlikely"
        assert updated_repairplan.consequence_comment == "Test comment 3"
        assert updated_repairplan.likelihood_comment == "Test comment 4"


class TestRepairPlanItemListView:
    def test_get_repairplanitems(
        self, client, single_inspection, standard_user, repair_recommendation, repairrecommendation_settings
    ):
        client.force_login(user=standard_user)

        response = client.get(
            path=repairrecommendation_settings.get_repairplanitemlist_url(
                inspection_id=single_inspection.uuid, actor="vapar"
            )
        )

        assert response.status_code == status.HTTP_200_OK

        response_data = response.json()
        assert response_data["actor"] == "vapar"
        assert "items" in response_data

    def test_update_repairplanitems_new(
        self, client, single_inspection, standard_user, repair_recommendation, repairrecommendation_settings
    ):
        client.force_login(user=standard_user)

        patch_data = {
            "actionSummary": "Updated summary",
            "items": [
                {
                    "repairType": "Dig Up",
                    "actor": "contractor",
                    "metadata": [],
                },
            ],
        }

        response = client.patch(
            path=repairrecommendation_settings.get_repairplanitemlist_url(
                inspection_id=single_inspection.uuid, actor="contractor"
            ),
            data=patch_data,
            content_type="application/json",
        )
        assert response.status_code == status.HTTP_200_OK

        response_data = response.json()
        assert response_data["actor"] == "contractor"
        assert response_data["actionSummary"] == "Updated summary"

        updated_repairplan = RepairRecommendation.objects.get(target__inspection__uuid=single_inspection.uuid)
        assert getattr(updated_repairplan, "c_action_summary") == "Updated summary"

    def test_update_repairplanitems_existing(
        self, client, single_inspection, standard_user, repair_recommendation, repairrecommendation_settings
    ):
        client.force_login(user=standard_user)

        patch_data = {
            "actionSummary": "Updated summary",
            "items": [
                {
                    "repairType": "Dig Up",
                    "actor": "contractor",
                    "metadata": [],
                },
            ],
        }

        response = client.patch(
            path=repairrecommendation_settings.get_repairplanitemlist_url(
                inspection_id=single_inspection.uuid, actor="contractor"
            ),
            data=patch_data,
            content_type="application/json",
        )
        assert response.status_code == status.HTTP_200_OK

        response_data = response.json()
        assert response_data["actor"] == "contractor"
        assert response_data["actionSummary"] == "Updated summary"

        updated_repairplan = RepairRecommendation.objects.get(target__inspection__uuid=single_inspection.uuid)
        assert getattr(updated_repairplan, "c_action_summary") == "Updated summary"
        assert getattr(updated_repairplan, "c_dig_up")

        patch_data = {
            "actionSummary": "Updated summary",
            "items": [],
        }

        response = client.patch(
            path=repairrecommendation_settings.get_repairplanitemlist_url(
                inspection_id=single_inspection.uuid, actor="contractor"
            ),
            data=patch_data,
            content_type="application/json",
        )
        assert response.status_code == status.HTTP_200_OK

        updated_repairplan = RepairRecommendation.objects.get(target__inspection__uuid=single_inspection.uuid)
        assert not getattr(updated_repairplan, "c_dig_up")

    def test_update_repairplanitems_noaction(
        self, client, single_inspection, standard_user, repair_recommendation, repairrecommendation_settings
    ):
        client.force_login(user=standard_user)

        patch_data = {
            "actionSummary": "Created Repair Plan",
            "items": [],
        }

        response = client.patch(
            path=repairrecommendation_settings.get_repairplanitemlist_url(
                inspection_id=single_inspection.uuid, actor="owner"
            ),
            data=patch_data,
            content_type="application/json",
        )
        assert response.status_code == status.HTTP_200_OK

        patch_data = {
            "items": [
                {
                    "repairType": "No Action",
                    "actor": "owner",
                    "metadata": [],
                },
            ],
        }

        response = client.patch(
            path=repairrecommendation_settings.get_repairplanitemlist_url(
                inspection_id=single_inspection.uuid, actor="owner"
            ),
            data=patch_data,
            content_type="application/json",
        )
        assert response.status_code == status.HTTP_200_OK

        updated_repairplan = RepairRecommendation.objects.get(target__inspection__uuid=single_inspection.uuid)
        assert getattr(updated_repairplan, "o_no_immediate_action")


class TestRepairPlanItemTypeCost:
    def test_get_repairplanitems_with_cost(
        self, client, single_inspection, standard_user, repair_recommendation, repairrecommendation_settings
    ):
        client.force_login(user=standard_user)

        patch_data = {
            "actionSummary": "Created Repair Plan",
            "items": [],
        }

        response = client.patch(
            path=repairrecommendation_settings.get_repairplanitemlist_url(
                inspection_id=single_inspection.uuid, actor="owner"
            ),
            data=patch_data,
            content_type="application/json",
        )
        assert response.status_code == status.HTTP_200_OK

        patch_data = {
            "items": [
                {
                    "repairType": RepairPlanItemTypeEnum.DIG_UP.value,
                    "actor": "owner",
                    "metadata": [],
                },
                {
                    "repairType": RepairPlanItemTypeEnum.PATCH.value,
                    "actor": "owner",
                    "metadata": [{"key": "Count", "value": 2}],
                },
                {
                    "repairType": RepairPlanItemTypeEnum.CLEANING.value,
                    "actor": "owner",
                    "metadata": [],
                },
                {
                    "repairType": RepairPlanItemTypeEnum.ROOT_REMOVAL.value,
                    "actor": "owner",
                    "metadata": [],
                },
                {
                    "repairType": RepairPlanItemTypeEnum.LINING.value,
                    "actor": "owner",
                    "metadata": [],
                },
            ],
        }

        response = client.patch(
            path=repairrecommendation_settings.get_repairplanitemlist_url(
                inspection_id=single_inspection.uuid, actor="owner"
            ),
            data=patch_data,
            content_type="application/json",
        )
        assert response.status_code == status.HTTP_200_OK

        response = client.get(
            path=repairrecommendation_settings.get_repairplanitemlist_url(
                inspection_id=single_inspection.uuid, actor="owner"
            )
        )

        assert response.status_code == status.HTTP_200_OK

        response_data = response.json()
        assert response_data["actor"] == "owner"
        assert "items" in response_data

        repair_items = response_data["items"]

        assert repair_items[0]["repairType"] == "Patch", repair_items[0]["repairType"]
        # cost when base = 1858 (225 dia) and patch count 2 == 3716.00
        assert repair_items[0]["repairCostEstimate"] == 3716.0, repair_items[0]["repairCostEstimate"]

        assert repair_items[1]["repairType"] == "Cleaning", repair_items[1]["repairType"]
        assert repair_items[1]["repairCostEstimate"] == 1350.0, repair_items[1]["repairCostEstimate"]

        assert repair_items[2]["repairType"] == "Lining", repair_items[2]["repairType"]
        assert repair_items[2]["repairCostEstimate"] == 31950.0, repair_items[2]["repairCostEstimate"]

        assert repair_items[3]["repairType"] == "Root Removal", repair_items[3]["repairType"]
        assert repair_items[3]["repairCostEstimate"] == 1350.0, repair_items[3]["repairCostEstimate"]

        assert repair_items[4]["repairType"] == "Dig Up", repair_items[4]["repairType"]
        assert repair_items[4]["repairCostEstimate"] == 20000.0, repair_items[4]["repairCostEstimate"]
