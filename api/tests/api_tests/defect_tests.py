import pytest
from django.test.client import Client

from api.common.enums import UserLevelEnum
from api.tests.settings import DefectSettings
from api.users.models import CustomUser

pytestmark = pytest.mark.django_db


def test_standard_subcategories_list_all(standard_user: CustomUser, defect_settings: DefectSettings):
    client = Client()
    client.force_login(standard_user)

    response = client.get(defect_settings.standard_subcategories_url)
    assert response.status_code == 200


def test_standard_subcategories_list_filtered(standard_user: CustomUser, defect_settings: DefectSettings):
    client = Client()
    client.force_login(standard_user)

    response = client.get(defect_settings.standard_subcategories_url + "?standard_key__name=MSCC5&pipe_type_sewer=true")
    assert response.status_code == 200

    res_data = response.json()
    assert res_data["count"] == 1
    assert res_data["results"][0]["standard"]["displayName"] == "UK V5"
    assert res_data["results"][0]["pipeTypeSewer"] is True
    assert res_data["results"][0]["materialType"] == "Rigid"
    assert res_data["results"][0]["comment"] == "UK V5 Sewer Rigid"
    assert res_data["results"][0]["region"] == "UK"


def test_standard_defects_list_all(service_user: CustomUser, defect_settings: DefectSettings):
    client = Client()
    client.force_login(service_user)

    response = client.get(defect_settings.standard_defects_url)
    assert response.status_code == 200


def test_standard_defects_list_filtered(service_user: CustomUser, defect_settings: DefectSettings):
    client = Client()
    client.force_login(service_user)

    response = client.get(defect_settings.standard_defects_url + "?standard_key__name=MSCC5&defect_key__id=3")
    assert response.status_code == 200

    test_result = None
    res_data = response.json()
    for r in res_data:
        if r["id"] == 1529:
            test_result = r

    assert test_result["substandard"]["standard"]["displayName"] == "UK V5"
    assert test_result["substandard"]["comment"] == "UK V5 Sewer Rigid"
    assert test_result["substandard"]["region"] == "UK"
    assert test_result["defectDescription"] == "B - Broken"
    assert test_result["defectModelName"] == "Breaking - Displaced_Small"
    assert test_result["defectModelId"] == 3
    assert test_result["serviceScore"] == "0"
    assert test_result["structuralScore"] == "80"
    assert test_result["defectType"] == "str"
    assert test_result["quantity1DefaultVal"] is None
    assert test_result["quantity1Units"] == ""
    assert test_result["quantity2DefaultVal"] is None
    assert test_result["quantity2Units"] == ""
    assert test_result["defectCode"] == "B"


def test_standards_list_as_upload_only_user(standard_user: CustomUser, defect_settings: DefectSettings):
    client = Client()
    client.force_login(standard_user)
    standard_user.user_level = UserLevelEnum.UPLOAD_ONLY
    standard_user.save()

    """
    Test that an upload only user can retrieve a list of standards
    """
    response = client.get(defect_settings.standards_url)
    assert response.status_code == 200
