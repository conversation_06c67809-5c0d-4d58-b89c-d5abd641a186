from unittest.mock import patch, MagicMock

import pytest

from django.test.client import Client
from rest_framework import status

from api.recommendations.models import RepairRecommendation, Custom_Repair_Values as CustomRepairValue
from api.recommendations.tasks.tasks import update_or_create_recommendation
from api.actions.models import AuditList
from api.inspections.models import Inspection, MapPointList
from api.users.models import CustomUser
from api.tests.settings import RepairRecommendationSettings

pytestmark = [pytest.mark.django_db(databases=["default"])]


class RepairRecommendationTests:

    client: Client = Client()
    standard_user: CustomUser = None

    @pytest.fixture(autouse=True)
    def setup_method(self, standard_user):
        self.standard_user = standard_user
        self.inspection = Inspection.objects.get(uuid="23896fe4-4baa-11ee-be56-0242ac120002")
        self.client.force_login(user=self.standard_user)

    def test_update_or_create_existing_item(self):
        user = CustomUser()

        item = {
            "vapar_id": "123",
            "counted": 1,
            "whole_rec_required": True,
            "recommendations": "Digup and fix whole pipe",
            "location": "Some location",
            "video_file": 1,
            "asset_id": "123",
            "start_node": "Node 1",
            "end_node": "Node 2",
            "length_inspected": 100,
            "direction": "Downstream",
            "digup": "True",
            "user_params": user,
        }

        mock_rr_instance = MagicMock(spec=RepairRecommendation)
        mock_rr_instance.save = MagicMock()

        with patch(
            "api.recommendations.models.RepairRecommendation.objects.get", return_value=mock_rr_instance
        ) as mock_get, patch(
            "api.inspections.models.MapPointList.objects.get", return_value=MagicMock(spec=MapPointList)
        ), patch(
            "api.recommendations.models.RepairRecommendation.save", autospec=True
        ):

            summary = update_or_create_recommendation(item, user)
            mock_get.assert_called_once_with(target_id=item["vapar_id"])
            mock_rr_instance.save.assert_called_once()

            assert mock_rr_instance.patches_counted == 0
            assert summary == {
                "id": item["vapar_id"],
                "location": item["location"],
                "video_file": item["video_file"],
                "asset_id": item["asset_id"],
                "start_node": item["start_node"],
                "end_node": item["end_node"],
                "direction": item["direction"],
                "length_inspected": item["length_inspected"],
                "recommendations": item["recommendations"],
            }, "Summary does not match expected value."

    def test_update_or_create_item_not_found(self):
        user = CustomUser()

        item = {
            "vapar_id": "123",
            "counted": 1,
            "whole_rec_required": True,
            "recommendations": "Digup and fix whole pipe",
            "location": "Some location",
            "video_file": 1,
            "asset_id": "123",
            "start_node": "Node 1",
            "end_node": "Node 2",
            "length_inspected": 100,
            "direction": "Downstream",
            "digup": "True",
            "user_params": user,
        }

        mock_audit_instance = MagicMock(spec=AuditList)
        mock_audit_instance.create = MagicMock()

        with patch(
            "api.recommendations.models.RepairRecommendation.objects.get", side_effect=RepairRecommendation.DoesNotExist
        ), patch(
            "api.actions.models.AuditList.objects.create", return_value=mock_audit_instance
        ) as mock_auditlist_create, patch(
            "api.inspections.models.MapPointList.objects.get", side_effect=MapPointList.DoesNotExist
        ):

            summary = update_or_create_recommendation(item, user)
            mock_auditlist_create.assert_called_once()

            assert summary is None, "Expected None when MapPointList does not exist."

    def test_repair_recommendation_consequence(self, repairrecommendation_settings: RepairRecommendationSettings):
        response = self.client.get(
            path=repairrecommendation_settings.repair_recommendation_list_url.format(vapar_id=self.inspection.legacy_id)
        )
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert "consequence" in response_data

    def test_num_repair_recommendation_csv_records(self, repairrecommendation_settings: RepairRecommendationSettings):
        mpl = MapPointList.objects.filter(inspection=self.inspection).first()
        mpl.status = "Reviewed"
        mpl.save()
        response = self.client.post(
            path=repairrecommendation_settings.repair_recommendation_csv_url,
            data={'inspection_ids': [str(self.inspection.legacy_id)]},
            content_type="application/json"
        )
        assert response.status_code == status.HTTP_200_OK
        assert response.content.decode('utf-8').count('\n') == 3

    def test_get_custom_repair_values(self, repairrecommendation_settings: RepairRecommendationSettings):
        mpl = MapPointList.objects.filter(inspection=self.inspection).first()
        crv = CustomRepairValue.objects.create(point=mpl)
        response = self.client.get(
            path = repairrecommendation_settings.get_custom_repair_value_url(
                inspection_id=mpl.id
            )
        )
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data[0]["id"] == crv.id
