import json
import pytest

from django.test.client import Client
from rest_framework import status

from api.defects.models import Standard
from api.users.models import CustomUser
from api.organisations.models import Organisations, AssetOwners
from api.inspections.models import InspectionFilter

pytestmark = [pytest.mark.django_db(databases=["default"])]


class TestOrganisations:
    """
    Tests for the Organisations API

    1. GET /organisations
    2. GET /organisations/{id}
    3. POST /organisations
    4. PATCH /organisations/{id}
    5. POST /organisations/{id}/upload-logo
    """

    client: Client = Client()
    product_owner: CustomUser = None
    standard_user: CustomUser = None
    asset_owner: Organisations = None
    contractor: Organisations = None

    @pytest.fixture(autouse=True)
    def setup_method(self, product_owner, standard_user, asset_owner_org, contractor_org):
        self.product_owner = product_owner
        self.standard_user = standard_user
        self.asset_owner = asset_owner_org
        self.contractor = contractor_org
        self.client.force_login(user=self.product_owner)

    def test_get_organisation_list_as_product_owner(self, organisation_settings):
        """
        Test that a product owner can get a list of organisations
        """
        response = self.client.get(path=organisation_settings.organisation_list_url)

        assert response.status_code == status.HTTP_200_OK
        assert "results" in response.data
        assert isinstance(response.data["results"], list)

    def test_get_organisation_list_as_standard_user(self, organisation_settings):
        """
        Test that a standard user is not able to get a list of organisations
        """
        self.client.force_login(user=self.standard_user)

        response = self.client.get(path=organisation_settings.organisation_list_url)

        assert response.status_code == status.HTTP_403_FORBIDDEN

    def test_get_asset_owner_organisations(self, organisation_settings, contractor_org, asset_owner_org):
        """
        test that a product owner can get a list of asset owner organisations
        """
        self.client.force_login(user=self.product_owner)

        response = self.client.get(path=organisation_settings.organisation_list_url + "?org_type=Asset_Owner")
        assert response.status_code == status.HTTP_200_OK
        reponse_data = response.json()

        assert "results" in reponse_data
        assert isinstance(reponse_data["results"], list)
        assert reponse_data["count"] > 0

        for organisation in reponse_data["results"]:
            assert organisation["orgType"] == "Asset_Owner"

    def test_get_contractor_organisations(self, organisation_settings, contractor_org, asset_owner_org):
        """
        test that a product owner can get a list of contractor organisations
        """
        self.client.force_login(user=self.product_owner)

        # create a contractor and asset owner
        response = self.client.get(path=organisation_settings.organisation_list_url + "?org_type=Contractor")
        assert response.status_code == status.HTTP_200_OK
        reponse_data = response.json()

        assert "results" in reponse_data
        assert isinstance(reponse_data["results"], list)
        assert reponse_data["count"] > 0

        for organisation in reponse_data["results"]:
            assert organisation["orgType"] == "Contractor"

    def test_get_organisation_by_id_as_product_owner(self, organisation_settings):
        """
        Test that a product owner can get an organisation
        """
        organisation_id = self.asset_owner.id
        response = self.client.get(path=organisation_settings.organisation_url.format(id=organisation_id))
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert isinstance(response_data, dict)
        assert "id" in response_data
        assert response_data["id"] == organisation_id

    def test_get_organisation_by_id_as_standard_user(self, organisation_settings):
        """
        Test that a standard user can get an organisation
        """
        self.client.force_login(user=self.standard_user)

        organisation_id = self.asset_owner.id
        response = self.client.get(path=organisation_settings.organisation_url.format(id=organisation_id))
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()

        assert isinstance(response_data, dict)
        assert "id" in response_data
        assert response_data["id"] == organisation_id

    def test_create_organisation_as_product_owner(self, organisation_settings):
        """
        Test that a product owner can create an organisation
        """
        # TODO: to be deprecation with upcoming user/org changes
        vapar_user_data = {"password": "Test123!@#", "short_name": "testorg"}

        data = {
            "org_type": "Asset_Owner",
            "full_name": "Test Organisation",
            "email_domain": "vapar.co",
            "sewer_data": True,
            "standard_key": 6,
            "country": "AU",
            **vapar_user_data,
        }

        response = self.client.post(
            path=organisation_settings.organisation_list_url, data=data, content_type="application/json"
        )

        assert response.status_code == status.HTTP_201_CREATED
        response_data = response.json()

        # check response
        assert isinstance(response_data, dict)
        assert "id" in response_data
        assert "orgType" in response_data
        assert response_data["orgType"] == data["org_type"]
        assert "fullName" in response_data
        assert response_data["fullName"] == data["full_name"]

        # check db
        organisation = Organisations.objects.get(id=response_data["id"])
        assert organisation
        assert organisation.id
        assert organisation.full_name == data["full_name"]
        assert organisation.email_domain == data["email_domain"]

    def test_create_organisation_as_standard_user(self, organisation_settings):
        """
        Test that a standard user cannot create an organisation
        """
        self.client.force_login(user=self.standard_user)

        vapar_user_data = {"password": "Test123!@#", "short_name": "testorg"}

        data = {
            "org_type": "Asset_Owner",
            "full_name": "Test Organisation",
            "email_domain": "vapar.co",
            "sewer_data": True,
            "standard_key": 6,
            "country": "AU",
            **vapar_user_data,
        }

        response = self.client.post(
            path=organisation_settings.organisation_list_url, data=data, content_type="application/json"
        )

        assert response.status_code == status.HTTP_403_FORBIDDEN

    def test_create_organisation_default_repair_param(self, organisation_settings):
        """
        Test that when creating an organisation, the correct default_repair_param is set.
        """
        self.client.force_login(user=self.product_owner)

        vapar_user_data = {"password": "Test1234!", "short_name": "testorg"}
        data = {
            "org_type": "Asset_Owner",
            "full_name": "Test US Organisation",
            "email_domain": "vapar.co",
            "sewer_data": True,
            "standard_key": 3,
            "country": "AU",
            **vapar_user_data,
        }

        response = self.client.post(
            path=organisation_settings.organisation_list_url, data=data, content_type="application/json"
        )

        assert response.status_code == status.HTTP_201_CREATED
        response_data = response.json()
        assert "id" in response_data
        assert "standardKey" in response_data
        assert response_data["standardKey"] == data["standard_key"]

        # check db
        organisation = Organisations.objects.get(id=response_data["id"])
        expected_repair_param = Standard.objects.get(id=response_data["standardKey"]).default_repair_param
        assert json.loads(organisation.repair_param) == expected_repair_param

    def test_update_organisation_by_id_as_product_owner(self, organisation_settings):
        """
        Test that a product owner can update an organisation
        """
        organisation = self.asset_owner
        data = {"country": "GB"}

        assert organisation.country != data["country"]

        response = self.client.patch(
            path=organisation_settings.organisation_url.format(id=self.asset_owner.id),
            data=data,
            content_type="application/json",
        )
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data["country"] == data["country"]

        organisation = Organisations.objects.get(id=response_data["id"])
        assert organisation.country == data["country"]

    def test_update_organisation_by_id_as_standard_user(self, organisation_settings):
        """
        Test that a standard user cannot update an organisation
        """
        self.client.force_login(user=self.standard_user)

        organisation = self.asset_owner
        data = {"country": "GB"}

        assert organisation.country != data["country"]

        response = self.client.patch(
            path=organisation_settings.organisation_url.format(id=self.asset_owner.id),
            data=data,
            content_type="application/json",
        )
        assert response.status_code == status.HTTP_403_FORBIDDEN

    def test_delete_organisation_by_id_as_product_owner(self, organisation_settings):
        users = CustomUser.objects.filter(organisation=self.asset_owner.id)
        assert len(users) > 0

        response = self.client.delete(path=organisation_settings.organisation_url.format(id=self.asset_owner.id))
        assert response.status_code == status.HTTP_400_BAD_REQUEST

        # TODO: expand
        # 400 for org with inspections
        # success when no users or inspections

    def test_delete_organisation_by_id_as_standard_user(self, organisation_settings):
        self.client.force_login(user=self.standard_user)

        users = CustomUser.objects.filter(organisation=self.asset_owner.id)
        assert len(users) > 0

        response = self.client.delete(path=organisation_settings.organisation_url.format(id=self.asset_owner.id))
        assert response.status_code == status.HTTP_403_FORBIDDEN

    # def test_upload_organisation_logo_as_product_owner(self, organisation_settings):
    # script_dir = os.path.dirname(__file__)
    # image_file_path = os.path.join(script_dir, "resources", "test_logo.png")
    #
    # file = {"file": ("test_logo.png", open(image_file_path, "rb"), "image/png")}
    # response = self.client.post(
    #     path=organisation_settings.organisation_logo_url,
    #     files=file
    # )

    # def test_upload_organisation_logo_as_standard_user(self):
    #     self.client.force_login(user=self.standard_user)
    #     assert False

    def test_link_contractor_to_asset_owner_as_product_owner(self, organisation_settings):
        """
        Test that a product owner can link a contractor to an asset owner
        """
        response = self.client.post(
            path=organisation_settings.organisation_link_url.format(
                contractor_id=self.contractor.id, asset_owner_id=self.asset_owner.id
            )
        )
        assert response.status_code == status.HTTP_200_OK

        contractors = AssetOwners.contractor.through.objects.filter(contractors__org=self.contractor.id)

        assert self.asset_owner in (contractor.assetowners.org for contractor in contractors)

    def test_link_contractor_to_asset_owner_as_standard_user(self, organisation_settings):
        self.client.force_login(user=self.standard_user)
        response = self.client.post(
            path=organisation_settings.organisation_link_url.format(
                contractor_id=self.contractor.id, asset_owner_id=self.asset_owner.id
            )
        )
        assert response.status_code == status.HTTP_403_FORBIDDEN

    def test_filter_records_created_when_orgs_linked(self, organisation_settings):
        """
        Test that a record is created when a contractor is linked to an asset owner
        """
        contractor = self.contractor
        self.standard_user.organisation = contractor
        self.standard_user.save()

        # create new asset for linking
        data = {
            "full_name": "Test Asset Owner",
            "org_type": "Asset_Owner",
            "email_domain": "vapar.co",
            "sewer_data": True,
            "standard_key": 6,
            "country": "AU",
            "short_name": "test_ao",
            "password": "Test123!@#",
        }
        response = self.client.post(
            path=organisation_settings.organisation_list_url, data=data, content_type="application/json"
        )
        assert response.status_code == status.HTTP_201_CREATED
        asset_owner_data = response.json()

        # check user doesn't have a filter record for this org yet
        filter_record = InspectionFilter.objects.filter(
            user=self.standard_user, organisation=asset_owner_data["id"]
        ).first()
        assert not filter_record

        response = self.client.post(
            path=organisation_settings.organisation_link_url.format(
                contractor_id=contractor.id, asset_owner_id=asset_owner_data["id"]
            )
        )
        assert response.status_code == status.HTTP_200_OK

        filter_record = InspectionFilter.objects.filter(
            user=self.standard_user, organisation=asset_owner_data["id"]
        ).first()
        assert filter_record

    def test_change_to_contractor(self, organisation_settings):
        """
        Test that a product owner can change an organisation to a contractor
        """
        self.client.force_login(user=self.product_owner)

        data = {"orgType": "Contractor"}
        response = self.client.patch(
            path=organisation_settings.organisation_url.format(id=self.asset_owner.id),
            data=data,
            content_type="application/json",
        )
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data["orgType"] == data["orgType"]

        organisation = Organisations.objects.get(id=response_data["id"])
        assert organisation.org_type == data["orgType"]
        assert hasattr(organisation, "contractors")
        assert not hasattr(organisation, "assetowners")

    def test_change_to_asset_owner(self, organisation_settings):
        """
        Test that a product owner can change an organisation to an asset owner
        """
        self.client.force_login(user=self.product_owner)

        data = {"orgType": "Asset_Owner"}
        response = self.client.patch(
            path=organisation_settings.organisation_url.format(id=self.contractor.id),
            data=data,
            content_type="application/json",
        )
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data["orgType"] == data["orgType"]

        organisation = Organisations.objects.get(id=response_data["id"])
        assert organisation.org_type == data["orgType"]
        assert hasattr(organisation, "assetowners")
        assert not hasattr(organisation, "contractors")
