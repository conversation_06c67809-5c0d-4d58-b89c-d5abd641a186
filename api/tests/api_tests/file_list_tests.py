from datetime import datetime, timezone

import pytest
from django.conf import settings
from django.db import transaction
from django.utils import timezone as djtimezone
from rest_framework import status
from vapar.constants.processing import ProcessingStatusEnum

from api.inspections.models import FileList, JobsTree, ProcessingList


pytestmark = pytest.mark.django_db


@pytest.fixture
def standalone_file_record(asset_owner_org) -> FileList:
    org = asset_owner_org
    obj = FileList.objects.create(upload_org=org, target_org=org, storage_region=str(org.country.code))
    return obj


def test_uploaded_file_list(client, asset_owner_org, standard_user, inspection_settings):
    org = asset_owner_org

    f1 = FileList.objects.create(
        filename="1",
        target_org=org,
        upload_completed=True,
        upload_completed_time=datetime(2024, 1, 1, tzinfo=timezone.utc),
        processing_completed_time=datetime(2024, 1, 2, tzinfo=timezone.utc),
    )

    f2 = FileList.objects.create(
        filename="2",
        target_org=org,
        upload_completed=True,
        upload_completed_time=datetime(2024, 1, 2, tzinfo=timezone.utc),
    )
    ProcessingList.objects.create(
        associated_file=f2,
        upload_user=standard_user.full_name,
        target_org=org,
        status=ProcessingStatusEnum.PROCESSING,
    )

    f3 = FileList.objects.create(
        filename="3",
        target_org=org,
        upload_completed=True,
        upload_completed_time=datetime(2024, 1, 2, tzinfo=timezone.utc),
    )
    ProcessingList.objects.create(
        associated_file=f3,
        upload_user=standard_user.full_name,
        target_org=org,
        status=ProcessingStatusEnum.FAILED_TO_PROCESS,
    )

    client.force_login(user=standard_user)
    res = client.get(inspection_settings.list_uploaded_files_url)
    assert res.status_code == status.HTTP_200_OK
    res_data = res.json()
    assert len(res_data["results"]) == 1
    assert res_data["results"][0]["filename"] == f1.filename


def test_file_list_urls(client, asset_owner_org, standard_user, inspection_settings):
    org = asset_owner_org

    f1 = FileList.objects.create(
        filename="1.mpg",
        url="uploadedvideofiles/10_10_2024_12_34_567-1.mpg",
        play_url="uploadedvideofiles/10_10_2024_12_34_567-1_play.mpg",
        storage_region="AU",
        target_org=org,
        upload_completed=True,
        upload_completed_time=datetime(2024, 1, 1, tzinfo=timezone.utc),
        processing_completed_time=datetime(2024, 1, 2, tzinfo=timezone.utc),
    )

    f2 = FileList.objects.create(
        filename="2.mpg",
        url="uploadedvideofiles/10_10_2024_12_34_567-2.mpg",
        play_url="uploadedvideofiles/10_10_2024_12_34_567-2_play.mpg",
        storage_region="AU",
        target_org=org,
        upload_completed=True,
        upload_completed_time=datetime(2024, 1, 2, tzinfo=timezone.utc),
        processing_completed_time=datetime(2024, 1, 2, tzinfo=timezone.utc),
    )

    client.force_login(user=standard_user)
    res = client.get(inspection_settings.list_files_url)
    assert res.status_code == status.HTTP_200_OK

    res_data = res.json()
    assert len(res_data["results"]) == 2
    base_platform_url = "https://mock-url.com/"
    assert res_data["results"][1]["downloadUrl"].startswith(
        f"{base_platform_url}{f1.url}"
    ), f"downloadUrl does not start with {base_platform_url}{f1.url} ({res_data['results'][1]['downloadUrl']})"
    assert "mock-sas-token" in res_data["results"][1]["downloadUrl"]
    assert res_data["results"][1]["playUrl"].startswith(
        f"{base_platform_url}{f1.play_url}"
    ), f"playUrl does not start with {base_platform_url}{f1.play_url} ({res_data['results'][1]['playUrl']})"
    assert res_data["results"][0]["downloadUrl"].startswith(
        f"{base_platform_url}{f2.url}"
    ), f"downloadUrl does not start with {base_platform_url}{f2.url} ({res_data['results'][0]['downloadUrl']})"
    assert res_data["results"][0]["playUrl"].startswith(
        f"{base_platform_url}{f2.play_url}"
    ), f"playUrl does not start with {base_platform_url}{f2.play_url} ({res_data['results'][0]['playUrl']})"


def test_file_list_create(client, service_user, service_user_key, asset_owner_org, folder, inspection_settings):
    n_before = FileList.objects.filter(target_org=asset_owner_org).count()
    res = client.post(
        inspection_settings.list_files_url,
        data={
            "jobTree": folder.id,
        },
        HTTP_X_API_KEY=service_user_key,
        HTTP_X_TARGET_ORG_ID=asset_owner_org.id,
        content_type="application/json",
    )
    assert res.status_code == status.HTTP_201_CREATED

    n_after = FileList.objects.filter(target_org=asset_owner_org).count()
    assert n_after == n_before + 1

    data = res.json()
    file_obj = FileList.objects.get(id=data["id"])
    assert file_obj.job_tree == folder
    assert file_obj.target_org == asset_owner_org
    assert file_obj.upload_org == asset_owner_org
    assert file_obj.uploaded_by is None, "No actual file has been uploaded"
    assert file_obj.upload_completed is False
    assert file_obj.upload_completed_time is None
    assert file_obj.processing_completed_time is None
    assert file_obj.processing_started_time is None
    assert file_obj.hidden is False
    assert file_obj.total_frames == 0, "No frames have been associated with this record"

    assert data["jobTree"] == folder.id
    assert data["targetOrg"] == asset_owner_org.id
    assert data["uploadOrg"] == asset_owner_org.id
    assert data["uploadedBy"] is None
    assert data["uploadCompleted"] is False
    assert data["uploadCompletedTime"] is None
    assert data["hidden"] is False
    assert data["playUrl"] is None
    assert data["downloadUrl"] is None


def test_file_list_standalone_media_upload(
    client, service_user, service_user_key, asset_owner_org, standalone_file_record, inspection_settings
):
    res = client.post(
        inspection_settings.get_upload_standalone_url(standalone_file_record.id),
        data={
            "filename": "test.mp4",
            "fileSize": "123MB",
        },
        HTTP_X_API_KEY=service_user_key,
        HTTP_X_TARGET_ORG_ID=asset_owner_org.id,
        content_type="application/json",
    )
    assert res.status_code == status.HTTP_200_OK

    data = res.json()
    file_obj = FileList.objects.get(id=standalone_file_record.id)

    assert file_obj.filename == "test.mp4"
    assert "test.mp4" in file_obj.url
    assert settings.BLOB_STORAGE_VIDEOS_CONTAINER in file_obj.url
    assert file_obj.file_size == "123MB"

    assert "test.mp4" in data["blobUrlWithSas"]


@pytest.fixture
def file_instances(asset_owner_org, linked_contractor_org, contractor_org, linked_contractor_folder):
    root = JobsTree.objects.filter(primary_org=asset_owner_org).first()

    # Create test folders
    folder1 = root.add_child(
        job_name="Folder1",
        created_date=djtimezone.now(),
        standard_key_id=1,
        pipe_type_sewer=True,
        primary_org=None,
        secondary_org=None,
    )
    folder2 = root.add_child(
        job_name="Folder2",
        created_date=djtimezone.now(),
        standard_key_id=1,
        pipe_type_sewer=True,
        primary_org=None,
        secondary_org=None,
    )
    folder_mismatch = root.add_child(
        job_name="MismatchFolder",
        created_date=djtimezone.now(),
        standard_key_id=2,
        pipe_type_sewer=False,
        primary_org=None,
        secondary_org=None,
    )
    folder_invalid = root.add_child(
        job_name="InvalidFolder",
        created_date=djtimezone.now(),
        standard_key_id=1,
        pipe_type_sewer=True,
        primary_org=None,
        secondary_org=contractor_org,  # Setings this folder as contractor "root"
    )

    # Create test FileList instances
    file1 = FileList.objects.create(
        filename="file1.mp4",
        file_type="video/mp4",
        file_size="10MB",
        url="url1",
        target_org=asset_owner_org,
        upload_org=asset_owner_org,
        upload_user="user1",
        job_tree=folder1,
        uploaded_by=None,
        storage_region="AU",
        upload_completed=True,
    )
    file2 = FileList.objects.create(
        filename="file2.mp4",
        file_type="video/mp4",
        file_size="20MB",
        url="url2",
        target_org=asset_owner_org,
        upload_org=asset_owner_org,
        upload_user="user2",
        job_tree=folder1,
        uploaded_by=None,
        storage_region="AU",
        upload_completed=True,
    )

    return {
        "file1": file1,
        "file2": file2,
        "folder1": folder1,
        "folder2": folder2,
        "folder_mismatch": folder_mismatch,
        "folder_invalid": folder_invalid,
        "org1": asset_owner_org,
        "org2": linked_contractor_org,
    }


@pytest.mark.django_db
def test_bulk_patch_successful(client, standard_user, file_instances, inspection_settings):
    url = inspection_settings.list_files_url

    client.force_login(user=standard_user)

    data = [
        {"id": file_instances["file1"].id, "job_tree": file_instances["folder2"].id},
        {"id": file_instances["file2"].id, "job_tree": file_instances["folder2"].id},
    ]

    response = client.patch(url, data=data, content_type="application/json")

    assert response.status_code == status.HTTP_200_OK
    assert len(response.data) == 2

    # Refresh and check updates
    file_instances["file1"].refresh_from_db()
    file_instances["file2"].refresh_from_db()
    assert file_instances["file1"].job_tree == file_instances["folder2"]
    assert file_instances["file2"].job_tree == file_instances["folder2"]


@pytest.mark.django_db
def test_bulk_patch_validation_failure_mismatch(client, standard_user, file_instances, inspection_settings):
    url = inspection_settings.list_files_url

    client.force_login(standard_user)

    data = [{"id": file_instances["file1"].id, "job_tree": file_instances["folder_mismatch"].id}]

    response = client.patch(url, data=data, content_type="application/json")

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert "Cannot move files (inspections) to folders with different Standards or Pipe Types." in str(response.data)


@pytest.mark.django_db
def test_bulk_patch_invalid_folder(client, standard_user, file_instances, inspection_settings):
    url = inspection_settings.list_files_url

    client.force_login(standard_user)

    data = [{"id": file_instances["file1"].id, "job_tree": file_instances["folder_invalid"].id}]

    response = client.patch(url, data=data, content_type="application/json")

    assert response.status_code == status.HTTP_403_FORBIDDEN  # Since PermissionDenied is raised
    assert "Cannot move files (inspections) into the root folder or folders that contain other folders" in str(
        response.data
    )


@pytest.mark.django_db
def test_bulk_patch_non_list_data(client, standard_user, inspection_settings):
    url = inspection_settings.list_files_url

    client.force_login(standard_user)

    data = {"id": 1, "job_tree": 2}  # Single dict, not list

    response = client.patch(url, data=data, content_type="application/json")

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert "Request data must be a list of objects to update." in response.data["error"]


@pytest.mark.django_db
def test_bulk_patch_missing_id(client, standard_user, file_instances, inspection_settings):
    url = inspection_settings.list_files_url

    client.force_login(standard_user)

    data = [{"job_tree": file_instances["folder2"].id}]  # Missing id

    response = client.patch(url, data=data, content_type="application/json")

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert "Missing 'id' in data." in str(response.data)


@pytest.mark.django_db
def test_bulk_patch_non_existent_id(client, standard_user, file_instances, inspection_settings):
    url = inspection_settings.list_files_url

    client.force_login(standard_user)

    data = [{"id": 9999, "job_tree": file_instances["folder2"].id}]  # Non-existent id

    response = client.patch(url, data=data, content_type="application/json")

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert "File with id 9999 does not exist." in str(response.data)


@pytest.mark.django_db
def test_bulk_patch_partial_success_with_errors(client, standard_user, file_instances, inspection_settings):
    # Note: Current implementation doesn"t support partial success; it fails all if any invalid.
    # If modified for partial, adjust test. For now, test all fail or all succeed.
    url = inspection_settings.list_files_url

    client.force_login(standard_user)

    data = [
        {"id": file_instances["file1"].id, "job_tree": file_instances["folder2"].id},  # Valid
        {"id": file_instances["file2"].id, "job_tree": file_instances["folder_mismatch"].id},  # Invalid
    ]

    with transaction.atomic():  # To simulate, but view has @atomic
        response = client.patch(url, data=data, content_type="application/json")

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    # Check no changes occurred due to atomic
    file_instances["file1"].refresh_from_db()
    assert file_instances["file1"].job_tree == file_instances["folder1"]
