import pytest
from django.test.client import Client
from rest_framework import status

from api.organisations.models import Organisations
from api.tests.settings import OrganisationSettings
from api.users.models import CustomUser

pytestmark = pytest.mark.django_db


def test_api_key_auth_no_header(service_user: CustomUser, organisation_settings: OrganisationSettings):
    client = Client()

    response = client.get(
        organisation_settings.organisation_list_url,
    )
    assert response.status_code == status.HTTP_401_UNAUTHORIZED


def test_api_key_auth_blank_header(service_user: CustomUser, organisation_settings: OrganisationSettings):
    client = Client()

    response = client.get(
        organisation_settings.organisation_list_url,
        HTTP_X_API_KEY="",
    )
    assert response.status_code == status.HTTP_401_UNAUTHORIZED


def test_api_key_incorrect_key(
    service_user: CustomUser, service_user_key: str, organisation_settings: OrganisationSettings
):
    client = Client()

    response = client.get(
        organisation_settings.organisation_list_url,
        HTTP_X_API_KEY="not-a-key",
    )
    assert response.status_code == status.HTTP_401_UNAUTHORIZED


def test_api_key_correct_key(
    service_user: CustomUser, service_user_key: str, organisation_settings: OrganisationSettings
):
    client = Client()

    response = client.get(
        organisation_settings.organisation_list_url,
        HTTP_X_API_KEY=service_user_key,
    )
    assert response.status_code == status.HTTP_200_OK


def test_target_org_invalid(
    service_user: CustomUser,
    service_user_key: str,
    organisation_settings: OrganisationSettings,
    asset_owner_org: Organisations,
):
    client = Client()

    response = client.get(
        organisation_settings.organisation_list_url,
        HTTP_X_API_KEY=service_user_key,
        HTTP_X_TARGET_ORG_ID="99999",
    )
    assert response.status_code == status.HTTP_403_FORBIDDEN


def test_target_org_valid(
    service_user: CustomUser,
    service_user_key: str,
    organisation_settings: OrganisationSettings,
    asset_owner_org: Organisations,
):
    client = Client()

    response = client.get(
        organisation_settings.organisation_list_url,
        HTTP_X_API_KEY=service_user_key,
        HTTP_X_TARGET_ORG_ID=str(asset_owner_org.id),
    )
    assert response.status_code == status.HTTP_200_OK
