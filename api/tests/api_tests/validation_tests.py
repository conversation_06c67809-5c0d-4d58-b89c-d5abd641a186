import csv
import unittest
import os
from unittest.mock import patch

import pytest


# Initialize Django settings
import django

from api.defects.standards import BaseStandard, MSCC5
from api.common.errors import CustomValidationError
from api.defects.models import DefectScores, StandardHeader
from api.inspections.models import InspectionValue, AssetValue, VideoFrames

# Set the DJANGO_SETTINGS_MODULE environment variable
os.environ["DJANGO_SETTINGS_MODULE"] = "config.settings"
django.setup()

pytestmark = [pytest.mark.django_db(databases=["default"])]


class TestBaseStandard(unittest.TestCase):
    def test_validate_standard_inspection_value_missing_header(self):
        standard_header = StandardHeader(name="TestHeader", data_type="string")
        inspection_value = InspectionValue(value="test_value", standard_header=standard_header)
        base_standard = BaseStandard()

        with patch.object(BaseStandard, "headers", return_value=[]):
            result = base_standard.validate_standard_value(inspection_value)
            expected_error = CustomValidationError(
                title="TestHeader",
                message="TestHeader is not a valid inspection value for  Standard",
                field_name="TestHeader",
                entity="inspection",
            )
            self.assertEqual(result, expected_error.serialize())

    def test_validate_standard_asset_value_missing_header(self):
        standard_header = StandardHeader(name="TestHeader", data_type="string")
        asset_value = AssetValue(value="test_value", standard_header=standard_header)
        base_standard = BaseStandard()

        with patch.object(BaseStandard, "headers", return_value=[]):
            result = base_standard.validate_standard_value(asset_value)
            expected_error = CustomValidationError(
                title="TestHeader",
                message="TestHeader is not a valid asset value for  Standard",
                field_name="TestHeader",
                entity="asset",
            )
            self.assertEqual(result, expected_error.serialize())

    def test_validate_standard_value_present_inspection_header(self):
        standard_header = StandardHeader(name="TestHeader", data_type="string")
        inspection_value = InspectionValue(value="test_value", standard_header=standard_header)
        standard = MSCC5()
        standard.headers = [standard_header]

        result = standard.validate_standard_value(inspection_value)
        self.assertIsNone(result)

    def test_validate_standard_value_present_asset_header(self):
        standard_header = StandardHeader(name="TestHeader", data_type="string")
        asset_value = AssetValue(value="test_value", standard_header=standard_header)
        standard = MSCC5()
        standard.headers = [standard_header]

        result = standard.validate_standard_value(asset_value)
        self.assertIsNone(result)

    def test_validate_standard_value_invalid_data_type(self):
        standard_header = StandardHeader(name="TestHeader", data_type="number")
        inspection_value = InspectionValue(value="test_value", standard_header=standard_header)

        standard = MSCC5()
        standard.headers = [standard_header]

        result = standard.validate_standard_value(inspection_value)
        expected_error = CustomValidationError(
            title="TestHeader",
            message="test_value is an invalid value for this field. Expected data type is number",
            field_name="TestHeader",
            entity="inspection",
        )
        self.assertEqual(result, expected_error.serialize())

    def test_validate_frame_defect_clock_reference_spread_possible_no_position_required(self):
        defect_scores_array = []
        script_dir = os.path.dirname(__file__)
        csv_path = os.path.join(script_dir, "../resources", "defect_scores.csv")
        with open(csv_path, mode="r", encoding="utf-8") as file:
            csv_reader = csv.DictReader(file)
            for row in csv_reader:
                defect_scores_array.append(DefectScores(**row))

        defect_scores = defect_scores_array[0]
        defect_scores.clock_position_required = False
        defect_scores.clock_spread_possible = True

        """
        clock_at is not required and clock_to is  
        """
        video_frame = VideoFrames(
            id=3331,
            frame_id=1,
            at_clock=None,
            to_clock=2,
            defect_scores=defect_scores,
            chainage=0.0,
        )

        errors = video_frame.validate()

        expected_error = CustomValidationError(
            title="Clock Start",
            message="'Clock Start' value missing for frame at chainage {}".format(video_frame.chainage),
            field_name="atClock",
            entity="defect",
            metadata={"frame_id": video_frame.id},
        )
        self.assertEqual(errors[0], expected_error.serialize())

    def test_validate_frame_defect_clock_reference_position_required_no_spread(self):
        defect_scores_array = []
        script_dir = os.path.dirname(__file__)
        csv_path = os.path.join(script_dir, "../resources", "defect_scores.csv")
        with open(csv_path, mode="r", encoding="utf-8") as file:
            csv_reader = csv.DictReader(file)
            for row in csv_reader:
                defect_scores_array.append(DefectScores(**row))

        defect_scores = defect_scores_array[1]
        defect_scores.clock_position_required = True
        defect_scores.clock_spread_possible = False

        """
        clock_at is required and clock_to is not  
        """
        video_frame = VideoFrames(
            id=3331,
            frame_id=1,
            at_clock=None,
            to_clock=2,
            defect_scores=defect_scores,
            chainage=0.0,
        )

        errors = video_frame.validate()

        expected_error = CustomValidationError(
            title="Clock Start",
            message="'Clock Start' value missing for frame at chainage {}".format(video_frame.chainage),
            field_name="atClock",
            entity="defect",
            metadata={"frame_id": video_frame.id},
        )
        self.assertEqual(errors[0], expected_error.serialize())

    def test_validate_frame_defect_clock_reference_empty_position_required_and_spread(self):
        defect_scores_array = []
        script_dir = os.path.dirname(__file__)
        csv_path = os.path.join(script_dir, "../resources", "defect_scores.csv")
        with open(csv_path, mode="r", encoding="utf-8") as file:
            csv_reader = csv.DictReader(file)
            for row in csv_reader:
                defect_scores_array.append(DefectScores(**row))

        defect_scores = defect_scores_array[2]
        defect_scores.clock_position_required = True
        defect_scores.clock_spread_possible = True

        """
        clock_at is required and clock_to is too  
        """
        video_frame = VideoFrames(
            id=3331,
            frame_id=1,
            at_clock=None,
            to_clock=2,
            defect_scores=defect_scores,
            chainage=0.0,
            quantity1_units="%",
            quantity1_value=10,
        )

        errors = video_frame.validate()

        expected_error = CustomValidationError(
            title="Clock Start",
            message="'Clock Start' value missing for frame at chainage {}".format(video_frame.chainage),
            field_name="atClock",
            entity="defect",
            metadata={"frame_id": video_frame.id},
        )
        self.assertEqual(errors[0], expected_error.serialize())

    def test_validate_frame_defect_clock_reference_no_spread_with_position(self):
        defect_scores_array = []
        script_dir = os.path.dirname(__file__)
        csv_path = os.path.join(script_dir, "../resources", "defect_scores.csv")
        with open(csv_path, mode="r", encoding="utf-8") as file:
            csv_reader = csv.DictReader(file)
            for row in csv_reader:
                defect_scores_array.append(DefectScores(**row))

        defect_scores = defect_scores_array[3]
        defect_scores.clock_position_required = False
        defect_scores.clock_spread_possible = False

        """
        clock_at is not required and spread not possible 
        """
        video_frame = VideoFrames(
            id=3331,
            frame_id=1,
            at_clock=1,
            to_clock=2,
            defect_scores=defect_scores,
            chainage=0.0,
            quantity1_units="%",
            quantity1_value=10,
        )

        errors = video_frame.validate()

        expected_error = CustomValidationError(
            title="Clock End",
            message="'Clock End' value should be empty for frame at chainage {}".format(video_frame.chainage),
            field_name="toClock",
            entity="defect",
            metadata={"frame_id": video_frame.id},
        )
        self.assertEqual(errors[0], expected_error.serialize())
