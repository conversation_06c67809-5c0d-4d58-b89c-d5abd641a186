import pytest
from django.test.client import Client
from rest_framework import status
from vapar.constants.exports import ExportStatus, ExportType, ExportFormat

from api.tests import factory
from api.common.enums import StatusEnum
from api.defects.models import Standard
from api.exports.models import Export, ExportOutput
from api.inspections.models import InspectionValue, MapPointList, Inspection
from api.organisations.models import Organisations
from api.tests.settings import GenericExportSettings
from api.users.models import CustomUser


pytestmark = pytest.mark.django_db


@pytest.fixture(autouse=True)
def patch_enqueue_message(monkeypatch):
    def mock_enqueue_message(*args, **kwargs):
        pass

    monkeypatch.setattr("api.exports.views.enqueue_export_message", mock_enqueue_message)
    monkeypatch.setattr("api.exports.sync.enqueue_export_message", mock_enqueue_message)


@pytest.fixture(autouse=True)
def patch_get_blob_url_with_sas_token(monkeypatch):
    def mock_get_blob_url(blob_path, *args, **kwargs):
        return f"https://mock-blob-url.com/{blob_path}"

    monkeypatch.setattr("api.external.views.get_platform_blob_url_with_sas", mock_get_blob_url)


@pytest.fixture
def disable_sync_export_sleep(settings):
    settings.EXTERNAL_INSPECTIONS_XML_VIEW_TIMEOUT_SECS = 0
    settings.DISABLE_SYNC_EXPORT_SLEEP = True


@pytest.fixture(autouse=True)
def patch_get_storage_sas_token(monkeypatch):
    def mock_get_storage_sas_token(*args, **kwargs):
        return "mock-sas-token"

    monkeypatch.setattr("api.exports.serializers.get_platform_blob_url_with_sas", mock_get_storage_sas_token)

@pytest.fixture
def inspection(single_inspection, standard_user):
    single_inspection.asset.organisation = standard_user.organisation
    single_inspection.asset.save()
    single_inspection.file.target_org = standard_user.organisation
    single_inspection.file.save()
    single_inspection.folder = single_inspection.file.job_tree
    single_inspection.folder.primary_org = standard_user.organisation
    single_inspection.folder.save()

    single_inspection.status = StatusEnum.REVIEWED.value
    single_inspection.save()

    return single_inspection


@pytest.mark.parametrize(
    ("msg", "request_body"),
    [
        (
            "Bulk Inspection PDF",
            {
                "payload": {
                    "type": "BI",
                    "format": "PDF",
                    "inspectionIds": [],
                    "runValidation": False,
                }
            },
        ),
        (
            "Bulk Inspection ZIP",
            {
                "payload": {
                    "type": "BI",
                    "format": "ZIP",
                    "inspectionIds": [],
                    "runValidation": False,
                }
            },
        ),
        (
            "Asset XML",
            {
                "payload": {
                    "type": "AS",
                    "format": "XML",
                    "inspectionIds": [],
                    "runValidation": False,
                }
            },
        ),
        (
            "Asset CSV",
            {
                "payload": {
                    "type": "AS",
                    "format": "CSV",
                    "inspectionIds": [],
                    "runValidation": False,
                }
            },
        ),
        (
            "Defect XML",
            {
                "payload": {
                    "type": "DF",
                    "format": "XML",
                    "inspectionIds": [],
                    "runValidation": False,
                }
            },
        ),
        (
            "Defect CSV",
            {
                "payload": {
                    "type": "DF",
                    "format": "CSV",
                    "inspectionIds": [],
                    "runValidation": False,
                }
            },
        ),
        (
            "Inspection Plan XML",
            {
                "payload": {
                    "type": "IP",
                    "format": "XML",
                    "inspectionIds": [],
                    "runValidation": False,
                }
            },
        ),
        (
            "Inspection Plan CSV",
            {
                "payload": {
                    "type": "IP",
                    "format": "CSV",
                    "inspectionIds": [],
                    "runValidation": False,
                }
            },
        ),
        (
            "Predictor XML",
            {
                "payload": {
                    "type": "PR",
                    "format": "XML",
                    "inspectionIds": [],
                    "runValidation": False,
                }
            },
        ),
        (
            "Predictor CSV",
            {
                "payload": {
                    "type": "PR",
                    "format": "CSV",
                    "inspectionIds": [],
                    "runValidation": False,
                }
            },
        ),
        (
            "PACP7 MDB",
            {
                "payload": {
                    "type": "PM",
                    "format": "MDB",
                    "inspectionIds": [],
                    "runValidation": False,
                }
            },
        ),
        (
            "InfoAsset CSV",
            {
                "payload": {
                    "type": "IA",
                    "format": "CSV",
                    "inspectionIds": [],
                    "runValidation": False,
                    "infoAssetExportType": "DEFECT",
                }
            },
        ),
    ],
)
def test_exports_create_valid_payload(
    msg: str,
    request_body: dict,
    generic_export_settings: GenericExportSettings,
    standard_user: CustomUser,
    inspection: Inspection,
    client: Client,
):
    client.force_login(standard_user)

    request_body["payload"]["inspectionIds"] = [inspection.uuid]

    response = client.post(
        generic_export_settings.export_create_url,
        data=request_body,
        content_type="application/json",
    )
    assert response.status_code == status.HTTP_201_CREATED, f"Expected 201 for {msg}, got {response.status_code}"

    res_data = response.json()
    payload = request_body["payload"]
    payload["inspectionIds"] = [str(i) for i in payload["inspectionIds"]]

    assert res_data["targetOrg"] == standard_user.organisation.id
    assert res_data["type"] == payload["type"]
    assert res_data["format"] == payload["format"]
    assert res_data["status"] == ExportStatus.PENDING
    assert res_data["createdBy"] == standard_user.id
    assert res_data["fileDisplayNames"] == []

    assert standard_user.organisation.exports.count() == 1
    export = standard_user.organisation.exports.first()

    assert export.payload == payload
    assert export.status == ExportStatus.PENDING
    assert export.created_by == standard_user
    assert export.target_org == standard_user.organisation
    assert str(export.id) == res_data["id"]


@pytest.mark.parametrize(
    ("msg", "request_body"),
    [
        (
            "Nonexistent type",
            {
                "payload": {
                    "type": "XX",
                    "format": "CSV",
                    "inspectionIds": [],
                }
            },
        ),
        (
            "Nonexistent format",
            {
                "payload": {
                    "type": "BI",
                    "format": "XX",
                    "inspectionIds": [],
                }
            },
        ),
        (
            "Missing format",
            {
                "payload": {
                    "type": "BI",
                    "inspectionIds": [],
                }
            },
        ),
        (
            "Wrong format for type",
            {
                "payload": {
                    "type": "BI",
                    "format": "CSV",
                    "inspectionIds": [],
                }
            },
        ),
    ],
)
def test_exports_create_invalid_payload(
    msg: str,
    request_body: dict,
    generic_export_settings: GenericExportSettings,
    standard_user: CustomUser,
    client: Client,
):
    client.force_login(standard_user)

    response = client.post(
        generic_export_settings.export_create_url,
        data=request_body,
        content_type="application/json",
    )
    assert response.status_code == status.HTTP_400_BAD_REQUEST, f"Expected 400 for {msg}, got {response.status_code}"
    assert standard_user.organisation.exports.count() == 0


@pytest.mark.parametrize(
    ("msg", "request_body"),
    [
        (
            "Bulk Inspection PDF",
            {
                "payload": {
                    "type": "BI",
                    "format": "PDF",
                    "inspectionIds": [],
                    "runValidation": False,
                }
            },
        ),
        (
            "Bulk Inspection ZIP",
            {
                "payload": {
                    "type": "BI",
                    "format": "ZIP",
                    "inspectionIds": [],
                    "runValidation": False,
                }
            },
        ),
        (
            "Asset XML",
            {
                "payload": {
                    "type": "AS",
                    "format": "XML",
                    "inspectionIds": [],
                    "runValidation": False,
                }
            },
        ),
        (
            "Asset CSV",
            {
                "payload": {
                    "type": "AS",
                    "format": "CSV",
                    "inspectionIds": [],
                    "runValidation": False,
                }
            },
        ),
        (
            "Defect XML",
            {
                "payload": {
                    "type": "DF",
                    "format": "XML",
                    "inspectionIds": [],
                    "runValidation": False,
                }
            },
        ),
        (
            "Defect CSV",
            {
                "payload": {
                    "type": "DF",
                    "format": "CSV",
                    "inspectionIds": [],
                    "runValidation": False,
                }
            },
        ),
        (
            "Inspection Plan XML",
            {
                "payload": {
                    "type": "IP",
                    "format": "XML",
                    "inspectionIds": [],
                    "runValidation": False,
                }
            },
        ),
        (
            "Inspection Plan CSV",
            {
                "payload": {
                    "type": "IP",
                    "format": "CSV",
                    "inspectionIds": [],
                    "runValidation": False,
                }
            },
        ),
        (
            "Predictor XML",
            {
                "payload": {
                    "type": "PR",
                    "format": "XML",
                    "inspectionIds": [],
                    "runValidation": False,
                }
            },
        ),
        (
            "Predictor CSV",
            {
                "payload": {
                    "type": "PR",
                    "format": "CSV",
                    "inspectionIds": [],
                    "runValidation": False,
                }
            },
        ),
        (
            "PACP7 MDB",
            {
                "payload": {
                    "type": "PM",
                    "format": "MDB",
                    "inspectionIds": [],
                    "runValidation": False,
                }
            },
        ),
        (
            "InfoAsset CSV",
            {
                "payload": {
                    "type": "IA",
                    "format": "CSV",
                    "inspectionIds": [],
                    "runValidation": False,
                    "infoAssetExportType": "DEFECT",
                }
            },
        ),
    ],
)
def test_exports_create_uploaded_status_without_access(
    msg: str,
    request_body: dict,
    generic_export_settings: GenericExportSettings,
    standard_user: CustomUser,
    inspection: Inspection,
    client: Client,
):
    client.force_login(standard_user)

    inspection.status = StatusEnum.UPLOADED.value
    inspection.save()
    inspection.file.upload_org = Organisations.objects.get(id=1)
    inspection.file.save()

    request_body["payload"]["inspectionIds"] = [inspection.uuid]

    response = client.post(
        generic_export_settings.export_create_url,
        data=request_body,
        content_type="application/json",
    )
    assert response.status_code == status.HTTP_403_FORBIDDEN, f"Expected 403 for {msg}, got {response.status_code}"


@pytest.fixture
def pending_export(standard_user):
    return Export.objects.create(
        type=ExportType.BULK_INSPECTION_PDF,
        format=ExportFormat.PDF,
        status=ExportStatus.PENDING,
        target_org=standard_user.organisation,
        created_by=standard_user,
        payload={
            "type": "BI",
            "format": "PDF",
            "inspectionIds": ["1fe3b1b4-0b1b-4b1b-8b1b-1b1b1b1b1b1b"],
            "runValidation": False,
        },
    )


def test_exports_retrieve_nonexistent(
    generic_export_settings: GenericExportSettings,
    standard_user: CustomUser,
    client: Client,
):
    client.force_login(standard_user)

    response = client.get(
        generic_export_settings.get_export_retrieve_update_url("a4e3b1b4-0b1b-4b1b-8b1b-1b1b1b1b1b1b"),
    )
    assert response.status_code == status.HTTP_404_NOT_FOUND


def test_exports_retrieve(
    generic_export_settings: GenericExportSettings,
    standard_user: CustomUser,
    pending_export: Export,
    client: Client,
):
    client.force_login(standard_user)

    response = client.get(
        generic_export_settings.get_export_retrieve_update_url(pending_export.id),
    )
    assert response.status_code == status.HTTP_200_OK

    res_data = response.json()
    assert res_data["id"] == str(pending_export.id)
    assert res_data["targetOrg"] == standard_user.organisation.id
    assert res_data["type"] == "BI"
    assert res_data["format"] == "PDF"
    assert res_data["status"] == ExportStatus.PENDING
    assert res_data["createdBy"] == standard_user.id
    assert res_data["completedAt"] is None
    assert res_data["fileDisplayNames"] == []


def test_exports_retrieve_with_outputs(
    generic_export_settings: GenericExportSettings,
    standard_user: CustomUser,
    pending_export: Export,
    client: Client,
):
    output_obj = pending_export.outputs.create(
        filename="1b1752aa-914f-477c-b3d2-b26dbe4bc941.pdf",
        blob_url="export-outputs/1b1752aa-914f-477c-b3d2-b26dbe4bc941.pdf",
        extension=".pdf",
        mime_type="application/pdf",
        file_display_name="A Useful File Name.pdf",
        file_size=999,
    )

    client.force_login(standard_user)

    response = client.get(
        generic_export_settings.get_export_retrieve_update_url(pending_export.id),
    )
    assert response.status_code == status.HTTP_200_OK

    res_data = response.json()
    assert res_data["id"] == str(pending_export.id)
    assert res_data["targetOrg"] == standard_user.organisation.id
    assert res_data["type"] == "BI"
    assert res_data["format"] == "PDF"
    assert res_data["status"] == ExportStatus.PENDING
    assert res_data["createdBy"] == standard_user.id
    assert res_data["completedAt"] is None
    assert res_data["fileDisplayNames"] == [output_obj.file_display_name], "Output file display name should be included"


def test_exports_retrieve_wrong_org(
    generic_export_settings: GenericExportSettings,
    standard_user: CustomUser,
    client: Client,
    pending_export: Export,
):
    client.force_login(standard_user)

    # Reassign the export to another organisation
    another_org = Organisations.objects.create(full_name="another_test_org", standard_key=Standard.objects.first())
    pending_export.target_org = another_org
    pending_export.save()

    response = client.get(
        generic_export_settings.get_export_retrieve_update_url(pending_export.id),
    )
    assert response.status_code == status.HTTP_404_NOT_FOUND


def test_exports_payload_retrieve_not_found(
    generic_export_settings: GenericExportSettings,
    standard_user: CustomUser,
    pending_export: Export,
    client: Client,
):
    client.force_login(standard_user)

    response = client.get(
        generic_export_settings.get_exports_payload_retrieve_url("a4e3b1b4-0b1b-4b1b-8b1b-1b1b1b1b1b1b")
    )
    assert response.status_code == status.HTTP_404_NOT_FOUND


def test_exports_payload_retrieve(
    generic_export_settings: GenericExportSettings,
    standard_user: CustomUser,
    pending_export: Export,
    client: Client,
):
    client.force_login(standard_user)

    response = client.get(
        generic_export_settings.get_exports_payload_retrieve_url(pending_export.id),
    )
    assert response.status_code == status.HTTP_200_OK

    res_data = response.json()
    assert res_data["type"] == "BI"
    assert res_data["format"] == "PDF"
    assert res_data["inspectionIds"] == ["1fe3b1b4-0b1b-4b1b-8b1b-1b1b1b1b1b1b"]


def test_exports_payload_retrieve_wrong_org(
    generic_export_settings: GenericExportSettings,
    standard_user: CustomUser,
    pending_export: Export,
    client: Client,
):
    client.force_login(standard_user)

    # Reassign the export to another organisation
    another_org = Organisations.objects.create(full_name="another_test_org", standard_key=Standard.objects.first())
    pending_export.target_org = another_org
    pending_export.save()

    response = client.get(
        generic_export_settings.get_exports_payload_retrieve_url(pending_export.id),
    )
    assert response.status_code == status.HTTP_404_NOT_FOUND


def test_exports_patch_valid(
    generic_export_settings: GenericExportSettings,
    standard_user: CustomUser,
    pending_export: Export,
    client: Client,
):
    client.force_login(standard_user)

    response = client.patch(
        path=generic_export_settings.get_export_retrieve_update_url(pending_export.id),
        data={
            "status": "FA",
            "status_reason": "GE",
        },
        content_type="application/json",
    )
    assert response.status_code == status.HTTP_200_OK

    patched_export = Export.objects.filter(id=pending_export.id).first()
    assert patched_export.status == "FA"
    assert patched_export.status_reason == "GE"


def test_exports_patch_invalid(
    generic_export_settings: GenericExportSettings,
    standard_user: CustomUser,
    pending_export: Export,
    client: Client,
):
    client.force_login(standard_user)

    response = client.patch(
        path=generic_export_settings.get_export_retrieve_update_url(pending_export.id),
        data={
            "status": "An invalid status",
            "status_reason": "An invalid status reason",
        },
        content_type="application/json",
    )
    assert response.status_code == status.HTTP_400_BAD_REQUEST


def test_export_outputs_create_valid(
    generic_export_settings: GenericExportSettings,
    service_user: CustomUser,
    pending_export: Export,
    client: Client,
):
    client.force_login(service_user)
    service_user.organisation = pending_export.target_org
    service_user.save()

    data_json = {
        "filename": "1b1752aa-914f-477c-b3d2-b26dbe4bc941.pdf",
        "blobUrl": "export-outputs/1b1752aa-914f-477c-b3d2-b26dbe4bc941.pdf",
        "extension": ".pdf",
        "mimeType": "application/pdf",
        "fileSize": 999,
        "fileDisplayName": "A Useful File Name.pdf",
    }

    response = client.post(
        path=generic_export_settings.get_export_output_list_create_url(pending_export.id),
        data=data_json,
        content_type="application/json",
    )
    assert response.status_code == status.HTTP_201_CREATED

    created_eo = response.json()

    # Check the response
    assert created_eo["filename"] == data_json["filename"]
    assert created_eo["blobUrl"] == data_json["blobUrl"]
    assert created_eo["extension"] == data_json["extension"]
    assert created_eo["mimeType"] == data_json["mimeType"]
    assert created_eo["fileSize"] == data_json["fileSize"]
    assert created_eo["fileDisplayName"] == data_json["fileDisplayName"]

    # Check the created record
    assert pending_export.outputs.count() == 1
    record = pending_export.outputs.first()
    assert record.filename == data_json["filename"]
    assert record.blob_url == data_json["blobUrl"]
    assert record.extension == data_json["extension"]
    assert record.mime_type == data_json["mimeType"]
    assert record.file_size == data_json["fileSize"]
    assert record.file_display_name == data_json["fileDisplayName"]


def test_export_outputs_create_invalid(
    generic_export_settings: GenericExportSettings,
    service_user: CustomUser,
    pending_export: Export,
    client: Client,
):
    client.force_login(service_user)
    service_user.organisation = pending_export.target_org
    service_user.save()

    data_json = {
        "filename": "1b1752aa-914f-477c-b3d2-b26dbe4bc941",
        "extension": "pdf",
        "mimeType": "pdf",
        "fileSize": 999,
    }

    response = client.post(
        path=generic_export_settings.get_export_output_list_create_url(pending_export.id),
        data=data_json,
        content_type="application/json",
    )
    assert response.status_code == status.HTTP_400_BAD_REQUEST


def test_export_outputs_list_valid(
    generic_export_settings: GenericExportSettings,
    standard_user: CustomUser,
    pending_export: Export,
    client: Client,
):
    client.force_login(standard_user)

    obj = ExportOutput.objects.create(
        export=pending_export,
        filename="1b1752aa-914f-477c-b3d2-b26dbe4bc942.pdf",
        blob_url="export-outputs/1b1752aa-914f-477c-b3d2-b26dbe4bc942.pdf",
        extension=".pdf",
        mime_type="application/pdf",
        file_size=999,
    )

    response = client.get(
        path=generic_export_settings.get_export_output_list_create_url(pending_export.id),
    )

    assert response.status_code == status.HTTP_200_OK

    eo_list = response.json()
    assert eo_list["count"] == 1

    eo = eo_list["results"][0]
    assert eo["export"] == str(obj.export.id)
    assert eo["filename"] == obj.filename
    assert eo["blobUrl"] == obj.blob_url
    assert eo["extension"] == obj.extension
    assert eo["mimeType"] == obj.mime_type
    assert eo["fileSize"] == obj.file_size


def test_export_outputs_list_invalid(
    generic_export_settings: GenericExportSettings,
    standard_user: CustomUser,
    pending_export: Export,
    client: Client,
):
    client.force_login(standard_user)

    response = client.get(
        path=generic_export_settings.get_export_output_list_create_url("1a1a1a1a-1a1a-1a1a-1a1a-1a1a1a1a1a1a"),
        content_type="application/json",
    )

    assert response.status_code == status.HTTP_404_NOT_FOUND


@pytest.fixture
def multiple_exports(standard_user: CustomUser):
    common = {
        "type": "BI",
        "format": "PDF",
        "target_org": standard_user.organisation,
        "created_by": standard_user,
        "payload": {
            "type": "BI",
            "format": "PDF",
            "inspectionIds": ["1fe3b1b4-0b1b-4b1b-8b1b-1b1b1b1b1b1b"],
            "runValidation": False,
        },
    }
    return [
        Export.objects.create(
            **common,
            status=ExportStatus.PENDING,
        ),
        Export.objects.create(
            **common,
            status=ExportStatus.FAILED,
        ),
        Export.objects.create(
            **common,
            status=ExportStatus.COMPLETED,
        ),
        Export.objects.create(
            **common,
            status=ExportStatus.COMPLETED,
        ),
        Export.objects.create(
            **common,
            status=ExportStatus.COMPLETED,
            is_initiated_by_user=False,
        ),
    ]


def test_exports_list_status_counts(
    generic_export_settings: GenericExportSettings,
    standard_user: CustomUser,
    client: Client,
    multiple_exports,
):
    client.force_login(standard_user)

    response = client.get(
        generic_export_settings.export_list_url,
    )
    assert response.status_code == status.HTTP_200_OK
    res_data = response.json()

    assert res_data["count"] == 4
    assert res_data["statusCounts"]["PE"] == 1
    assert res_data["statusCounts"]["FA"] == 1
    assert res_data["statusCounts"]["CO"] == 2
    assert res_data["statusCounts"]["PR"] == 0


def test_export_list_hidden(
    generic_export_settings: GenericExportSettings,
    standard_user: CustomUser,
    client: Client,
    multiple_exports,
):
    client.force_login(standard_user)

    multiple_exports[0].is_hidden = True
    multiple_exports[0].save()

    response = client.get(
        generic_export_settings.export_list_url,
    )
    assert response.status_code == status.HTTP_200_OK
    res_data = response.json()

    assert res_data["count"] == 3, "Hidden exports should not be returned"
    assert res_data["statusCounts"]["PE"] == 0, "Pending export should be hidden"
    assert res_data["statusCounts"]["FA"] == 1
    assert res_data["statusCounts"]["CO"] == 2
    assert res_data["statusCounts"]["PR"] == 0


def test_export_hide(
    generic_export_settings: GenericExportSettings,
    standard_user: CustomUser,
    client: Client,
    multiple_exports,
):
    client.force_login(standard_user)

    response = client.patch(
        generic_export_settings.get_export_retrieve_update_url(multiple_exports[0].id),
        data={"isHidden": True},
        content_type="application/json",
    )
    assert response.status_code == status.HTTP_200_OK

    assert Export.objects.filter(is_hidden=False, is_initiated_by_user=True).count() == 3
    assert Export.objects.filter(is_hidden=True, id=multiple_exports[0].id).count() == 1


@pytest.mark.parametrize("validates", [True, False])
def test_exports_validation(
    client: Client,
    generic_export_settings: GenericExportSettings,
    standard_user: CustomUser,
    validates,
):
    client.force_login(standard_user)
    asset = factory.create_assets(org=standard_user.organisation)[0]
    insp = factory.create_bulk_inspections(asset)[0]

    if not validates:
        # create validation errors
        inspection_values = InspectionValue.objects.filter(inspection_id=insp["inspection_id"])
        for iv in inspection_values:
            iv.value = ""
            iv.save()

    response = client.post(
        generic_export_settings.export_create_url,
        data={
            "payload": {
                "type": "DF",
                "format": "XML",
                "inspectionIds": [insp["inspection_id"]],
                "runValidation": True,
            }
        },
        content_type="application/json",
    )
    expected_status_code = status.HTTP_201_CREATED if validates else status.HTTP_412_PRECONDITION_FAILED
    assert response.status_code == expected_status_code


def test_exports_validation_without_mpl(
    client: Client,
    generic_export_settings: GenericExportSettings,
    standard_user: CustomUser,
):
    client.force_login(standard_user)
    asset = factory.create_assets(org=standard_user.organisation)[0]
    insp = factory.create_bulk_inspections(asset)[0]
    MapPointList.objects.filter(inspection=insp["inspection_id"]).delete()

    response = client.post(
        generic_export_settings.export_create_url,
        data={
            "payload": {
                "type": "DF",
                "format": "XML",
                "inspectionIds": [insp["inspection_id"]],
                "runValidation": True,
            }
        },
        content_type="application/json",
    )
    assert response.status_code == status.HTTP_201_CREATED, "Should succeed"


def test_exports_pacp8_validation(
    client,
    generic_export_settings,
    standard_user,
):
    client.force_login(standard_user)
    asset = factory.create_assets(org=standard_user.organisation)[0]

    pacp = Standard.objects.get(name="PACP8")
    insp = factory.create_bulk_inspections(asset)[0]
    inspection = Inspection.objects.get(uuid=insp["inspection_id"])
    inspection.folder.standard_key = pacp
    inspection.folder.save()

    response = client.post(
        generic_export_settings.export_create_url,
        data={
            "payload": {
                "type": "PM",
                "format": "MDB",
                "inspectionIds": [insp["inspection_id"]],
                "runValidation": True,
            }
        },
        content_type="application/json",
    )
    # TODO: Improve tests for PACP8 validation; find/build examples that should pass validation
    assert response.status_code == status.HTTP_412_PRECONDITION_FAILED, "Check this can at least run to completion"


def test_exports_bulk_update_status_of_pending(client, generic_export_settings, multiple_exports, service_user_key):
    response = client.patch(
        generic_export_settings.export_bulk_update_status_url + "?status=PE",
        data={
            "status": "FA",
            "status_reason": "GE",
        },
        content_type="application/json",
        HTTP_X_API_KEY=service_user_key,
    )
    assert response.status_code == status.HTTP_204_NO_CONTENT

    assert Export.objects.filter(status=ExportStatus.FAILED).count() == 2, "One export was already failed"
    assert Export.objects.filter(status=ExportStatus.COMPLETED).count() == 3, "Completed exports were not changed"
    assert Export.objects.filter(status=ExportStatus.PENDING).count() == 0, "Pending exports were moved to failed"


def test_exports_bulk_update_status_multiple_filters(
    client, generic_export_settings, multiple_exports, service_user_key
):
    Export.objects.filter(pk=multiple_exports[0].pk).update(updated_at="2020-01-01T00:00:00Z")
    Export.objects.filter(pk=multiple_exports[2].pk).update(updated_at="2020-01-01T00:00:00Z")  # Fulfils both filters

    response = client.patch(
        generic_export_settings.export_bulk_update_status_url + "?status=CO&updated_at__lte=2022-01-01T00:00:00Z",
        data={
            "status": "FA",
            "status_reason": "GE",
        },
        content_type="application/json",
        HTTP_X_API_KEY=service_user_key,
    )
    assert response.status_code == status.HTTP_204_NO_CONTENT

    assert Export.objects.filter(status=ExportStatus.FAILED).count() == 2, "One moved to failed"
    assert Export.objects.filter(status=ExportStatus.COMPLETED).count() == 2, "Two completed exports were not changed"
    assert Export.objects.filter(status=ExportStatus.PENDING).count() == 1, "Pending export was unchanged"
