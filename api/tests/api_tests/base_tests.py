import pytest

from django.test import Client
from rest_framework import status

from api.users.models import CustomUser

pytestmark = [pytest.mark.django_db(databases=["default"])]


class HeaderTests:
    """
    Tests for the Headers API

    1. GET /headers
    2. GET /headers/filterable
    """

    client = Client()
    product_owner: CustomUser = None
    standard_user: CustomUser = None

    @pytest.fixture(autouse=True)
    def setup(self, product_owner, standard_user):
        self.product_owner = product_owner
        self.standard_user = standard_user
        self.client.force_login(user=self.product_owner)

    def test_get_headers_as_product_owner(self, header_settings):
        """
        Test that a product owner can fetch a list of headers
        """
        response = self.client.get(path=header_settings.header_list_url)
        assert response.status_code == status.HTTP_200_OK

    def test_get_headers_as_standard_user(self, header_settings):
        """
        Test that a standard user can fetch a list of headers
        """
        self.client.force_login(user=self.standard_user)
        response = self.client.get(path=header_settings.header_list_url)
        assert response.status_code == status.HTTP_200_OK

    def test_get_header_by_name(self, header_settings):
        """
        Test that a header can be retrieved via its name
        """
        response = self.client.get(path=f"{header_settings.header_list_url}?name=Direction")
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data["count"] == 1
        assert response_data["results"][0]["name"] == "Direction"
