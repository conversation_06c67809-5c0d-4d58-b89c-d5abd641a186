import pytest

from django.test.client import Client
from rest_framework import status

from api.users.models import CustomUser
from api.organisations.models import Organisations
from api.inspections.models import InspectionFilter

pytestmark = [pytest.mark.django_db(databases=["default"])]


class UserTests:
    """
    Tests for the User model
    """

    client: Client = Client()
    product_owner: CustomUser = None
    standard_user: CustomUser = None
    asset_owner: Organisations = None

    @pytest.fixture(autouse=True)
    def setup_method(self, product_owner, standard_user, asset_owner_org):
        self.product_owner = product_owner
        self.standard_user = standard_user
        self.client.force_login(user=self.product_owner)
        self.asset_owner = asset_owner_org

    def test_get_user_by_id_as_product_owner(self, user_settings):
        """
        Test that we can retrieve a user object given an id
        """
        user_id = self.standard_user.id
        response = self.client.get(path=user_settings.user_url.format(id=user_id))
        assert response.status_code == status.HTTP_200_OK

    def test_get_user_by_id_as_standard_user(self, user_settings):
        """
        Test that a standard user can retrieve a user object given an id
        """
        self.client.force_login(user=self.standard_user)

        response = self.client.get(path=user_settings.user_url.format(id=self.standard_user.id))

        assert response.status_code == status.HTTP_403_FORBIDDEN

    def test_get_user_list_as_product_owner(self, user_settings):
        """
        Test that a product owner can retrieve a list of users
        """
        response = self.client.get(
            path=user_settings.user_list_url.format(organisation=self.standard_user.organisation.id)
        )
        assert response.status_code == status.HTTP_200_OK

    def test_get_user_list_as_standard_user(self, user_settings):
        """
        Test that a standard user cannot retrieve a list of users
        """
        self.client.force_login(user=self.standard_user)

        response = self.client.get(
            path=user_settings.user_list_url.format(organisation=self.standard_user.organisation)
        )

        assert response.status_code == status.HTTP_403_FORBIDDEN

    def test_create_user_as_product_owner(self, user_settings):
        """
        Test that a product owner can create a new user
        """
        organisation_id = self.asset_owner.id

        data = {
            "email": "<EMAIL>",
            "first_name": "standard",
            "last_name": "user",
            "group": 2,
            "is_active": True,
            "is_staff": False,
            "user_level": "standard",
            "organisation_id": organisation_id,
            "password": "Test123!@#",
        }
        response = self.client.post(path=user_settings.user_base_url, data=data, content_type="application/json")
        assert response.status_code == status.HTTP_200_OK

        user = CustomUser.objects.get(email=data["email"])
        assert user

        user_id = user.id
        response = self.client.get(path=user_settings.user_url.format(id=user_id))
        assert response.status_code == status.HTTP_200_OK

        # check filter record exists
        inspection_filter = InspectionFilter.objects.filter(user=user, organisation_id=organisation_id).first()
        assert inspection_filter

    def test_create_user_as_standard_user(self, user_settings):
        """
        Test that a standard user cannot create a new user
        """
        self.client.force_login(user=self.standard_user)

        organisation_id = self.asset_owner.id

        data = {
            "email": "<EMAIL>",
            "first_name": "standard",
            "last_name": "user",
            "group": 2,
            "is_active": True,
            "is_staff": False,
            "user_level": "standard",
            "organisation_id": organisation_id,
            "password": "Test123!@#",
        }
        response = self.client.post(path=user_settings.user_base_url, data=data, content_type="application/json")
        assert response.status_code == status.HTTP_403_FORBIDDEN

    def test_user_sign_up(self, user_settings):
        """
        Test that a user can sign up
        """
        self.client.logout()

        data = {
            "email": "<EMAIL>",
            "firstName": "test",
            "group": 2,
            "isActive": False,
            "isAdmin": False,
            "isMember": True,
            "isStaff": False,
            "lastName": "register",
            "password": "^$7utW5Dx8Z!M3yKTA%a#Hgi",
        }

        response = self.client.post(user_settings.user_register_url, data=data, content_type="application/json")
        assert response.status_code == status.HTTP_200_OK

        # check the filter record exists
        user = CustomUser.objects.get(email=data["email"])
        assert user
        inspection_filter = InspectionFilter.objects.filter(user=user, organisation_id=user.organisation).first()
        assert inspection_filter
