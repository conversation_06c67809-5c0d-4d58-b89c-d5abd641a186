from io import BytesIO
from uuid import UUID, uuid4

import pandas as pd
import pytest
import xmltodict
from django.utils import timezone
from rest_framework import status
from rest_framework.test import APIClient
from vapar.constants.imports import ImportTypeEnum
from vapar.constants.imports import ImportValidationStatusEnum
from vapar.core.imports import ImportRequestQueueMessage
from vapar.core.imports import MSCC5DefectXMLInspectionImportPayload

from api.defects.models import Standard
from api.imports.models import Import, ImportFile
from api.inspections.models import FileList
from api.organisations.models import Organisations
from api.tests import factory
from api.tests.settings import GenericImportSettings
from api.users.models import CustomUser

pytestmark = pytest.mark.django_db


def create_import(
    *,
    org: Organisations,
    by: CustomUser | None = None,
    type: str = "AS",
    payload: dict | None = None,
) -> Import:
    payload = payload or {}
    return Import.objects.create(
        target_org=org,
        created_by=by,
        type=type,
        payload=payload,
    )


def create_import_file(
    *,
    import_obj: Import,
    by: CustomUser | None = None,
    original_filename: str = "file.csv",
    blob_url: str | None = None,
) -> ImportFile:
    if blob_url is None:
        uid = uuid4()
        blob_url = f"import-files/{uid}.csv"

    return import_obj.files.create(
        created_by=by,
        original_filename=original_filename,
        blob_url=blob_url,
        extension=".csv",
        mime_type="text/csv",
        file_size=100,
        validation_status="PA",
        validated_at="2021-01-01T00:00:00Z",
    )


def create_csv(data: list[dict], columns=None) -> BytesIO:
    csv_str = pd.DataFrame(data, columns).to_csv(index=False)
    return BytesIO(csv_str.encode("utf-8"))


def create_mscc5_xml(*, surveys: list[dict] | None = None, n: int = 1) -> BytesIO:
    """Factory to create test xml files for MSCC5 imports"""
    if surveys is None:
        surveys = [create_mscc5_survey() for _ in range(n)]
    root = {
        "SurveyGroup": {
            "Survey": surveys,
        }
    }
    as_str = xmltodict.unparse(root)
    return BytesIO(as_str.encode("utf-8"))


def create_mscc5_survey(
    *,
    pipeline_length_ref: str | None = None,
    height_diameter: str | None = None,
    material: str | None = None,
    upstream_node: str | None = None,
    downstream_node: str | None = None,
    direction: str | None = None,
    location_town: str | None = None,
    location_street: str | None = None,
    length_surveyed: str | None = None,
    date: str | None = None,
    observations: list[dict] | None = None,
) -> dict:
    """Factory to create a single MSCC5 survey / inspection for an XML file"""
    return {
        "Header": {
            "PipelineLengthRef": pipeline_length_ref or "TheAssetID",
            "HeightDiameter": height_diameter or "100",
            "Material": material or "C",
            "StartNodeRef": (upstream_node if direction == "D" else downstream_node) or "USNODE1",
            "FinishNodeRef": (downstream_node if direction == "D" else upstream_node) or "DSNODE2",
            "Direction": direction or "D",
            "LocationTown": location_town or "Town",
            "LocationStreet": location_street or "Street",
            "ExpectedLength": length_surveyed or "100",
            "Date": date or "2021-01-01",
        },
        "Observations": observations or [],
    }


@pytest.fixture(autouse=True)
def patch_put_to_platform_blob_storage(monkeypatch):
    def stub_fn(*args, **kwargs):
        pass

    monkeypatch.setattr("api.imports.views.put_to_platform_blob_storage", stub_fn)


@pytest.fixture(autouse=True)
def patch_enqueue_import(monkeypatch):
    enqueued = []

    def stub_fn(payload, org):
        enqueued.append((payload, org))

    monkeypatch.setattr("api.imports.views.enqueue_import_message", stub_fn)

    return enqueued


@pytest.fixture
def blank_file_record(asset_owner_org) -> FileList:
    org = asset_owner_org
    file_obj = FileList.objects.create(
        upload_org=org,
        target_org=org,
    )
    return file_obj


@pytest.fixture
def uploaded_file_record(asset_owner_org) -> FileList:
    org = asset_owner_org
    file_obj = FileList.objects.create(
        upload_org=org,
        target_org=org,
        url="uploadedvideofiles/test-123.mp4",
        file_size="123MB",
    )
    return file_obj


class TestImportListCreate:
    def test_create(self, client, standard_user, import_settings: GenericImportSettings):
        client.force_login(standard_user)
        res = client.post(
            import_settings.import_create_url,
            data={
                "type": "AS",
                "payload": {},
            },
            content_type="application/json",
        )
        assert res.status_code == status.HTTP_201_CREATED
        data = res.json()

        import_obj = Import.objects.get(id=data["id"])
        assert import_obj.type == "AS"
        assert import_obj.created_by == standard_user
        assert import_obj.target_org == standard_user.organisation
        assert import_obj.status == "UP", "Status should be UPLOADING when first created"
        assert import_obj.is_hidden is False

    def test_create_media_upload_blank_file(self, client, standard_user, blank_file_record, import_settings):
        client.force_login(standard_user)
        res = client.post(
            import_settings.import_create_url,
            data={
                "type": "IV",  # Inspection Video Media
                "payload": {"file_id": blank_file_record.id},
            },
            content_type="application/json",
        )
        assert (
            res.status_code == status.HTTP_400_BAD_REQUEST
        ), "File has not been uploaded yet, so should not be allowed"

    def test_create_media_upload_with_uploaded_file(
        self, client, standard_user, uploaded_file_record, import_settings, patch_enqueue_import
    ):
        client.force_login(standard_user)
        res = client.post(
            import_settings.import_create_url,
            data={
                "type": "IV",  # Inspection Video Media
                "payload": {"file_id": uploaded_file_record.id},
            },
            content_type="application/json",
        )
        assert res.status_code == status.HTTP_201_CREATED
        data = res.json()

        import_obj = Import.objects.get(id=data["id"])
        assert import_obj.type == "IV"
        assert import_obj.created_by == standard_user
        assert import_obj.target_org == standard_user.organisation
        assert import_obj.status == "PE", "Status should be PENDING since this type can be processed immediately"
        assert import_obj.validation_status == ImportValidationStatusEnum.PASSED, "Should be validated immediately"
        assert import_obj.is_hidden is False

        assert len(patch_enqueue_import) == 1, "Should have enqueued a message"
        msg, org = patch_enqueue_import[0]
        msg = ImportRequestQueueMessage.model_validate(msg)
        assert msg.import_id == import_obj.id
        assert msg.target_org_id == standard_user.organisation.id
        assert org == standard_user.organisation

    def test_list_empty(self, client, standard_user, import_settings: GenericImportSettings):
        client.force_login(standard_user)
        res = client.get(import_settings.import_list_url)
        assert res.status_code == status.HTTP_200_OK
        data = res.json()
        assert data["count"] == 0
        assert data["results"] == []

    def test_create_and_list(self, client, standard_user, import_settings: GenericImportSettings):
        client.force_login(standard_user)
        res = client.post(
            import_settings.import_create_url,
            data={
                "type": "AS",
                "payload": {},
            },
            content_type="application/json",
        )
        assert res.status_code == status.HTTP_201_CREATED
        data_1 = res.json()

        res = client.post(
            import_settings.import_create_url,
            data={
                "type": "AS",
                "payload": {},
            },
            content_type="application/json",
        )
        assert res.status_code == status.HTTP_201_CREATED
        data_2 = res.json()

        res = client.get(import_settings.import_list_url)
        assert res.status_code == status.HTTP_200_OK
        data = res.json()
        assert data["count"] == 2

        row_1 = next(x for x in data["results"] if x["id"] == str(data_1["id"]))
        assert row_1["type"] == "AS"
        assert row_1["createdBy"] == standard_user.id
        assert row_1["targetOrg"] == standard_user.organisation.id
        assert row_1["status"] == "UP"

        row_2 = next(x for x in data["results"] if x["id"] == str(data_2["id"]))
        assert row_2["type"] == "AS"
        assert row_2["createdBy"] == standard_user.id
        assert row_2["targetOrg"] == standard_user.organisation.id
        assert row_2["status"] == "UP"


class TestImportRetrieveUpdate:
    @pytest.fixture
    def import_obj(self, standard_user):
        return create_import(org=standard_user.organisation, by=standard_user, type="AS")

    def test_update_status(self, client, standard_user, import_settings: GenericImportSettings, import_obj: Import):
        client.force_login(standard_user)
        res = client.patch(
            import_settings.get_import_retrieve_update_url(import_obj.id),
            data={
                "status": "CO",
            },
            content_type="application/json",
        )
        assert res.status_code == status.HTTP_200_OK
        data = res.json()
        assert data["status"] == "CO"
        assert data["completedAt"] is not None, "completedAt should be set when status is changed to completed"

        import_obj.refresh_from_db()
        assert import_obj.status == "CO"
        assert import_obj.completed_at is not None

    def test_hide(self, client, standard_user, import_settings: GenericImportSettings, import_obj: Import):
        client.force_login(standard_user)
        res = client.patch(
            import_settings.get_import_retrieve_update_url(import_obj.id),
            data={
                "isHidden": True,
            },
            content_type="application/json",
        )
        assert res.status_code == status.HTTP_200_OK
        data = res.json()
        assert data["isHidden"] is True

        import_obj.refresh_from_db()
        assert import_obj.is_hidden is True

        res = client.get(import_settings.import_list_url)
        assert res.status_code == status.HTTP_200_OK
        data = res.json()
        assert data["count"] == 0
        assert data["results"] == [], "No longer visible in list view"


class TestAssetsImportFileUploadValidation:
    @pytest.fixture
    def asset_import(self, standard_user: CustomUser) -> Import:
        return create_import(org=standard_user.organisation, by=standard_user, type="AS")

    @pytest.fixture
    def client(self) -> APIClient:
        # Note: Using DRF api client rather than django's test client because that one seemingly doesn't encode form
        # data correctly
        return APIClient()

    def test_nonexistent_import(
        self, client: APIClient, standard_user: CustomUser, import_settings: GenericImportSettings
    ):
        client.force_login(standard_user)
        res = client.post(
            import_settings.get_import_file_upload_list_url(UUID("00000000-0000-0000-0000-000000000000")),
            data={},
            format="multipart",
        )
        assert res.status_code == status.HTTP_404_NOT_FOUND

    def test_empty_file(
        self, client: APIClient, standard_user: CustomUser, import_settings: GenericImportSettings, asset_import: Import
    ):
        client.force_login(standard_user)
        empty_file = BytesIO(b"")
        empty_file.name = "empty.csv"
        res = client.post(
            import_settings.get_import_file_upload_list_url(asset_import.id),
            data={"file": empty_file},
            format="multipart",
        )
        assert res.status_code == status.HTTP_400_BAD_REQUEST
        assert asset_import.files.count() == 0

    def test_malformed_csv(
        self, client: APIClient, standard_user: CustomUser, import_settings: GenericImportSettings, asset_import: Import
    ):
        client.force_login(standard_user)
        malformed_csv = BytesIO(",,\n,\n,,,".encode())
        malformed_csv.name = "malformed.csv"
        res = client.post(
            import_settings.get_import_file_upload_list_url(asset_import.id),
            data={"file": malformed_csv},
            format="multipart",
        )
        assert res.status_code == status.HTTP_400_BAD_REQUEST
        data = res.json()
        assert len(data["errors"]) > 0
        assert any(e["code"] == "UNPARSEABLE" for e in data["errors"])
        assert asset_import.files.count() == 0

    def test_missing_column(
        self, client: APIClient, standard_user: CustomUser, import_settings: GenericImportSettings, asset_import: Import
    ):
        client.force_login(standard_user)
        data = [
            {"UseOfDrainSewer": "Sewer"},
            {"UseOfDrainSewer": "Sewer"},
        ]
        csv_file = create_csv(data)
        csv_file.name = "missing_col.csv"
        res = client.post(
            import_settings.get_import_file_upload_list_url(asset_import.id),
            data={"file": csv_file},
            format="multipart",
        )
        assert res.status_code == status.HTTP_400_BAD_REQUEST
        data = res.json()
        assert len(data["errors"]) > 0
        assert any(e["code"] == "MISSING_COL" for e in data["errors"])
        assert asset_import.files.count() == 0

    def test_duplicates_in_file(
        self, client: APIClient, standard_user: CustomUser, import_settings: GenericImportSettings, asset_import: Import
    ):
        client.force_login(standard_user)
        data = [
            {"AssetID": "1"},
            {"AssetID": "1"},
        ]
        csv_file = create_csv(data)
        csv_file.name = "duplicates.csv"
        res = client.post(
            import_settings.get_import_file_upload_list_url(asset_import.id),
            data={"file": csv_file},
            format="multipart",
        )
        assert res.status_code == status.HTTP_400_BAD_REQUEST
        data = res.json()
        assert len(data["errors"]) > 0
        assert any(e["code"] == "DUPLICATES" and e["rowNumbers"] == [2, 3] for e in data["errors"])
        assert asset_import.files.count() == 0

    def test_bad_column_length(
        self, client: APIClient, standard_user: CustomUser, import_settings: GenericImportSettings, asset_import: Import
    ):
        client.force_login(standard_user)
        data = [
            {"AssetID": "1", "DownstreamNode": "A" * 1000},
        ]
        csv_file = create_csv(data)

        csv_file.name = "bad_column_length.csv"
        res = client.post(
            import_settings.get_import_file_upload_list_url(asset_import.id),
            data={"file": csv_file},
            format="multipart",
        )
        assert res.status_code == status.HTTP_400_BAD_REQUEST
        data = res.json()
        assert len(data["errors"]) > 0
        assert any(e["code"] == "BAD_VALUE" and e["rowNumbers"] == [2] for e in data["errors"])
        assert asset_import.files.count() == 0

    def test_invalid_pipe_type(
        self, client: APIClient, standard_user: CustomUser, import_settings: GenericImportSettings, asset_import: Import
    ):
        client.force_login(standard_user)
        data = [
            {"AssetID": "1", "UseOfDrainSewer": "Not a real pipe type"},  # 'D' for Drain or 'F' for Foul in MSCC5
        ]
        csv_file = create_csv(data)
        csv_file.name = "invalid_pipe_type.csv"
        res = client.post(
            import_settings.get_import_file_upload_list_url(asset_import.id),
            data={"file": csv_file},
            format="multipart",
        )
        assert res.status_code == status.HTTP_400_BAD_REQUEST
        data = res.json()
        assert len(data["errors"]) > 0
        assert any(e["code"] == "BAD_VALUE" and e["rowNumbers"] == [2] for e in data["errors"])
        assert asset_import.files.count() == 0

    def test_invalid_standard(
        self, client: APIClient, standard_user: CustomUser, import_settings: GenericImportSettings, asset_import: Import
    ):
        client.force_login(standard_user)
        data = [
            {"AssetID": "1", "StandardName": "Not a real standard"},
        ]
        csv_file = create_csv(data)
        csv_file.name = "invalid_standard.csv"
        res = client.post(
            import_settings.get_import_file_upload_list_url(asset_import.id),
            data={"file": csv_file},
            format="multipart",
        )
        assert res.status_code == status.HTTP_400_BAD_REQUEST
        data = res.json()
        assert len(data["errors"]) > 0
        assert any(
            e["code"] == "BAD_VALUE" and "StandardName" in e["description"] and e["rowNumbers"] == [2]
            for e in data["errors"]
        )
        assert asset_import.files.count() == 0

    @pytest.mark.parametrize(
        "csv_file",
        [
            create_csv(
                [
                    {"AssetID": "1", "UseOfDrainSewer": "C"},
                ]
            ),
            create_csv(
                [
                    {"AssetID": "LongerAssetID123"},
                    {"AssetID": "AnotherOne"},
                ]
            ),
            create_csv(
                [
                    {"AssetID": "1", "UseOfDrainSewer": "SS", "StandardName": "US V7"},
                    {"AssetID": "2", "UseOfDrainSewer": "SW", "StandardName": "US V7"},
                ]
            ),
            create_csv(
                [
                    {
                        "AssetID": "1",
                        "UseOfDrainSewer": "D",
                        "StandardName": "UK V5",
                        "UpstreamNode": "USNODE1",
                        "DownstreamNode": "USNODE2",
                        "HeightDiameter": "100",
                        "Material": "",  # Gets converted to N/A when loaded from csv by pandas
                    },
                    {
                        "AssetID": "2",
                        "UseOfDrainSewer": "D",
                        "StandardName": "",
                        "UpstreamNode": "",
                        "DownstreamNode": "USNODE4",
                        "HeightDiameter": "",
                        "Material": "",
                    },
                ]
            ),
        ],
    )
    def test_valid_asset_csv(
        self, client, standard_user, import_settings: GenericImportSettings, asset_import, csv_file
    ):
        client.force_login(standard_user)
        csv_file.name = "valid.csv"
        res = client.post(
            import_settings.get_import_file_upload_list_url(asset_import.id),
            data={"file": csv_file},
            format="multipart",
        )
        assert res.status_code == status.HTTP_201_CREATED
        data = res.json()

        assert asset_import.files.count() == 1
        import_file = asset_import.files.first()

        assert import_file.validation_status == "PA", "Should have successfully validated"
        assert import_file.validated_at is not None
        assert import_file.extension == ".csv"
        assert import_file.blob_url.startswith("import-files/")
        assert import_file.original_filename == "valid.csv"
        assert import_file.mime_type == "text/csv"

        asset_import.refresh_from_db()
        assert asset_import.validation_status == "PA"
        assert asset_import.validated_at is not None

        assert data["id"] == str(import_file.id)
        assert data["importOperation"] == str(asset_import.id)
        assert data["createdBy"] == standard_user.id
        assert data["validationStatus"] == "PA"
        assert data["originalFilename"] == "valid.csv"
        assert data["blobUrl"] == import_file.blob_url


class TestImportFileList:
    @pytest.fixture
    def asset_import(self, standard_user: CustomUser) -> Import:
        return create_import(org=standard_user.organisation, by=standard_user, type="AS")

    def test_nonexistent_import(
        self, client: APIClient, standard_user: CustomUser, import_settings: GenericImportSettings
    ):
        client.force_login(standard_user)
        res = client.get(
            import_settings.get_import_file_upload_list_url(UUID("00000000-0000-0000-0000-000000000000")),
        )
        assert res.status_code == status.HTTP_404_NOT_FOUND

    def test_no_files(
        self, client, standard_user: CustomUser, import_settings: GenericImportSettings, asset_import: Import
    ):
        client.force_login(standard_user)
        res = client.get(import_settings.get_import_file_upload_list_url(asset_import.id))
        assert res.status_code == status.HTTP_200_OK
        data = res.json()
        assert data["count"] == 0

    def test_list_files(
        self, client, standard_user: CustomUser, import_settings: GenericImportSettings, asset_import: Import
    ):
        client.force_login(standard_user)

        create_import_file(import_obj=asset_import, by=standard_user, original_filename="file1.csv")
        create_import_file(import_obj=asset_import, by=standard_user, original_filename="file2.csv")

        res = client.get(import_settings.get_import_file_upload_list_url(asset_import.id))
        assert res.status_code == status.HTTP_200_OK
        data = res.json()
        assert data["count"] == 2
        assert len(data["results"]) == 2

        file_1 = next(x for x in data["results"] if x["originalFilename"] == "file1.csv")
        assert file_1["importOperation"] == str(asset_import.id)
        assert file_1["createdBy"] == standard_user.id
        assert file_1["validationStatus"] == "PA"

        file_2 = next(x for x in data["results"] if x["originalFilename"] == "file2.csv")
        assert file_2["importOperation"] == str(asset_import.id)
        assert file_2["createdBy"] == standard_user.id
        assert file_2["validationStatus"] == "PA"


class TestMSCC5DefectXMLImportFileUploadValidation:
    @pytest.fixture
    def mscc5_defect_import(self, standard_user, root_folder_for_standard_user) -> Import:
        folder = root_folder_for_standard_user.add_child(
            job_name="MSCC5Folder",
            primary_org=standard_user.organisation,
            standard_key=Standard.objects.get(name="MSCC5"),
            created_date=timezone.now(),
        )
        return create_import(
            org=standard_user.organisation,
            by=standard_user,
            type="IN",
            payload=MSCC5DefectXMLInspectionImportPayload(
                destinationFolderId=folder.id,
            ).model_dump(),
        )

    def test_bad_folder(
        self, standard_user, root_folder_for_standard_user, client, import_settings: GenericImportSettings
    ):
        client.force_login(standard_user)
        folder = root_folder_for_standard_user.add_child(
            job_name="WrongStandardFolder",
            primary_org=standard_user.organisation,
            standard_key=Standard.objects.get(name="PACP7"),
            created_date=timezone.now(),
        )
        res = client.post(
            import_settings.import_create_url,
            data={
                "type": "IN",
                "payload": {
                    "format": "MSCC5_DEFECT_XML",
                    "destinationFolderId": folder.id,
                },
            },
            content_type="application/json",
        )
        assert res.status_code == status.HTTP_400_BAD_REQUEST, "Folder needs to belong to the correct standard"

    def test_empty_file(
        self,
        client: APIClient,
        standard_user: CustomUser,
        import_settings: GenericImportSettings,
        mscc5_defect_import: Import,
    ):
        client.force_login(standard_user)
        empty_file = BytesIO(b"")
        empty_file.name = "empty.xml"
        res = client.post(
            import_settings.get_import_file_upload_list_url(mscc5_defect_import.id),
            data={"file": empty_file},
            format="multipart",
        )
        assert res.status_code == status.HTTP_400_BAD_REQUEST
        assert mscc5_defect_import.files.count() == 0

    def test_malformed_xml(
        self,
        client: APIClient,
        standard_user: CustomUser,
        import_settings: GenericImportSettings,
        mscc5_defect_import: Import,
    ):
        client.force_login(standard_user)
        malformed_xml = BytesIO("<root><tag></root>".encode())
        malformed_xml.name = "malformed.xml"
        res = client.post(
            import_settings.get_import_file_upload_list_url(mscc5_defect_import.id),
            data={"file": malformed_xml},
            format="multipart",
        )
        assert res.status_code == status.HTTP_400_BAD_REQUEST
        data = res.json()
        assert len(data["errors"]) > 0
        assert any(e["code"] == "UNPARSEABLE" for e in data["errors"])
        assert mscc5_defect_import.files.count() == 0

    def test_missing_elements(
        self,
        client: APIClient,
        standard_user: CustomUser,
        import_settings: GenericImportSettings,
        mscc5_defect_import: Import,
    ):
        client.force_login(standard_user)

        xml_file = BytesIO("<SurveyGroup></SurveyGroup>".encode())
        xml_file.name = "missing_columns.xml"
        res = client.post(
            import_settings.get_import_file_upload_list_url(mscc5_defect_import.id),
            data={"file": xml_file},
            format="multipart",
        )
        assert res.status_code == status.HTTP_400_BAD_REQUEST
        data = res.json()
        assert len(data["errors"]) > 0
        assert any(e["code"] == "UNPARSEABLE" and "Survey" in e["description"] for e in data["errors"])
        assert mscc5_defect_import.files.count() == 0

    def test_missing_columns(
        self,
        client: APIClient,
        standard_user: CustomUser,
        import_settings: GenericImportSettings,
        mscc5_defect_import: Import,
    ):
        client.force_login(standard_user)

        xml_file = create_mscc5_xml(
            surveys=[
                {
                    "Header": {
                        "PipelineLengthRef": "TheAssetID",
                        "UpstreamNode": "USNODE1",
                        "DownstreamNode": "DSNODE2",
                    },
                    "Observations": [],
                }
            ]
        )
        xml_file.name = "missing_columns.xml"
        res = client.post(
            import_settings.get_import_file_upload_list_url(mscc5_defect_import.id),
            data={"file": xml_file},
            format="multipart",
        )
        assert res.status_code == status.HTTP_400_BAD_REQUEST
        data = res.json()
        assert len(data["errors"]) > 0
        assert any(e["code"] == "MISSING_COL" and "HeightDiameter" in e["description"] for e in data["errors"])
        assert mscc5_defect_import.files.count() == 0

    def test_bad_date(
        self,
        client: APIClient,
        standard_user: CustomUser,
        import_settings: GenericImportSettings,
        mscc5_defect_import: Import,
    ):
        client.force_login(standard_user)

        xml_file = create_mscc5_xml(surveys=[create_mscc5_survey(date="Not a date")])
        xml_file.name = "missing_columns.xml"
        res = client.post(
            import_settings.get_import_file_upload_list_url(mscc5_defect_import.id),
            data={"file": xml_file},
            format="multipart",
        )
        assert res.status_code == status.HTTP_400_BAD_REQUEST
        data = res.json()
        assert len(data["errors"]) > 0
        assert any(e["code"] == "BAD_VALUE" and "Date" in e["description"] for e in data["errors"])
        assert mscc5_defect_import.files.count() == 0

    @pytest.mark.parametrize(
        "xml_file",
        [
            create_mscc5_xml(),
            create_mscc5_xml(n=1000),
        ],
    )
    def test_valid_xml(self, xml_file, standard_user, client, mscc5_defect_import):
        client.force_login(standard_user)
        xml_file.name = "valid.xml"
        res = client.post(
            f"/api/v3/imports/{mscc5_defect_import.id}/files",
            data={"file": xml_file},
            format="multipart",
        )
        assert res.status_code == status.HTTP_201_CREATED
        data = res.json()

        assert mscc5_defect_import.files.count() == 1
        import_file = mscc5_defect_import.files.first()

        assert import_file.validation_status == "PA", "Should have successfully validated"
        assert import_file.validated_at is not None
        assert import_file.extension == ".xml"
        assert import_file.blob_url.startswith("import-files/")
        assert import_file.original_filename == "valid.xml"
        assert import_file.mime_type in ImportFile.TYPE_TO_ALLOWED_MIMETYPES[ImportTypeEnum.INSPECTIONS]

        mscc5_defect_import.refresh_from_db()
        assert mscc5_defect_import.validation_status == "PA"
        assert mscc5_defect_import.validated_at is not None

        assert data["id"] == str(import_file.id)
        assert data["importOperation"] == str(mscc5_defect_import.id)
        assert data["createdBy"] == standard_user.id
        assert data["validationStatus"] == "PA"
        assert data["originalFilename"] == "valid.xml"
        assert data["blobUrl"] == import_file.blob_url


class TestLinkedEntities:
    @pytest.fixture
    def import_obj(self, standard_user):
        return create_import(org=standard_user.organisation, by=standard_user, type="AS")

    @pytest.fixture
    def assets(self, standard_user):
        return factory.create_assets(org=standard_user.organisation, n=2)

    @pytest.fixture
    def inspections(self, standard_user):
        return factory.create_inspections(org=standard_user.organisation, n=2)

    @pytest.fixture
    def linked_asset(self, import_obj):
        asset = factory.create_assets(org=import_obj.target_org)[0]
        import_obj.assets.add(asset)
        import_obj.save()
        return asset

    @pytest.fixture
    def linked_inspection(self, import_obj):
        inspection = factory.create_inspections(org=import_obj.target_org)[0]
        import_obj.inspections.add(inspection)
        import_obj.save()
        return inspection

    def test_link_assets(self, import_obj, assets, standard_user, client, import_settings: GenericImportSettings):
        client.force_login(standard_user)

        res = client.post(
            import_settings.get_imported_asset_create_url(import_obj.id),
            data={
                "assetUuids": [a.uuid for a in assets],
            },
            content_type="application/json",
        )
        assert res.status_code == status.HTTP_201_CREATED
        data = res.json()

        import_obj.refresh_from_db()
        assert import_obj.assets.count() == 2
        assert all(a in import_obj.assets.all() for a in assets)
        assert sorted(data["assetUuids"]) == sorted([str(a.uuid) for a in assets])

    def test_retrieve_from_linked_asset(
        self, linked_asset, standard_user, client, import_settings: GenericImportSettings
    ):
        client.force_login(standard_user)

        res = client.get(import_settings.get_imported_asset_retrieve_url(linked_asset.uuid))
        assert res.status_code == status.HTTP_200_OK
        data = res.json()
        assert data["id"] == str(linked_asset.import_set.first().id)

    def test_link_inspections(
        self, import_obj, inspections, standard_user, client, import_settings: GenericImportSettings
    ):
        client.force_login(standard_user)

        res = client.post(
            import_settings.get_imported_inspection_create_url(import_obj.id),
            data={
                "inspectionUuids": [i.uuid for i in inspections],
            },
            content_type="application/json",
        )
        assert res.status_code == status.HTTP_201_CREATED
        data = res.json()
        assert sorted(data["inspectionUuids"]) == sorted([str(i.uuid) for i in inspections])

        import_obj.refresh_from_db()
        assert import_obj.inspections.count() == 2
        assert all(i in import_obj.inspections.all() for i in inspections)

    def test_retrieve_from_linked_inspection(
        self, linked_inspection, standard_user, client, import_settings: GenericImportSettings
    ):
        client.force_login(standard_user)

        res = client.get(import_settings.get_imported_inspection_retrieve_url(linked_inspection.uuid))
        assert res.status_code == status.HTTP_200_OK
        data = res.json()
        assert data["id"] == str(linked_inspection.import_set.first().id)
