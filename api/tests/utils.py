import uuid
from api.base.models import Header
from api.defects.models import StandardHeader


def get_headers(header_type: str) -> [Header]:
    """
    Get a list of headers by type

    :param header_type: header type as a string
    :return: a list of header objects
    """
    return Header.objects.filter(type=header_type, is_editable=True)


def get_standard_header(standard: int, header: uuid) -> StandardHeader:
    """
    Get a standard header given a standard and a header

    :param standard: a standard id
    :param header: a header uuid
    :return: a standard header object
    """
    return StandardHeader.objects.filter(standard=standard, header=header).first()
