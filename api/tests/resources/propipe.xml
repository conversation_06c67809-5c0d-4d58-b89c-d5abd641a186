<?xml version="1.0" encoding="ISO-8859-1" ?>
<exportdata distanceunit="meter" cnameprefix="minicam">
  <project>
    <name cname="cdname">ProPipe Test</name>
    <cddate>2022-11-01</cddate>
    <catalogbase>PROPIPE</catalogbase>
    <catalogcustom>PRO</catalogcustom>
    <country>UK</country>
    <MiniCamCatalogueFileName>ProPipe_Catalogue_UK_ENG_PRO</MiniCamCatalogueFileName>
    <MiniCamProjectComments>text</MiniCamProjectComments>
  </project>
  <section>
    <sectionname>secName1</sectionname>
    <MinicamSectionComments>text</MinicamSectionComments>
    <MinicamXMLname>Section0</MinicamXMLname>
    <MinicamSummary></MinicamSummary>
    <PCDStartNode cname="PCDStartNode" MinicamFlags="2">mh01</PCDStartNode>
    <PCDEndNode cname="PCDEndNode" MinicamFlags="2">mh02</PCDEndNode>
    <PCDNumber cname="PCDNumber">1</PCDNumber>
    <PCDPipeMaterial cname="PCDPipeMaterial" codetext="PVC">PV</PCDPipeMaterial>
    <PCDPipeDimension1 cname="PCDPipeDimension1" codetext="50" unit="mm" MinicamFlags="2">50</PCDPipeDimension1>
    <PCDLocation cname="PCDLocation"></PCDLocation>
    <PCDStartStreetName cname="PCDStartStreetName">Yew Tree</PCDStartStreetName>
    <PCDCityName></PCDCityName>
    <PCDMunicipal cname="PCDMunicipal">Bolton</PCDMunicipal>
    <PCDProjectName cname="PCDProjectName"></PCDProjectName>
    <PCDPipeShape cname="PCDPipeShape" codetext="Circular">C</PCDPipeShape>
    <PCDUsage cname="PCDUsage" codetext="Foul" MinicamFlags="2">F</PCDUsage>
    <PCDLiningMaterial cname="PCDLiningMaterial"></PCDLiningMaterial>
    <PCDLiningType cname="PCDLiningType"></PCDLiningType>
    <PCDEndDepth cname="PCDEndDepth" unit="m">2</PCDEndDepth>
    <PCDFlowControl cname="PCDFlowControl"></PCDFlowControl>
    <PCDFinalCategory cname="PCDFinalCategory"></PCDFinalCategory>
    <PCDOveralCostFactor cname="PCDOveralCostFactor"></PCDOveralCostFactor>
    <PCDRepairCostFactor cname="PCDRepairCostFactor"></PCDRepairCostFactor>
    <PCDTakingOffReference cname="PCDTakingOffReference"></PCDTakingOffReference>
    <PCDTrafficFlow cname="PCDTrafficFlow"></PCDTrafficFlow>
    <PCDTrafficGrade cname="PCDTrafficGrade"></PCDTrafficGrade>
    <PCDMidDepth cname="PCDMidDepth"></PCDMidDepth>
    <PCDPipeDimension2 cname="PCDPipeDimension2" codetext="50" unit="mm">50</PCDPipeDimension2>
    <PCDPipeJointLength cname="PCDPipeJointLength"></PCDPipeJointLength>
    <PCDPipeLength cname="PCDPipeLength"></PCDPipeLength>
    <PCDSectionLength cname="PCDSectionLength"></PCDSectionLength>
    <PCDSoilType cname="PCDSoilType"></PCDSoilType>
    <PCDStartDepth cname="PCDStartDepth" unit="m">1</PCDStartDepth>
    <PCDPLR cname="PCDPLR"></PCDPLR>
    <PCDConstructionYear cname="PCDConstructionYear"></PCDConstructionYear>
    <PCDType cname="PCDType"></PCDType>
    <PCDDrainArea cname="PCDDrainArea"></PCDDrainArea>
    <sectioninspection>
      <MinicamNumOfObs></MinicamNumOfObs>
      <MinicamLastContDefectNumber></MinicamLastContDefectNumber>
      <PIDStartNode cname="PIDStartNode" MinicamFlags="2">mh01</PIDStartNode>
      <PIDEndNode cname="PIDEndNode" MinicamFlags="2">mh02</PIDEndNode>
      <PIDDirection cname="PIDDirection" codetext="Downstream" MinicamFlags="2">D</PIDDirection>
      <PIDLengthEstimated cname="PIDLengthEstimated"></PIDLengthEstimated>
      <PIDCleaning cname="PIDCleaning"></PIDCleaning>
      <PIDWeather cname="PIDWeather"></PIDWeather>
      <PIDDate cname="PIDDate">2022-11-01</PIDDate>
      <PIDRemarks cname="PIDRemarks"></PIDRemarks>
      <PIDPurpose cname="PIDPurpose"></PIDPurpose>
      <PIDMethod cname="PIDMethod" codetext="CCTV">B</PIDMethod>
      <PIDOperator cname="PIDOperator">SHANE</PIDOperator>
      <PIDClient cname="PIDClient">00001</PIDClient>
      <PIDJobID cname="PIDJobID">8393</PIDJobID>
      <PIDContractor cname="PIDContractor"></PIDContractor>
      <PIDTime cname="PIDTime">15:27:26</PIDTime>
      <PIDTemperature cname="PIDTemperature"></PIDTemperature>
      <PIDEquipment cname="PIDEquipment"></PIDEquipment>
      <PIDLength cname="PIDLength"></PIDLength>
      <sectionobservation>
        <poddistance>0.43</poddistance>
        <podcode>CRX</podcode>
        <podtext>CRX: Circumferential Crack. from 12 O&apos;Clock to 1 O&apos;Clock Not at joint</podtext>
        <podmovie cname="podmovie">secName1_152904.avi</podmovie>
        <podmpegposition>00:00:12</podmpegposition>
        <MinicamVideoFrameCount>303</MinicamVideoFrameCount>
        <PODAtJoint cname="PODAtJoint" codetext="Not at joint">0</PODAtJoint>
        <PODPicture1>secName1_0-43m_152941.jpg</PODPicture1>
        <PODFromClock>12</PODFromClock>
        <PODToClock>1</PODToClock>
        <PODCharacteristic1 codetext="Circumferential">C</PODCharacteristic1>
        <PODRemarks></PODRemarks>
      </sectionobservation>
      <sectionobservation>
        <poddistance>0.92</poddistance>
        <podcode>COX</podcode>
        <podtext>COX: Collapse.  broken</podtext>
        <podmovie cname="podmovie">secName1_152904.avi</podmovie>
        <podmpegposition>00:00:25</podmpegposition>
        <MinicamVideoFrameCount>633</MinicamVideoFrameCount>
        <PODPicture1>secName1_0-92m_153012.jpg</PODPicture1>
        <PODRemarks>broken</PODRemarks>
      </sectionobservation>
      <sectionobservation>
        <poddistance>1.38</poddistance>
        <podcode>ENX</podcode>
        <podtext>ENX: End of survey. </podtext>
        <podmovie cname="podmovie">secName1_152904.avi</podmovie>
        <podmpegposition>00:00:40</podmpegposition>
        <MinicamVideoFrameCount>1007</MinicamVideoFrameCount>
        <PODPicture1>secName1_1-38m_153046.jpg</PODPicture1>
        <PODRemarks></PODRemarks>
      </sectionobservation>
    </sectioninspection>
  </section>
</exportdata>
