import json
from datetime import datetime

import pytest

from api.tests import factory
from api.inspections import analytics
from api.inspections.models import Inspection
from api.inspections.pydantic_models.inspection_model import get_inspection_representation

pytestmark = pytest.mark.django_db


@pytest.fixture
def single_inspection(standard_user) -> Inspection:
    asset = factory.create_assets(org=standard_user.organisation)[0]
    insp = factory.create_bulk_inspections(asset)[0]
    return Inspection.objects.get(pk=insp["inspection_id"])


def test_inspection_create(event_client, standard_user, single_inspection, client):
    client.force_login(standard_user)
    insp_repr = get_inspection_representation(single_inspection)
    file_id = insp_repr.file["id"]
    analytics.send_inspection_created_event(
        insp_repr,
        user=standard_user,
        owner_org=single_inspection.asset.organisation,
        created_at=datetime(2024, 1, 1),
        client=event_client,
    )

    assert len(event_client.events) == 1
    event = json.loads(event_client.events[0])

    assert event == {
        "id": str(insp_repr.inspection_id),
        "created_at": "2024-01-01T00:00:00",
        "user_id": standard_user.id,
        "file_id": file_id,
        "owner_org": {
            "org_id": single_inspection.asset.organisation.id,
            "org_type": "Asset_Owner",
            "full_name": single_inspection.asset.organisation.full_name,
            "country_code": single_inspection.asset.organisation.country.code,
        },
        "creator_org": {
            "org_id": standard_user.organisation.id,
            "org_type": standard_user.organisation.org_type,
            "full_name": standard_user.organisation.full_name,
            "country_code": standard_user.organisation.country.code,
        },
        "values": {
            "LengthSurveyed": insp_repr.chainage,
            "Date": insp_repr.date_captured,
            "Direction": insp_repr.direction.value,
            "GeneralRemarks": insp_repr.inspection_notes,
        },
    }


def test_inspection_update(event_client, standard_user, single_inspection, client):
    client.force_login(standard_user)
    insp_repr = get_inspection_representation(single_inspection)
    file_id = insp_repr.file["id"]
    analytics.send_inspection_updated_event(
        insp_repr,
        user=standard_user,
        owner_org=single_inspection.asset.organisation,
        updated_at=datetime(2024, 1, 1),
        client=event_client,
    )

    assert len(event_client.events) == 1
    event = json.loads(event_client.events[0])

    assert event == {
        "id": str(insp_repr.inspection_id),
        "updated_at": "2024-01-01T00:00:00",
        "user_id": standard_user.id,
        "file_id": file_id,
        "owner_org": {
            "org_id": single_inspection.asset.organisation.id,
            "org_type": "Asset_Owner",
            "full_name": single_inspection.asset.organisation.full_name,
            "country_code": single_inspection.asset.organisation.country.code,
        },
        "updater_org": {
            "org_id": standard_user.organisation.id,
            "org_type": standard_user.organisation.org_type,
            "full_name": standard_user.organisation.full_name,
            "country_code": standard_user.organisation.country.code,
        },
        "values": {
            "LengthSurveyed": insp_repr.chainage,
            "Date": insp_repr.date_captured,
            "Direction": insp_repr.direction.value,
            "GeneralRemarks": insp_repr.inspection_notes,
        },
    }
