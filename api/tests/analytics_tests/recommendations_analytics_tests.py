import json
from datetime import datetime

import pytest

from api.inspections.models import MapPointList, FileList
from api.recommendations.models import RepairRecommendation
from api.recommendations.analytics import (
    send_repair_recommendation_updated_event,
    send_repair_recommendation_created_event,
    send_repair_recommendation_applied_event,
    send_repair_plan_created_updated_event,
    RepairRecommendationParty as Party,
)

pytestmark = pytest.mark.django_db


def test_recommendation_updated_no_fields(event_client):
    rr = RepairRecommendation(
        id=123,
        target=MapPointList(associated_file=FileList(filename="testfile.mp4", id=999)),
        updated_at=datetime(2023, 1, 1),
    )
    fields_set = set()
    send_repair_recommendation_updated_event(fields_set, rr, Party.OWNER, client=event_client)
    assert len(event_client.events) == 1
    record = json.loads(event_client.events[0])
    assert record == {
        "video_id": 999,
        "repair_recommendation_id": 123,
        "updated_at": "2023-01-01T00:00:00",
        "updated_by": "owner",
        "values": {},
    }


def test_recommendation_updated_contractor_fields_updated(event_client):
    rr = RepairRecommendation(
        id=123,
        target=MapPointList(associated_file=FileList(filename="testfile.mp4", id=999)),
        updated_at=datetime(2023, 1, 1),
        c_root_treatment=True,
        c_patches_counted=5,
        o_root_treatment=False,
        o_patches_counted=6,
    )
    fields_set = {"c_patches_counted", "c_root_treatment"}
    send_repair_recommendation_updated_event(fields_set, rr, Party.CONTRACTOR, client=event_client)
    assert len(event_client.events) == 1
    record = json.loads(event_client.events[0])
    assert record == {
        "video_id": 999,
        "repair_recommendation_id": 123,
        "updated_at": "2023-01-01T00:00:00",
        "updated_by": "contractor",
        "values": {"root_treatment": True, "patches_counted": 5},
    }


def test_recommendation_updated_owner_fields_updated(event_client):
    rr = RepairRecommendation(
        id=123,
        target=MapPointList(associated_file=FileList(filename="testfile.mp4", id=999)),
        updated_at=datetime(2023, 1, 1),
        o_root_treatment=False,
        o_patches_counted=6,
    )
    fields_set = {"o_patches_counted", "o_root_treatment"}
    send_repair_recommendation_updated_event(fields_set, rr, Party.OWNER, client=event_client)
    assert len(event_client.events) == 1
    record = json.loads(event_client.events[0])
    assert record == {
        "video_id": 999,
        "repair_recommendation_id": 123,
        "updated_at": "2023-01-01T00:00:00",
        "updated_by": "owner",
        "values": {"root_treatment": False, "patches_counted": 6},
    }


def test_recommendation_created(event_client):
    rr = RepairRecommendation(
        id=123,
        target=MapPointList(associated_file=FileList(filename="testfile.mp4", id=999)),
        created_at=datetime(2023, 1, 1),
        action_summary="test action summary",
        root_treatment=True,
        patches_counted=5,
    )
    send_repair_recommendation_created_event(rr, client=event_client)
    assert len(event_client.events) == 1
    record = json.loads(event_client.events[0])
    assert record == {
        "video_id": 999,
        "repair_recommendation_id": 123,
        "created_at": "2023-01-01T00:00:00",
        "values": {
            "action_summary": "test action summary",
            "root_treatment": True,
            "cleaning_required": False,
            "patches_counted": 5,
            "patching_details": False,
            "full_relining": False,
            "dig_up": False,
            "dig_up_details": None,
            "no_immediate_action": False,
            "other_action": None,
        },
    }


def test_recommendation_applied_contractor_to_owner(event_client):
    rr = RepairRecommendation(
        id=123,
        target=MapPointList(associated_file=FileList(filename="testfile.mp4", id=999)),
        created_at=datetime(2023, 1, 1),
        c_action_summary="action summary from contractor",
        c_root_treatment=True,
        c_patches_counted=5,
        c_cleaning_required=True,
        c_patching_details=True,
        c_full_relining=True,
        c_dig_up=True,
        c_dig_up_details="dig up details from contractor",
        c_no_immediate_action=True,
        c_other_action=True,
        o_action_summary="action summary from owner",
        o_root_treatment=False,
        o_patches_counted=6,
        o_cleaning_required=False,
        o_patching_details=False,
        o_full_relining=False,
        o_dig_up=False,
        o_dig_up_details="dig up details from owner",
        o_no_immediate_action=False,
        o_other_action=False,
    )
    send_repair_recommendation_applied_event(
        rr, Party.CONTRACTOR, Party.OWNER, datetime(2023, 1, 2), client=event_client
    )
    assert len(event_client.events) == 1
    record = json.loads(event_client.events[0])
    assert record == {
        "video_id": 999,
        "repair_recommendation_id": 123,
        "copied_from": "contractor",
        "copied_to": "owner",
        "updated_at": "2023-01-02T00:00:00",
        "previous_values": {
            "action_summary": "action summary from owner",
            "root_treatment": False,
            "patches_counted": 6,
            "cleaning_required": False,
            "patching_details": False,
            "full_relining": False,
            "dig_up": False,
            "dig_up_details": "dig up details from owner",
            "no_immediate_action": False,
            "other_action": False,
        },
        "new_values": {
            "action_summary": "action summary from contractor",
            "root_treatment": True,
            "patches_counted": 5,
            "cleaning_required": True,
            "patching_details": True,
            "full_relining": True,
            "dig_up": True,
            "dig_up_details": "dig up details from contractor",
            "no_immediate_action": True,
            "other_action": True,
        },
    }


def test_recommendation_applied_vapar_to_owner(event_client):
    rr = RepairRecommendation(
        id=123,
        target=MapPointList(associated_file=FileList(filename="testfile.mp4", id=999)),
        created_at=datetime(2023, 1, 1),
        action_summary="action summary from VAPAR",
        root_treatment=True,
        patches_counted=5,
        cleaning_required=True,
        patching_details=True,
        full_relining=True,
        dig_up=True,
        dig_up_details="dig up details from VAPAR",
        no_immediate_action=True,
        other_action=True,
        o_action_summary="action summary from owner",
        o_root_treatment=False,
        o_patches_counted=6,
        o_cleaning_required=False,
        o_patching_details=False,
        o_full_relining=False,
        o_dig_up=False,
        o_dig_up_details="dig up details from owner",
        o_no_immediate_action=False,
        o_other_action=False,
    )
    send_repair_recommendation_applied_event(rr, Party.VAPAR, Party.OWNER, datetime(2023, 1, 2), client=event_client)
    assert len(event_client.events) == 1
    record = json.loads(event_client.events[0])
    assert record == {
        "video_id": 999,
        "repair_recommendation_id": 123,
        "copied_from": "vapar",
        "copied_to": "owner",
        "updated_at": "2023-01-02T00:00:00",
        "previous_values": {
            "action_summary": "action summary from owner",
            "root_treatment": False,
            "patches_counted": 6,
            "cleaning_required": False,
            "patching_details": False,
            "full_relining": False,
            "dig_up": False,
            "dig_up_details": "dig up details from owner",
            "no_immediate_action": False,
            "other_action": False,
        },
        "new_values": {
            "action_summary": "action summary from VAPAR",
            "root_treatment": True,
            "patches_counted": 5,
            "cleaning_required": True,
            "patching_details": True,
            "full_relining": True,
            "dig_up": True,
            "dig_up_details": "dig up details from VAPAR",
            "no_immediate_action": True,
            "other_action": True,
        },
    }


def test_recommendation_applied_vapar_to_contractor(event_client):
    rr = RepairRecommendation(
        id=123,
        target=MapPointList(associated_file=FileList(filename="testfile.mp4", id=999)),
        created_at=datetime(2023, 1, 1),
        action_summary="action summary from VAPAR",
        root_treatment=True,
        patches_counted=5,
        cleaning_required=True,
        patching_details=True,
        full_relining=True,
        dig_up=True,
        dig_up_details="dig up details from VAPAR",
        no_immediate_action=True,
        other_action=True,
        c_action_summary="action summary from contractor",
        c_root_treatment=False,
        c_patches_counted=6,
        c_cleaning_required=False,
        c_patching_details=False,
        c_full_relining=False,
        c_dig_up=False,
        c_dig_up_details="dig up details from contractor",
        c_no_immediate_action=False,
        c_other_action=False,
    )
    send_repair_recommendation_applied_event(
        rr, Party.VAPAR, Party.CONTRACTOR, datetime(2023, 1, 2), client=event_client
    )

    assert len(event_client.events) == 1
    record = json.loads(event_client.events[0])
    assert record == {
        "video_id": 999,
        "repair_recommendation_id": 123,
        "copied_from": "vapar",
        "copied_to": "contractor",
        "updated_at": "2023-01-02T00:00:00",
        "previous_values": {
            "action_summary": "action summary from contractor",
            "root_treatment": False,
            "patches_counted": 6,
            "cleaning_required": False,
            "patching_details": False,
            "full_relining": False,
            "dig_up": False,
            "dig_up_details": "dig up details from contractor",
            "no_immediate_action": False,
            "other_action": False,
        },
        "new_values": {
            "action_summary": "action summary from VAPAR",
            "root_treatment": True,
            "patches_counted": 5,
            "cleaning_required": True,
            "patching_details": True,
            "full_relining": True,
            "dig_up": True,
            "dig_up_details": "dig up details from VAPAR",
            "no_immediate_action": True,
            "other_action": True,
        },
    }


def test_repairplan_created(event_client):
    rr = RepairRecommendation(
        id=123,
        target=MapPointList(associated_file=FileList(filename="testfile.mp4", id=999)),
        created_at=datetime(2023, 1, 1),
        action_summary="test action summary",
        root_treatment=True,
        patches_counted=5,
    )
    send_repair_plan_created_updated_event(rr, client=event_client)
    assert len(event_client.events) == 1
    record = json.loads(event_client.events[0])

    assert record == {
        "video_id": 999,
        "repair_recommendation_id": 123,
        "created_at": "2023-01-01T00:00:00",
        "values": {
            "likelihood": None,
            "consequence": None,
            "likelihood_comment": None,
            "consequence_comment": None,
            "frequency": None,
            "action_summary": "test action summary",
            "root_treatment": True,
            "cleaning_required": False,
            "patches_counted": 5,
            "patching_details": False,
            "full_relining": False,
            "dig_up": False,
            "dig_up_details": None,
            "no_immediate_action": False,
            "other_action": False,
        },
    }
