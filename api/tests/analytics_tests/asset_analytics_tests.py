import json
from datetime import datetime

import pytest

from api.tests import factory
from api.inspections import analytics
from api.inspections.models import Inspection
from api.inspections.serializers.asset_serializers import AssetSerializer

pytestmark = pytest.mark.django_db


@pytest.fixture
def single_asset(standard_user):
    vals = {
        "AssetID": "123",
        "UpstreamNode": "US",
        "DownstreamNode": "DS",
        "HeightDiameter": "15",
        "Material": "Concrete",
        "LocationStreet": "123 Test Street",
        "LocationTown": "Test Town",
        "UseOfDrainSewer": "SS",
    }
    return factory.create_assets(org=standard_user.organisation, asset_values_list=[vals])[0]


@pytest.fixture
def single_inspection(single_asset):
    insp_model = factory.create_bulk_inspections(single_asset)[0]
    return Inspection.objects.get(pk=insp_model["inspection_id"])


def test_asset_linked_to_inspection(single_asset, standard_user, single_inspection, event_client):
    analytics.send_asset_linked_to_inspection_event(
        single_asset.uuid,
        single_inspection.uuid,
        single_asset.organisation,
        standard_user,
        linked_at=datetime(2024, 1, 1),
        client=event_client,
    )
    assert len(event_client.events) == 1

    event = json.loads(event_client.events[0])
    assert event == {
        "asset_id": str(single_asset.uuid),
        "inspection_id": str(single_inspection.uuid),
        "linked_at": "2024-01-01T00:00:00",
        "user_id": standard_user.id,
        "owner_org": {
            "org_id": single_asset.organisation.id,
            "org_type": "Asset_Owner",
            "full_name": single_asset.organisation.full_name,
            "country_code": single_asset.organisation.country.code,
        },
        "creator_org": {
            "org_id": standard_user.organisation.id,
            "org_type": standard_user.organisation.org_type,
            "full_name": standard_user.organisation.full_name,
            "country_code": standard_user.organisation.country.code,
        },
    }


def test_asset_created(single_asset, standard_user, event_client):
    asset_model = AssetSerializer(instance=single_asset).to_representation(single_asset)
    analytics.send_asset_created_event(
        asset_model,
        "SS",
        standard_user,
        single_asset.organisation,
        created_at=datetime(2024, 1, 1),
        client=event_client,
    )
    assert len(event_client.events) == 1

    event = json.loads(event_client.events[0])
    assert event == {
        "id": str(single_asset.uuid),
        "created_at": "2024-01-01T00:00:00",
        "user_id": standard_user.id,
        "owner_org": {
            "org_id": single_asset.organisation.id,
            "org_type": "Asset_Owner",
            "full_name": single_asset.organisation.full_name,
            "country_code": single_asset.organisation.country.code,
        },
        "creator_org": {
            "org_id": standard_user.organisation.id,
            "org_type": standard_user.organisation.org_type,
            "full_name": standard_user.organisation.full_name,
            "country_code": standard_user.organisation.country.code,
        },
        "values": {
            "UpstreamNode": "US",
            "DownstreamNode": "DS",
            "HeightDiameter": 15.0,
            "Material": "Concrete",
            "LocationStreet": "123 Test Street",
            "LocationTown": "Test Town",
            "UseOfDrainSewer": "SS",
        },
    }


def test_asset_updated(single_asset, standard_user, event_client):
    asset_model = AssetSerializer().to_representation(single_asset)
    analytics.send_asset_updated_event(
        asset_model,
        "SS",
        standard_user,
        single_asset.organisation,
        updated_at=datetime(2024, 1, 1),
        client=event_client,
    )
    assert len(event_client.events) == 1

    event = json.loads(event_client.events[0])
    assert event == {
        "id": str(single_asset.uuid),
        "updated_at": "2024-01-01T00:00:00",
        "user_id": standard_user.id,
        "owner_org": {
            "org_id": single_asset.organisation.id,
            "org_type": "Asset_Owner",
            "full_name": single_asset.organisation.full_name,
            "country_code": single_asset.organisation.country.code,
        },
        "updater_org": {
            "org_id": standard_user.organisation.id,
            "org_type": standard_user.organisation.org_type,
            "full_name": standard_user.organisation.full_name,
            "country_code": standard_user.organisation.country.code,
        },
        "values": {
            "UpstreamNode": "US",
            "DownstreamNode": "DS",
            "HeightDiameter": 15.0,
            "Material": "Concrete",
            "LocationStreet": "123 Test Street",
            "LocationTown": "Test Town",
            "UseOfDrainSewer": "SS",
        },
    }
