import pytest

from api.defects.models import Standard, DefectModelList, StandardSubcategory
from api.base.models import Header

pytestmark = [pytest.mark.django_db(databases=["default"])]


@pytest.mark.django_db
def test_fixture_data():
    standards = Standard.objects.all()
    assert len(standards) > 0

    sub_standards = StandardSubcategory.objects.all()
    assert len(sub_standards) > 0

    defect_models = DefectModelList.objects.all()
    assert len(defect_models) > 0

    headers = Header.objects.all()
    assert len(headers) > 0
