import string
import uuid
from functools import cached_property

from pydantic_settings import BaseSettings
from pydantic import computed_field


class CoreSettings(BaseSettings):
    """
    Base settings used in pytest suite
    """

    base_url: str
    client_url: str
    timezone: str = "UTC"


class AuthSettings(CoreSettings):
    """
    Authentication settings used in pytest suite
    """

    redis_host: str
    product_owner_email: str
    product_owner_password: str
    standard_user_email: str
    standard_user_password: str
    alphabet_chars: str = string.ascii_letters + string.digits + "-_!@#$%^&*()+"

    @computed_field
    @cached_property
    def login_url(self) -> str:
        return f"{self.base_url}/login"

    @computed_field
    @cached_property
    def verify_url(self) -> str:
        return f"{self.base_url}/verify"

    @computed_field
    @cached_property
    def client_verify_url(self) -> str:
        return f"{self.client_url}/api/v3/verify"


class HeaderSettings(CoreSettings):
    """ """

    @computed_field
    @cached_property
    def header_list_url(self) -> str:
        return f"{self.base_url}/headers"


class DefectSettings(CoreSettings):
    @computed_field
    @cached_property
    def standard_subcategories_url(self) -> str:
        return f"{self.base_url}/standards/subcategories"

    @computed_field
    @cached_property
    def standard_defects_url(self) -> str:
        return f"{self.base_url}/standards/defects"

    @computed_field
    @cached_property
    def standards_url(self) -> str:
        return f"{self.base_url}/standards"


class InspectionSettings(CoreSettings):
    """
    Inspection settings used in pytest suite

    """

    @computed_field
    @cached_property
    def get_base_inspection_url(self) -> str:
        return f"{self.base_url}/inspections"

    @computed_field
    @cached_property
    def inspection_list_url(self) -> str:
        return f"{self.base_url}/inspections2?organisation={{organisation_id}}"

    @computed_field
    @cached_property
    def inspection_url(self) -> str:
        return f"{self.base_url}/inspections2/{{inspection_uuid}}"

    @computed_field
    @cached_property
    def inspection_filter_url(self) -> str:
        return f"{self.get_base_inspection_url}/filters?organisation={{organisation_id}}"

    @computed_field
    @cached_property
    def standard_inspection_url(self) -> str:
        return f"{self.get_base_inspection_url}/{{inspection_id}}/standard-headers"

    def get_inspections_post_url(self, asset_uuid: uuid.UUID, file_id: int, folder_id: int) -> str:
        return f"{self.base_url}/inspections2?asset_uuid={asset_uuid}&file_id={file_id}&folder_id={folder_id}"

    @computed_field
    @cached_property
    def list_files_url(self) -> str:
        return f"{self.base_url}/files"

    @computed_field
    @cached_property
    def list_uploaded_files_url(self) -> str:
        return f"{self.list_files_url}?upload_completed=true"

    def get_retry_file_processing_url(
        self,
        file_id: int,
    ) -> str:
        return f"{self.base_url}/files/processing/{file_id}/retry"

    @computed_field
    @cached_property
    def list_processing_records_url(self) -> str:
        return f"{self.base_url}/files/processing"

    @computed_field
    @cached_property
    def upload_file_url(self) -> str:
        return f"{self.base_url}/files/upload"

    def get_upload_standalone_url(self, id: int) -> str:
        return f"{self.base_url}/files/{id}/upload"

    @computed_field
    @cached_property
    def get_inspection_bulk_status_update_url(self) -> str:
        return f"{self.get_base_inspection_url}/bulk-update-status"

    @computed_field
    @cached_property
    def get_inspection_bulk_move_folder_url(self) -> str:
        return f"{self.get_base_inspection_url}/move/{{folder_id}}"

    @computed_field
    @cached_property
    def get_inspection_bulk_delete_url(self) -> str:
        return f"{self.get_base_inspection_url}/bulk-delete"

    def get_file_detail_url(self, file_id: int) -> str:
        return f"{self.base_url}/files/{file_id}"

    @computed_field
    @cached_property
    def processing_files_url(self) -> str:
        return f"{self.base_url}/files/processing"

    def get_file_processing_url(self, processing_record_id: int) -> str:
        return f"{self.base_url}/files/processing/{processing_record_id}"

    def get_post_video_frames_url(self, video_id: int) -> str:
        return f"{self.base_url}/inspections/videos/{video_id}/frames"

    @computed_field
    @cached_property
    def get_frames_list_url(self) -> str:
        return f"{self.get_base_inspection_url}/{{mpl_id}}/frames"

    @computed_field
    @cached_property
    def get_frame_url(self) -> str:
        return f"{self.get_base_inspection_url}/frames/{{frame_id}}"

    @computed_field
    @cached_property
    def get_frame_update_defect_url(self) -> str:
        return f"{self.get_frame_url}/update-defect"

    @computed_field
    @cached_property
    def get_inspection_filter_url(self) -> str:
        return f"{self.get_base_inspection_url}/filters"

    @computed_field
    @cached_property
    def get_frames_global_list_url(self) -> str:
        return f"{self.base_url}/inspections/frames"

    @computed_field
    @cached_property
    def get_folder_list_url(self) -> str:
        return f"{self.base_url}/inspections/folders"


class OrganisationSettings(CoreSettings):
    """
    Organisation settings used in pytest suite
    """

    @computed_field
    @cached_property
    def organisation_list_url(self) -> str:
        return f"{self.base_url}/organisations"

    @computed_field
    @cached_property
    def organisation_url(self) -> str:
        return f"{self.organisation_list_url}/{{id}}"

    @computed_field
    @cached_property
    def organisation_logo_url(self) -> str:
        return f"{self.organisation_list_url}/{{id}}/upload-logo"

    @computed_field
    @cached_property
    def organisation_link_url(self) -> str:
        return f"{self.organisation_list_url}/contractor/{{contractor_id}}/asset-owner/{{asset_owner_id}}/link"


class RepairRecommendationSettings(CoreSettings):
    """
    Repair recommendation settings used in pytest suite
    """

    @computed_field
    @cached_property
    def repair_recommendation_list_url(self) -> str:
        return f"{self.base_url}/repairs/{{vapar_id}}"

    @computed_field
    @cached_property
    def repair_recommendation_csv_url(self) -> str:
        return f"{self.base_url}/repair/generate-csv"

    def get_repairplan_url(self, inspection_id) -> str:
        return f"{self.base_url}/inspections/{inspection_id}/repairplan"

    def get_repairplanitemlist_url(self, inspection_id, actor) -> str:
        return f"{self.base_url}/inspections/{inspection_id}/repairplan/{actor}"

    def get_custom_repair_value_url(self, inspection_id) -> str:
        return f"{self.base_url}/repairs/{inspection_id}/custom-value"


class UserSettings(CoreSettings):
    """
    User settings used in pytest suite
    """

    @computed_field
    @cached_property
    def user_base_url(self) -> str:
        return f"{self.base_url}/users"

    @computed_field
    @cached_property
    def user_list_url(self) -> str:
        return f"{self.user_base_url}?organisation={{organisation}}"

    @computed_field
    @cached_property
    def user_url(self) -> str:
        return f"{self.user_base_url}/{{id}}"

    @computed_field
    @cached_property
    def user_verify_url(self) -> str:
        return f"{self.user_base_url}/verify?code={{code}}&email={{email}}"

    @computed_field
    @cached_property
    def user_register_url(self) -> str:
        return f"{self.base_url}/auth/register"


class AssetSettings(CoreSettings):
    """
    Asset settings used in pytest suite
    """

    @computed_field
    @cached_property
    def asset_base_url(self) -> str:
        return f"{self.base_url}/assets"

    @computed_field
    @cached_property
    def asset_list_url(self) -> str:
        return f"{self.asset_base_url}?organisation_id={{organisation_id}}"

    @computed_field
    @cached_property
    def asset_url(self) -> str:
        return f"{self.asset_base_url}/{{asset_uuid}}"

    @computed_field
    @cached_property
    def asset_inspections_url(self) -> str:
        return f"{self.asset_base_url}/{{asset_uuid}}/inspections"

    @computed_field
    @cached_property
    def asset_value_url(self) -> str:
        return f"{self.base_url}/inspections/asset-values/{{asset_value_uuid}}"

    @computed_field
    @cached_property
    def asset_match_or_create_url(self) -> str:
        return f"{self.asset_base_url}/match-or-create"


class ExportSettings(CoreSettings):
    """
    Export settings used in pytest suite
    """

    @computed_field
    @cached_property
    def export_base_url(self) -> str:
        return f"{self.base_url}/inspections/export"

    @computed_field
    @cached_property
    def export_pdf(self) -> str:
        return f"{self.export_base_url}/pdf"

    @computed_field
    @cached_property
    def export_csv(self) -> str:
        return f"{self.export_base_url}/csv"

    @computed_field
    @cached_property
    def export_planned_inspection(self) -> str:
        return f"{self.base_url}/inspections/standard-inspection/export/csv"

    @computed_field
    @cached_property
    def export_predictor(self) -> str:
        return f"{self.export_base_url}/predictor"

    @computed_field
    @cached_property
    def export_infoasset(self) -> str:
        return f"{self.export_base_url}/infoasset"

    @computed_field
    @cached_property
    def export_xml(self) -> str:
        return f"{self.export_base_url}/xml"

    @computed_field
    @cached_property
    def export_mdb(self) -> str:
        return f"{self.export_base_url}/mdb"


class GenericExportSettings(CoreSettings):
    """
    Generic export settings used in pytest suite
    """

    @computed_field
    @cached_property
    def export_create_url(self) -> str:
        return f"{self.base_url}/exports"

    @computed_field
    @cached_property
    def export_list_url(self) -> str:
        return f"{self.base_url}/exports"

    @computed_field
    @cached_property
    def export_bulk_update_status_url(self) -> str:
        return f"{self.base_url}/exports/status"

    def get_export_retrieve_update_url(self, uid) -> str:
        return f"{self.base_url}/exports/{uid}"

    def get_exports_payload_retrieve_url(self, uid) -> str:
        return f"{self.base_url}/exports/{uid}/payload"

    def get_export_output_list_create_url(self, uid) -> str:
        return f"{self.base_url}/exports/{uid}/outputs"


class GenericImportSettings(CoreSettings):
    @computed_field
    @cached_property
    def import_create_url(self) -> str:
        return f"{self.base_url}/imports"

    @computed_field
    @cached_property
    def import_list_url(self) -> str:
        return f"{self.base_url}/imports"

    def get_import_retrieve_update_url(self, uid) -> str:
        return f"{self.base_url}/imports/{uid}"

    def get_import_file_upload_list_url(self, uid) -> str:
        return f"{self.base_url}/imports/{uid}/files"

    def get_imported_asset_create_url(self, uid) -> str:
        return f"{self.base_url}/imports/{uid}/assets"

    def get_imported_asset_retrieve_url(self, uid) -> str:
        return f"{self.base_url}/assets/{uid}/import"

    def get_imported_inspection_create_url(self, uid) -> str:
        return f"{self.base_url}/imports/{uid}/inspections"

    def get_imported_inspection_retrieve_url(self, uid) -> str:
        return f"{self.base_url}/inspections/{uid}/import"


class ExternalSettings(CoreSettings):
    """
    Settings for external urls
    """

    @computed_field
    @cached_property
    def inspections_xml_url(self) -> str:
        return f"{self.base_url}/external/inspections/xml"

    @computed_field
    @cached_property
    def external_submit_job_url(self) -> str:
        return f"{self.base_url}/external/job/submit"

    @computed_field
    @cached_property
    def external_standards_url(self) -> str:
        return f"{self.base_url}/external/standards"

    @computed_field
    @cached_property
    def external_video_images_url(self) -> str:
        return f"{self.base_url}/external/video/images"

    @computed_field
    @cached_property
    def external_video_results_url(self) -> str:
        return f"{self.base_url}/external/video/results"

    @computed_field
    @cached_property
    def external_submit_jobs_url(self) -> str:
        return f"{self.base_url}/external/upload/paths"

    @computed_field
    @cached_property
    def external_upload_progress_url(self) -> str:
        return f"{self.base_url}/external/upload/progress"

    @computed_field
    @cached_property
    def external_inspections_xml_url(self) -> str:
        return f"{self.base_url}/external/inspections/xml"

    @computed_field
    @cached_property
    def external_jobs_list_url(self) -> str:
        return f"{self.base_url}/external/joblist"

    @computed_field
    @cached_property
    def external_sas_url(self) -> str:
        return f"{self.base_url}/external/sas"

    @computed_field
    @cached_property
    def external_submit_xml_job_url(self) -> str:
        return f"{self.base_url}/external/job/submit/xml"

    @computed_field
    @cached_property
    def external_frame_url(self) -> str:
        return f"{self.base_url}/external/inspections/{{id}}/frame"

    @computed_field
    @cached_property
    def external_inspection_list_url(self) -> str:
        return f"{self.base_url}/external/inspections"

    @computed_field
    @cached_property
    def external_exports_create_url(self) -> str:
        return f"{self.base_url}/external/exports"

    def get_external_exports_outputs_list_url(self, export_id: str) -> str:
        return f"{self.base_url}/external/exports/{export_id}/outputs"

    def get_external_exports_get_url(self, export_id: str) -> str:
        return f"{self.base_url}/external/exports/{export_id}"
