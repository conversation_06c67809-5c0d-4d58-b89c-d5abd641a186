from rest_framework import serializers
from django.core.cache import caches
from django.contrib.auth.password_validation import validate_password

from api.common.enums import UserLevelEnum, OrgDisplayGroupEnum
from api.organisations.models import Organisations
from api.organisations.serializers import EditOrganisationSerializer, BaseOrganisationSerializer
from api.users.models import CustomUser

sas_cache = caches["default"]


class LoginSerializer(serializers.ModelSerializer):
    class Meta:
        model = CustomUser
        exclude = ["password", "expiry_datetime"]


class BaseUserSerializer(serializers.ModelSerializer):
    organisation = EditOrganisationSerializer(read_only=True)

    def create(self, validated_data):
        return CustomUser.objects.create_user(**validated_data)

    def validate_password(self, value):
        if value:
            try:
                validate_password(value)
            except serializers.ValidationError as error:
                raise serializers.ValidationError(error)

        return value

    class Meta:
        model = CustomUser
        fields = (
            "id",
            "email",
            "first_name",
            "last_name",
            "full_name",
            "password",
            "is_active",
            "is_member",
            "is_staff",
            "group",
            "organisation",
            "user_level",
            "expiry_datetime",
            "created_at",
            "require_tfa",
        )
        extra_kwargs = {"password": {"write_only": True}}


class UserSerializer(BaseUserSerializer):
    organisation_id = serializers.IntegerField(write_only=True, required=False)

    def create(self, validated_data):
        return CustomUser.objects.create_user_as_product_owner(**validated_data)

    class Meta(BaseUserSerializer.Meta):
        fields = BaseUserSerializer.Meta.fields + ("organisation", "organisation_id")


class EditUserSerializer(BaseUserSerializer):
    def validate_user_level(self, value):
        if value not in (level.value for level in UserLevelEnum):
            raise serializers.ValidationError("Invalid user level")

        return value

    class Meta(BaseUserSerializer.Meta):
        fields = ("first_name", "last_name", "user_level", "is_active")


class PasswordResetSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)
    user: CustomUser

    def validate_email(self, value):
        user = CustomUser.objects.filter(email=value).first()

        if not user:
            raise serializers.ValidationError("Unable to reset password")

        self.user = user
        return value

    def create(self, validated_data):
        pass

    def update(self, instance, validated_data):
        pass


class PasswordResetConfirmSerializer(serializers.Serializer):
    new_password = serializers.CharField(required=True)
    new_password_confirm = serializers.CharField(required=True)

    def validate(self, attrs):
        if attrs["new_password"] != attrs["new_password_confirm"]:
            raise serializers.ValidationError("Passwords do not match")

        validate_password(attrs["new_password"])

        return attrs

    def create(self, validated_data):
        pass

    def update(self, instance, validated_data):
        pass


class UserOrganisationContextOptionSerializer(BaseOrganisationSerializer):
    display_group = serializers.SerializerMethodField()

    def get_display_group(self, obj) -> OrgDisplayGroupEnum:
        if getattr(self.context["request"].organisation, "id", None) == obj.id:
            display_group = OrgDisplayGroupEnum.MY_ORGANISATION
        elif obj.is_asset_owner:
            display_group = OrgDisplayGroupEnum.ASSET_OWNERS
        elif obj.is_contractor:
            display_group = OrgDisplayGroupEnum.CONTRACTORS
        return display_group

    class Meta:
        model = Organisations
        fields = BaseOrganisationSerializer.Meta.fields + ["display_group"]
