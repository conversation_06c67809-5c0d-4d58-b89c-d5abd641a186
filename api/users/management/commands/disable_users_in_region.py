from pathlib import Path

import pandas as pd
from django.conf import settings
from django.core.management import BaseCommand, CommandError

from api.users.models import CustomUser


class Command(BaseCommand):
    help = "Set all user accounts within a particular country code to inactive"

    def add_arguments(self, parser):
        parser.add_argument(
            "country_code",
            type=str,
            help="Country code of the region to disable users in",
        )
        parser.add_argument(
            "-y",
            action="store_true",
            help="Skip prompt",
        )
        parser.add_argument(
            "--output_file",
            type=str,
            help="CSV file to which the list of affected users will be written. 'deactivated-users-{country_code}.csv' by default.",
            required=False,
        )
        parser.add_argument(
            "--revert",
            action="store_true",
            help="Revert the action by setting all user from an existing output file to active",
        )

    def handle_deactivate_users(self, country_code: str, skip_prompt: bool, output_file: Path):
        users_qs = CustomUser.objects.filter(
            organisation__country=country_code,
            is_active=True,
            is_service_user=False,
            is_staff=False,
        ).values_list("id", flat=True)

        user_ids = list(users_qs)

        if skip_prompt:
            should_continue = True
        else:
            n_users = len(user_ids)
            db_desc = f"{settings.DATABASES['default']['HOST']}/{settings.DATABASES['default']['NAME']}"
            prompt = input(
                f"This will disable {n_users} {country_code} user accounts in database '{db_desc}'. Continue? (y/n): "
            )
            should_continue = prompt.strip().lower() in ["y", "yes"]

        if not should_continue:
            self.stdout.write("Exiting")
            return

        n_updated = CustomUser.objects.filter(id__in=user_ids).update(is_active=False)
        self.stdout.write(self.style.SUCCESS(f"Disabled {n_updated} user accounts in {country_code}"))

        with output_file.open("w") as f:
            pd.Series(user_ids, name="user_ids").to_csv(f, index=False)

        self.stdout.write(self.style.SUCCESS(f"Wrote ids of deactivated users to {output_file}"))

    def handle_revert_action(self, skip_prompt: bool, output_file: Path):
        if not output_file.exists():
            raise CommandError(f"Output file '{output_file}' does not exist")

        user_ids = pd.read_csv(output_file)["user_ids"].tolist()
        n_users = len(user_ids)

        if skip_prompt:
            should_continue = True
        else:
            db_desc = f"{settings.DATABASES['default']['HOST']}/{settings.DATABASES['default']['NAME']}"
            prompt = input(f"This will enable {n_users} user accounts in database '{db_desc}'. Continue? (y/n): ")
            should_continue = prompt.strip().lower() in ["y", "yes"]

        if not should_continue:
            self.stdout.write("Exiting")
            return

        n_updated = CustomUser.objects.filter(id__in=user_ids).update(is_active=True)
        self.stdout.write(self.style.SUCCESS(f"Enabled {n_updated} user accounts"))

    def handle(self, *args, **options):
        country_code = options["country_code"]
        skip_prompt = options["y"]
        is_revert_action = options["revert"]
        output_file = Path(options.get("output_file") or f"deactivated-users-{country_code}.csv")

        if not output_file.is_absolute():
            output_file = settings.BASE_DIR / output_file

        if is_revert_action:
            self.handle_revert_action(skip_prompt, output_file)
        else:
            self.handle_deactivate_users(country_code, skip_prompt, output_file)
