from django.urls import path

from api.users.views import (
    UserList,
    UserDetail,
    UserOrganisationContextOptions,
    ResendActivationEmail,
    ResetPasswordEmail,
)

urlpatterns = [
    path("users", UserList.as_view(), name="user_list"),
    path("users/<int:id>", UserDetail.as_view(), name="user_detail"),
    path("users/<int:id>/resend-activation", ResendActivationEmail.as_view(), name="user_resend_activation"),
    path("users/<int:id>/reset-password", ResetPasswordEmail.as_view(), name="user_reset_password"),
    path(
        "users/organisation-context-options",
        UserOrganisationContextOptions.as_view(),
        name="user_organisation_context_options",
    ),
]
