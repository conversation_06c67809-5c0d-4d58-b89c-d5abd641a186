from django.db.models import Q
from django.db.transaction import atomic
from django.http import HttpResponse, JsonResponse
from django.contrib.auth import get_user_model
from django.contrib.auth.tokens import PasswordResetTokenGenerator
from django.forms import model_to_dict
from django.utils.http import urlsafe_base64_encode, urlsafe_base64_decode
from django.utils.encoding import force_bytes, force_str
from django.utils import timezone
from django.conf import settings
from djangorestframework_camel_case.parser import CamelCaseJSONParser
from djangorestframework_camel_case.render import CamelCaseJSONRenderer
from drf_spectacular.utils import extend_schema, OpenApiParameter
from rest_framework import generics, status
from rest_framework.exceptions import PermissionDenied, ValidationError
from rest_framework.filters import SearchFilter, OrderingFilter
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import AllowAny
from rest_framework_simplejwt.views import TokenObtainPairView, TokenVerifyView
from rest_framework_simplejwt.tokens import AccessToken
from yarl import URL

from api.common.enums import OrgDisplayGroupEnum
from api.common.linked_organisations import get_linked_orgs
from api.common.pagination import StandardResultsSetPagination
from api.common.permissions import IsProductOwner, IsAuthenticated
from api.common.send_vapar_email import send_activation_email, send_reset_password_email
from api.inspections.models import FileList, ImportedInspectionFile, Inspection, InspectionFilter
from api.organisations.models import Organisations
from api.recommendations.models import RepairRecommendation
from api.users.models import CustomUser
from api.users.serializer import (
    BaseUserSerializer,
    LoginSerializer,
    PasswordResetSerializer,
    PasswordResetConfirmSerializer,
    UserSerializer,
    EditUserSerializer,
    UserOrganisationContextOptionSerializer,
)
from api.users.token_generator import account_activation_token, reset_password_token

User = get_user_model()


def activate_user(request, uidb64, token):
    try:
        uid = force_str(urlsafe_base64_decode(uidb64))
        user_object = User.objects.get(pk=uid)
        user = BaseUserSerializer(user_object).data

    except (TypeError, ValueError, OverflowError, User.DoesNotExist):
        user = None

    if user_object is not None and account_activation_token.check_token(user, token):
        user_object.is_active = True
        user_object.save()

        return HttpResponse(status=200)
    else:
        return HttpResponse(status=400)


def validate_reset_password_link(request, uidb64, token, redirect=True):
    try:
        uid = force_str(urlsafe_base64_decode(uidb64))
        user_object = User.objects.get(pk=uid)
        user = BaseUserSerializer(user_object).data

    except (TypeError, ValueError, OverflowError, User.DoesNotExist):
        user = None

    if user_object is not None and reset_password_token.check_token(user, token):
        if redirect:
            return HttpResponse(
                f"Your account was successfully verified for a password reset. <a href='{settings.VAPAR_RESET_PASSWORD_URL}?uidb64={uidb64}&token={token}'>Reset password here</a>"
            )
        return user
    else:
        return HttpResponse("Reset Password link is invalid!")


def create_filter_record(user: CustomUser, organisation: Organisations):
    """
    Create a filter record for a user/organisation combination
    """
    InspectionFilter.objects.create(user=user, organisation=organisation)


class VerifyView(TokenVerifyView):
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    def post(self, request, *args, **kwargs):
        token = request.data.get("token", None)

        if not token:
            return JsonResponse({"error": "token is required"}, status=status.HTTP_400_BAD_REQUEST)

        access_token_obj = AccessToken(token)
        user_id = access_token_obj["user_id"]
        response = super().post(request, *args, **kwargs)

        if response.status_code == status.HTTP_200_OK:
            user = User.objects.select_related("organisation").filter(id=user_id).first()
            org_data = {}

            if user and user.organisation:
                org_data = {
                    "organisation_id": user.organisation.id,
                    "organisation_type": user.organisation.org_type,
                    "organisation_name": user.organisation.full_name,
                    "org_country_code": str(user.organisation.country),
                }

            serialized_user = LoginSerializer(user)

            response.data.update({"user": serialized_user.data})
            response.data.update(org_data)

            return response
        else:
            return JsonResponse({"error": "failed to verify"}, status=status.HTTP_401_UNAUTHORIZED)


class LoginView(TokenObtainPairView):
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    def post(self, request, *args, **kwargs):
        request.data["email"] = request.data.get("email", "").lower()

        response = super().post(request, *args, **kwargs)

        if response.status_code == status.HTTP_200_OK:
            user = User.objects.select_related("organisation").filter(email=request.data["email"]).first()
            org_data = {}

            if user and user.organisation:
                org_data = {
                    "organisation_id": user.organisation.id,
                    "organisation_type": user.organisation.org_type,
                    "organisation_name": user.organisation.full_name,
                    "org_country_code": str(user.organisation.country),
                }

            now_time = timezone.now()

            if now_time > user.expiry_datetime:
                uid = urlsafe_base64_encode(force_bytes(user.id))
                token = reset_password_token.make_token(model_to_dict(user))
                base_reset_url = URL(settings.VAPAR_RESET_URL)
                # Note that the trailing slash is necessary for the request to be routed correctly
                reset_password_url_obj = (base_reset_url / uid / token / "").update_query(base_reset_url.query)
                reset_password_link = str(reset_password_url_obj)
                raise PermissionDenied(
                    detail={
                        "error_message": "User password is expired.",
                        "action": {"text": "Please reset password here.", "url": reset_password_link},
                        "error_type": "password_expired",
                    }
                )

            serialized_user = LoginSerializer(user)

            response.data.update({"user": serialized_user.data})
            response.data.update(org_data)

            return response
        else:
            raise PermissionDenied(
                detail={"error_message": "Failed to verify using your credentials", "error_type": "credentials_failed"}
            )


class RegisterView(generics.CreateAPIView):
    permission_classes = [AllowAny]
    serializer_class = BaseUserSerializer
    parser_classes = [CamelCaseJSONParser]
    queryset = CustomUser.objects.all()

    @atomic
    def post(self, request, *args, **kwargs):
        data = request.data
        data["is_active"] = False
        data["email"] = data["email"].lower()

        serializer = self.serializer_class(data=request.data)

        if serializer.is_valid(raise_exception=True):
            serializer.save()
            user = serializer.data

            send_activation_email(request, user=user)

            return Response({"detail": "Activation email has been sent."}, status=status.HTTP_200_OK)

        return Response(serializer.error_messages, status=status.HTTP_400_BAD_REQUEST)


class ResetPasswordView(generics.GenericAPIView, PasswordResetTokenGenerator):
    permission_classes = [AllowAny]
    serializer_class = PasswordResetSerializer
    parser_classes = [CamelCaseJSONParser]
    queryset = CustomUser.objects.all()

    def post(self, request, *args, **kwargs):
        request.data["email"] = request.data.get("email", "").lower()
        serializer = self.get_serializer(data=request.data)

        if serializer.is_valid():
            user = CustomUser.objects.filter(email=request.data["email"]).first()
            send_reset_password_email(request, user=model_to_dict(user))

            return Response(
                {"detail": "Password reset email has been sent."},
                status=status.HTTP_200_OK,
            )
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ResetPasswordConfirmView(generics.GenericAPIView, PasswordResetTokenGenerator):
    permission_classes = [AllowAny]
    serializer_class = PasswordResetConfirmSerializer
    parser_classes = [CamelCaseJSONParser]
    queryset = CustomUser.objects.all()

    def post(self, request, *args, **kwargs):
        uid = request.data.get("uid", None)
        token = request.data.get("token", None)

        try:
            uid = force_str(urlsafe_base64_decode(uid))
            user_object = User.objects.get(pk=uid)
            user = BaseUserSerializer(user_object).data

        except (TypeError, ValueError, OverflowError, User.DoesNotExist):
            user = None

        if user_object is not None and reset_password_token.check_token(user, token):
            serializer = self.serializer_class(data=request.data)
            serializer.is_valid(raise_exception=True)

            user = CustomUser.objects.filter(email=user["email"]).first()
            user.set_password(serializer.validated_data["new_password"])
            user.expiry_datetime = timezone.now() + timezone.timedelta(days=180)
            user.save()

            return Response(
                "You have successfully reset your password. You may now sign in.", status=status.HTTP_200_OK
            )

        return Response(
            {"non_field_errors": ["Your password reset failed, please try resetting your password again"]},
            status=status.HTTP_404_NOT_FOUND,
        )


class UserList(generics.ListCreateAPIView):
    permission_classes = [IsAuthenticated, IsProductOwner]
    serializer_class = UserSerializer
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    pagination_class = StandardResultsSetPagination
    filter_backends = [SearchFilter, OrderingFilter]
    search_fields = ["id", "first_name", "last_name", "email"]
    ordering_fields = [
        "first_name",
        "last_name",
        "email",
        "user_level",
        "created_at",
        "expiry_datetime",
        "is_active",
        "id",
    ]
    queryset = CustomUser.objects.all().prefetch_related("organisation").order_by("-created_at")

    def get_queryset(self):
        organisation = self.request.query_params.get("organisation", None)

        if not organisation:
            raise ValidationError("Organisation is required")

        return self.queryset.filter(organisation=organisation)

    def post(self, request, *args, **kwargs):
        data = request.data
        data["is_active"] = False
        data["email"] = data["email"].lower()
        data["group"] = 2  # needs to be deprecated

        serializer = self.serializer_class(data=data)

        if serializer.is_valid(raise_exception=True):
            serializer.save()
            send_activation_email(request, user=serializer.data)

            return Response(status=status.HTTP_200_OK)

        return Response(serializer.error_messages, status=status.HTTP_400_BAD_REQUEST)

    @extend_schema(
        parameters=[
            OpenApiParameter("organisation", int, description="Organisation ID to filter by", required=True),
        ]
    )
    def get(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)


class UserDetail(generics.RetrieveUpdateAPIView):
    permission_classes = [IsAuthenticated, IsProductOwner]
    serializer_class = BaseUserSerializer
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    queryset = CustomUser.objects.all()
    http_method_names = ["get", "patch", "delete"]
    lookup_field = "id"

    def get(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

    def patch(self, request, *args, **kwargs):
        partial = kwargs.pop("partial", False)
        instance = self.get_object()
        serializer = EditUserSerializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        full_serializer = self.get_serializer(instance)
        return Response(full_serializer.data, status=status.HTTP_200_OK)

    def delete(self, request, *args, **kwargs):
        # check user has not uploaded anything, imported anything, or created any inspections or updated any repair recommendations
        if not IsProductOwner().has_permission(request, self):
            raise PermissionDenied(detail="You do not have permission to delete this user")

        instance = self.get_object()

        files = FileList.objects.filter(uploaded_by=instance)
        imported_files = ImportedInspectionFile.objects.filter(created_by=instance)
        inspections = Inspection.objects.filter(created_by=instance)
        repairs = RepairRecommendation.objects.filter(
            Q(updated_by=instance) | Q(c_updated_by=instance) | Q(o_updated_by=instance)
        )

        if files or imported_files or inspections or repairs:
            raise ValidationError("You cannot delete this user as they have critical data associated with them")

        instance.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


class ResendActivationEmail(APIView):
    permission_classes = [IsAuthenticated, IsProductOwner]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    @extend_schema(request=None, responses={200: dict[str, str]})
    def post(self, request, id):
        user = CustomUser.objects.filter(id=id).first()

        if not user:
            raise ValidationError("User not found")

        if user.is_active:
            raise ValidationError("User is already active")

        send_activation_email(request, user=model_to_dict(user))

        return Response({"detail": "Activation email has been sent."}, status=status.HTTP_200_OK)


class ResetPasswordEmail(APIView):
    permission_classes = [IsAuthenticated, IsProductOwner]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    @extend_schema(request=None, responses={200: dict[str, str]})
    def post(self, request, id):
        user = CustomUser.objects.filter(id=id).first()

        if not user:
            raise ValidationError("User not found")

        send_reset_password_email(request, user=model_to_dict(user))

        return Response({"detail": "Password reset email has been sent."}, status=status.HTTP_200_OK)


class UserOrganisationContextOptions(APIView):
    permission_classes = [IsAuthenticated]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    serializer_class = UserOrganisationContextOptionSerializer

    def get(self, request):
        user = request.user

        if IsProductOwner().has_permission(request, self):
            organisations = Organisations.objects.all()
        else:
            organisations = []
            organisations.append(user.organisation)
            organisations.extend(get_linked_orgs(user.organisation))

        serializer = self.serializer_class(organisations, many=True, context={"request": request})
        sorted_serializer_data = sorted(serializer.data, key=lambda x: (x["display_group"], x["full_name"]))
        for item in serializer.data:
            item["display_group"] = str(OrgDisplayGroupEnum(item["display_group"]))
        return Response(sorted_serializer_data)
