from functools import lru_cache

from django.db import models
from django.contrib.auth.models import AbstractBaseUser, BaseUserManager
from django.contrib.auth.password_validation import validate_password
from django.utils import timezone as django_timezone
from django.core.exceptions import ValidationError

from rest_framework.exceptions import PermissionDenied
from rest_framework_api_key.models import AbstractAPIKey

from api.organisations.models import Organisations
from api.common.send_vapar_email import send_vapar_email


class CustomUserManager(BaseUserManager):
    def create_user_as_product_owner(self, email, password=None, **extra_fields):
        organisation_id = extra_fields.pop("organisation_id")
        organisation = Organisations.objects.get(id=organisation_id)
        extra_fields["organisation"] = organisation
        extra_fields["require_tfa"] = organisation.require_tfa
        extra_fields["expiry_datetime"] = django_timezone.now() + django_timezone.timedelta(days=180)
        user = self.model(email=self.normalize_email(email), **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_user(self, email, password=None, **extra_fields):
        if password:
            try:
                validate_password(password)
            except ValidationError as error:
                raise ValidationError(error)

        if not email:
            raise PermissionDenied({"error": "Invalid Email"})

        # check if email domain matches an existing organisation domain
        email_end_i = email.find("@") + 1
        email_end = email[email_end_i:].lower()

        matching_org = Organisations.objects.filter(email_domain=email_end).first()

        if (email_end_i == 0) or not matching_org:
            try:
                text = "A user has tried to sign up to VAPAR Solutions with an unregistered email domain. "
                text += f"Email: {email}"
                html = "<p>A user has tried to sign up to VAPAR Solutions with an unregistered email domain.</p>"
                html += f"<p>{extra_fields['first_name']} {extra_fields['last_name']} </p><p>{email}</p>"
                html += "<strong>VAPAR</strong>"
                # send email to VAPAR to alert of new user sign up attempt for and unregisteres domain
                build_email = {
                    "subject": "VAPAR Solutions - User with unregistered email domain tried to sign up",
                    "from_email": "<<EMAIL>>",
                    "to": ["<EMAIL>"],
                    "text_content": text,
                    "html_content": html,
                }

                send_vapar_email(build_email)

            except Exception:
                pass

            raise PermissionDenied({"error": "Unregistered email domain"})

        extra_fields["organisation"] = matching_org
        extra_fields["require_tfa"] = matching_org.require_tfa

        extra_fields["expiry_datetime"] = django_timezone.now() + django_timezone.timedelta(days=180)
        user = self.model(email=self.normalize_email(email), **extra_fields)
        user.set_password(password)
        user.save(using=self._db)

        try:
            # send email to VAPAR to alert of new user sign up
            build_email = {
                "subject": "VAPAR Solutions - New user signed up",
                "from_email": "<<EMAIL>>",
                "to": ["<EMAIL>"],
                "text_content": f"A new user has signed up to VAPAR Solutions. Name: {user.full_name}, Email: {user.email}",
                "html_content": f"<p>A new user has signed up to VAPAR Solutions.</p><p> {user.full_name} </p><p> {user.email} </p><strong>VAPAR</strong>",
            }

            send_vapar_email(build_email)

        except Exception:
            raise ValidationError("Unable to send activation email. <NAME_EMAIL>")

        return user

    def create_vapar_user(self, email, password=None, **extra_fields):
        if password:
            try:
                validate_password(password)
            except ValidationError as error:
                raise ValidationError({"password": error})

        if not email:
            raise PermissionDenied({"error": "Invalid Email"})

        extra_fields["expiry_datetime"] = django_timezone.now() + django_timezone.timedelta(days=365)
        user = self.model(email=self.normalize_email(email), **extra_fields)
        user.set_password(password)
        user.save(using=self._db)

        return user

    def create_superuser(self, email, password=None, **extra_fields):
        user = self.create_user(email, password, **extra_fields)
        user.is_admin = True
        user.is_staff = True
        user.is_member = True
        user.is_active = True
        user.save(using=self._db)

        return user

    def create_service_user(self, name: str, email: str):
        user = self.model(email=self.normalize_email(email), first_name=name, group=0)
        user.set_password(None)
        user.is_service_user = True
        user.is_staff = True
        user.is_active = True
        user.save(using=self._db)
        return user


class CustomUser(AbstractBaseUser):
    is_admin = models.BooleanField(default=False)
    is_staff = models.BooleanField(default=False)
    is_member = models.BooleanField(default=False)
    is_service_user = models.BooleanField(default=False)
    email = models.EmailField(unique=True)
    first_name = models.CharField(max_length=200, blank=True)
    last_name = models.CharField(max_length=200, blank=True)
    group = models.IntegerField(blank=True)
    full_name = models.CharField(max_length=200, blank=True)
    is_active = models.BooleanField(default=False)
    organisation = models.ForeignKey(Organisations, on_delete=models.SET_NULL, null=True, blank=True)
    expiry_datetime = models.DateTimeField(null=True, blank=True)
    require_tfa = models.BooleanField(blank=False, null=False, default=False)
    USERNAME_FIELD = "email"
    user_level = models.CharField(max_length=200, default="standard", blank=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=True)

    objects = CustomUserManager()

    @property
    def __unicode__(self):
        return self.email

    def get_full_name(self):
        return f"{self.first_name.strip()} {self.last_name.strip()}"

    @lru_cache
    def is_asset_owner(self) -> bool:
        return self.organisation and self.organisation.is_asset_owner

    def create_service_api_key(self) -> str:
        """
        Create an API key and associates it to the user.

        Only service users can create API keys. If the user already has an associated API key, it will be replaced.

        :return: The API key that was generated - this will not be shown again
        """
        if not self.is_service_user:
            raise PermissionDenied("Only service users can be assigned API keys")

        if hasattr(self, "api_key"):
            self.api_key.delete()

        _, key = ServiceUserApiKey.objects.create_key(name=self.get_full_name().strip(), user=self)
        return key

    def save(self, *args, **kwargs):
        self.full_name = self.get_full_name()
        super().save(*args, **kwargs)

    class Meta:
        db_table = "service_customuser"


class ServiceUserApiKey(AbstractAPIKey):
    user = models.OneToOneField(CustomUser, on_delete=models.CASCADE, related_name="api_key")


# TODO: Unused table to be removed in database redesign
class UserList(models.Model):
    first_name = models.CharField(max_length=200, blank=True)
    last_name = models.CharField(max_length=200, blank=True)
    email = models.CharField(max_length=200, blank=True)
    is_admin = models.BooleanField(default=False)
    is_member = models.BooleanField(default=False)
    is_staff = models.BooleanField(default=False)
    group = models.IntegerField(blank=True)
    full_name = models.CharField(max_length=200, blank=True)
    is_active = models.BooleanField(default=False)

    class Meta:
        db_table = "service_userlist"
