# Generated by Django 4.1.2 on 2024-03-14 04:44

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("organisations", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="UserList",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("first_name", models.Char<PERSON>ield(blank=True, max_length=200)),
                ("last_name", models.Char<PERSON>ield(blank=True, max_length=200)),
                ("email", models.CharField(blank=True, max_length=200)),
                ("is_admin", models.<PERSON>olean<PERSON>ield(default=False)),
                ("is_member", models.BooleanField(default=False)),
                ("is_staff", models.BooleanField(default=False)),
                ("group", models.IntegerField(blank=True)),
                ("full_name", models.Char<PERSON><PERSON>(blank=True, max_length=200)),
                ("is_active", models.<PERSON>olean<PERSON>ield(default=False)),
            ],
            options={
                "db_table": "service_userlist",
            },
        ),
        migrations.CreateModel(
            name="CustomUser",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("password", models.CharField(max_length=128, verbose_name="password")),
                (
                    "last_login",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="last login"
                    ),
                ),
                ("is_admin", models.BooleanField(default=False)),
                ("is_staff", models.BooleanField(default=False)),
                ("is_member", models.BooleanField(default=False)),
                ("email", models.EmailField(max_length=254, unique=True)),
                ("first_name", models.CharField(blank=True, max_length=200)),
                ("last_name", models.CharField(blank=True, max_length=200)),
                ("group", models.IntegerField(blank=True)),
                ("full_name", models.CharField(blank=True, max_length=200)),
                ("is_active", models.BooleanField(default=False)),
                ("expiry_datetime", models.DateTimeField(blank=True, null=True)),
                ("require_tfa", models.BooleanField(default=False)),
                (
                    "user_level",
                    models.CharField(blank=True, default="standard", max_length=200),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "organisation",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="organisations.organisations",
                    ),
                ),
            ],
            options={
                "db_table": "service_customuser",
            },
        ),
    ]
