# Generated by Django 4.1.2 on 2025-02-26 23:17

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("inspections", "0025_remove_asset_imported_from"),
        ("imports", "0003_alter_import_type_importedasset_import_assets"),
    ]

    operations = [
        migrations.AlterField(
            model_name="import",
            name="type",
            field=models.CharField(
                choices=[
                    ("AS", "ASSETS"),
                    ("IN", "INSPECTIONS"),
                    ("IV", "INSPECTION_VIDEO_MEDIA"),
                ],
                max_length=2,
            ),
        ),
        migrations.CreateModel(
            name="ImportedInspection",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "import_obj",
                    models.ForeignKey(
                        editable=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="imported_inspections",
                        to="imports.import",
                    ),
                ),
                (
                    "inspection",
                    models.ForeignKey(
                        editable=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="imports",
                        to="inspections.inspection",
                    ),
                ),
            ],
            options={
                "unique_together": {("inspection", "import_obj")},
            },
        ),
        migrations.AddField(
            model_name="import",
            name="inspections",
            field=models.ManyToManyField(
                through="imports.ImportedInspection", to="inspections.inspection"
            ),
        ),
    ]
