# Generated by Django 4.1.2 on 2025-02-17 05:17

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("inspections", "0025_remove_asset_imported_from"),
        ("imports", "0002_alter_import_payload"),
    ]

    operations = [
        migrations.AlterField(
            model_name="import",
            name="type",
            field=models.CharField(
                choices=[("AS", "ASSETS"), ("IN", "INSPECTIONS")], max_length=2
            ),
        ),
        migrations.CreateModel(
            name="ImportedAsset",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "asset",
                    models.ForeignKey(
                        editable=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="imports",
                        to="inspections.asset",
                    ),
                ),
                (
                    "import_obj",
                    models.ForeignKey(
                        editable=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="imported_assets",
                        to="imports.import",
                    ),
                ),
            ],
            options={
                "unique_together": {("asset", "import_obj")},
            },
        ),
        migrations.AddField(
            model_name="import",
            name="assets",
            field=models.ManyToManyField(
                through="imports.ImportedAsset", to="inspections.asset"
            ),
        ),
    ]
