# Generated by Django 4.1.2 on 2025-01-13 03:19

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid
import vapar.constants.imports


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("organisations", "0002_remove_organisations_use_azure_functions_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="Import",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("completed_at", models.DateTimeField(default=None, null=True)),
                ("validated_at", models.DateTimeField(default=None, null=True)),
                (
                    "validation_status",
                    models.CharField(
                        choices=[
                            ("NV", "NOT_YET_VALIDATED"),
                            ("PA", "PASSED"),
                            ("FA", "FAILED"),
                        ],
                        default=vapar.constants.imports.ImportValidationStatusEnum[
                            "NOT_YET_VALIDATED"
                        ],
                        max_length=2,
                    ),
                ),
                ("is_hidden", models.BooleanField(default=False)),
                ("type", models.CharField(choices=[("AS", "ASSETS")], max_length=2)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("UP", "UPLOADING"),
                            ("PE", "PENDING"),
                            ("PR", "PROCESSING"),
                            ("CO", "COMPLETED"),
                            ("FA", "FAILED"),
                        ],
                        default=vapar.constants.imports.ImportStatusEnum["UPLOADING"],
                        max_length=2,
                    ),
                ),
                (
                    "status_reason",
                    models.CharField(
                        choices=[
                            ("GE", "GENERIC_ERROR"),
                            ("MF", "MISSING_FILE"),
                            ("TO", "TIMEOUT"),
                            ("VF", "VALIDATION_FAILED"),
                        ],
                        default=None,
                        max_length=2,
                        null=True,
                    ),
                ),
                ("payload", models.JSONField(editable=False)),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="imports",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "target_org",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="imports",
                        to="organisations.organisations",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="ImportFile",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("original_filename", models.CharField(max_length=255)),
                ("blob_url", models.CharField(max_length=1000)),
                ("extension", models.CharField(max_length=10)),
                ("mime_type", models.CharField(max_length=255)),
                ("file_size", models.PositiveBigIntegerField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("validated_at", models.DateTimeField(default=None, null=True)),
                (
                    "validation_status",
                    models.CharField(
                        choices=[
                            ("NV", "NOT_YET_VALIDATED"),
                            ("PA", "PASSED"),
                            ("FA", "FAILED"),
                        ],
                        max_length=2,
                        null=True,
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="import_files",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "import_operation",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="files",
                        to="imports.import",
                    ),
                ),
            ],
        ),
    ]
