import dataclasses


class ErrorCodes:
    UNPARSEABLE = "UNPARSEABLE"
    MISSING_COL = "MISSING_COL"
    UNEXPECTED_COL = "UNEXPECTED_COL"
    BAD_VALUE = "BAD_VALUE"
    DUPLICATES = "DUPLICATES"
    OTHER = "OTHER"


@dataclasses.dataclass
class ImportValidationError:
    file_id: str
    description: str
    code: str = ErrorCodes.OTHER
    row_numbers: list[int] = dataclasses.field(default_factory=list)
    allowed_values: list[str] = dataclasses.field(default_factory=list)

    def to_dict(self) -> dict:
        return dataclasses.asdict(self)
