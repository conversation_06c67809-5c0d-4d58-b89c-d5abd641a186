from typing import TYPE_CHECKING, BinaryIO

import pandas as pd
from vapar.constants.conversion import STANDARD_HEADER_MAPPINGS
from vapar.constants.pipes import AssetHeaderEnum

from api.defects.models import Standard
from .errors import ErrorCodes, ImportValidationError
from .utils import validate_column_length, validate_choice_field, validate_columns_exist

if TYPE_CHECKING:
    from api.imports.models import ImportFile

ASSET_ID_COL_NAME = "AssetID"
STANDARDS_COL_NAME = "StandardName"

ASSETS_REQUIRED_COLUMNS = {ASSET_ID_COL_NAME}
ASSETS_OPTIONAL_COLUMNS = {STANDARDS_COL_NAME}
ASSETS_ALLOWED_STANDARD_HEADERS = {
    "UseOfDrainSewer",
    "UpstreamNode",
    "DownstreamNode",
    "HeightDiameter",
    "Material",
    "LocationStreet",
    "LocationTown",
}
ASSETS_ALL_ALLOWED_COLUMNS = ASSETS_REQUIRED_COLUMNS | ASSETS_OPTIONAL_COLUMNS | ASSETS_ALLOWED_STANDARD_HEADERS
PIPE_TYPE_COL_NAME = "UseOfDrainSewer"

# Limited by the database field size
MIN_ASSET_VALUE_LEN = 1
MAX_ASSET_VALUE_LEN = 255


def validate_asset_ids(df: pd.DataFrame, file_id: str) -> list[ImportValidationError]:
    errors = []

    len_errors = validate_column_length(
        df, ASSET_ID_COL_NAME, file_id, MIN_ASSET_VALUE_LEN, MAX_ASSET_VALUE_LEN, row_num_offset=2
    )
    errors.extend(len_errors)

    # Asset ID must not be empty
    empty_id_rows = df[df[ASSET_ID_COL_NAME].isna() | (df[ASSET_ID_COL_NAME] == "")]
    if not empty_id_rows.empty:
        errors.append(
            ImportValidationError(
                file_id=file_id,
                description="Asset ID cannot be empty",
                code=ErrorCodes.BAD_VALUE,
                row_numbers=list(empty_id_rows.index + 2),
            )
        )

    # Asset ID must be unique within the file
    duplicate_id_rows = df[df.duplicated(subset=ASSET_ID_COL_NAME, keep=False)]
    dup_ids = set(duplicate_id_rows[ASSET_ID_COL_NAME])
    for dup_id in dup_ids:
        rows_affected = duplicate_id_rows[duplicate_id_rows[ASSET_ID_COL_NAME] == dup_id]
        errors.append(
            ImportValidationError(
                file_id=file_id,
                description=f"Duplicate Asset ID: '{dup_id}'",
                code=ErrorCodes.DUPLICATES,
                row_numbers=list(rows_affected.index + 2),  # Display as 1-indexed row numbers including header
            )
        )

    return errors


def validate_assets_import(
    file_obj: "ImportFile",
    file_content: BinaryIO,
) -> list[ImportValidationError]:
    """
    Validate an assets import file, returning a list of encountered errors.

    If the file is valid, returns an empty list.
    """

    try:
        full_df = pd.read_csv(file_content)
    except (pd.errors.EmptyDataError, pd.errors.ParserError):
        return [
            ImportValidationError(
                file_id=str(file_obj.id),
                description="File could not be read as a CSV",
                code=ErrorCodes.UNPARSEABLE,
            )
        ]

    # Cast all columns to str, preserving NaNs
    full_df = full_df.astype(str).where(full_df.notna())

    # Handle additional columns - remove them but don't raise an error
    extra_columns = set(full_df.columns) - ASSETS_ALL_ALLOWED_COLUMNS
    full_df = full_df.drop(columns=list(extra_columns))

    # Handle missing columns
    missing_col_errors = validate_columns_exist(full_df, ASSETS_REQUIRED_COLUMNS, str(file_obj.id))
    if missing_col_errors:
        return missing_col_errors

    errors = []

    asset_id_errors = validate_asset_ids(full_df, str(file_obj.id))
    errors.extend(asset_id_errors)

    default_standard = file_obj.import_operation.target_org.standard_key.display_name
    standard_display_names = set(Standard.objects.all().values_list("display_name", flat=True))

    if STANDARDS_COL_NAME in full_df.columns:
        standard_name_errors = validate_choice_field(
            full_df, STANDARDS_COL_NAME, standard_display_names, str(file_obj.id), row_num_offset=2
        )
        errors.extend(standard_name_errors)

    if PIPE_TYPE_COL_NAME in full_df.columns:
        for idx, row in full_df.iterrows():
            standard_display_name = (
                row[STANDARDS_COL_NAME] if STANDARDS_COL_NAME in full_df.columns else default_standard
            )
            if standard_display_name not in standard_display_names:
                # Skip validating pipe type if standard is not found - should be caught by standard name validation
                continue

            allowed_types_map = STANDARD_HEADER_MAPPINGS[AssetHeaderEnum.PIPE_TYPE].get(standard_display_name, {})
            allowed_pipe_types = set(t.value for t in allowed_types_map.values())

            if row[PIPE_TYPE_COL_NAME] not in allowed_pipe_types:
                errors.append(
                    ImportValidationError(
                        file_id=str(file_obj.id),
                        description=f"Invalid pipe type '{row[PIPE_TYPE_COL_NAME]}' for standard '{standard_display_name}'",
                        code=ErrorCodes.BAD_VALUE,
                        row_numbers=[int(idx) + 2],
                    )
                )

    # Validate column lengths
    given_asset_value_columns = set(full_df.columns) & ASSETS_ALLOWED_STANDARD_HEADERS
    for col in given_asset_value_columns:
        len_errors = validate_column_length(
            full_df, col, str(file_obj.id), MIN_ASSET_VALUE_LEN, MAX_ASSET_VALUE_LEN, row_num_offset=2
        )
        errors.extend(len_errors)

    return errors
