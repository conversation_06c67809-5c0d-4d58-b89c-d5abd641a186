import pandas as pd

from .errors import ImportValidationError, ErrorCodes


def validate_choice_field(
    df: pd.DataFrame,
    col_name: str,
    allowed_values: set[str],
    file_id: str,
    row_num_offset: int = 1,
    allow_null: bool = True,
) -> list[ImportValidationError]:
    """
    Generate validation errors for a column that should only contain a specific set of values.
    """

    errors = []

    df_populated = df[df[col_name].notna()]

    if not allow_null:
        df_null = df[df[col_name].isna()]
        if not df_null.empty:
            errors.append(
                ImportValidationError(
                    file_id=file_id,
                    description=f"Column '{col_name}' must not contain null values",
                    code=ErrorCodes.BAD_VALUE,
                    row_numbers=list(df_null.index + row_num_offset),
                )
            )

    invalid_rows = df_populated[~df_populated[col_name].isin(allowed_values)]
    invalid_values = set(invalid_rows[col_name])
    for invalid_value in invalid_values:
        rows_affected = invalid_rows[invalid_rows[col_name] == invalid_value]
        errors.append(
            ImportValidationError(
                file_id=file_id,
                description=f"Invalid value '{invalid_value}' in column '{col_name}'",
                code=ErrorCodes.BAD_VALUE,
                row_numbers=list(rows_affected.index + row_num_offset),
                allowed_values=list(allowed_values),
            )
        )
    return errors


def validate_column_length(
    df: pd.DataFrame, col_name: str, file_id: str, min_len: int, max_len: int, row_num_offset: int = 1
) -> list[ImportValidationError]:
    errors = []
    nonempty_rows = df[df[col_name].notna()]
    invalid_length_rows = nonempty_rows[~nonempty_rows[col_name].str.strip().str.len().between(min_len, max_len)]
    if not invalid_length_rows.empty:
        error = ImportValidationError(
            file_id=file_id,
            description=f"Values in column '{col_name}' must be between {min_len} and {max_len} characters long",
            code=ErrorCodes.BAD_VALUE,
            row_numbers=list(invalid_length_rows.index + row_num_offset),
        )
        errors.append(error)

    return errors


def validate_columns_exist(df: pd.DataFrame, required_columns: set[str], file_id: str) -> list[ImportValidationError]:
    errors = []
    missing_cols = required_columns - set(df.columns)
    for missing_col in missing_cols:
        errors.append(
            ImportValidationError(
                file_id=file_id,
                description=f"Missing required column: '{missing_col}'",
                code=ErrorCodes.MISSING_COL,
            )
        )
    return errors
