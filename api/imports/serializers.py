from pathlib import Path

import pydantic
from django.conf import settings
from django.utils import timezone
from drf_spectacular.utils import extend_schema_field
from rest_framework import serializers
from vapar.constants.imports import ImportStatusEnum, ImportTypeEnum, ImportTriggerTypeEnum, ImportValidationStatusEnum
from vapar.core.imports import (
    IMPORT_TYPE_TO_PAYLOAD_TYPE,
    AnyImportPayload,
    MSCC5DefectXMLInspectionImportPayload,
    AnyInspectionImportPayload,
    InspectionVideoMediaImportPayload,
    IMPORT_TYPE_TO_TRIGGER_TYPE,
)

from .models import Import, ImportFile, ImportedAsset, ImportedInspection
from api.defects.models import Standard
from api.inspections.models import JobsTree, Asset, Inspection, FileList


# Give correct schema for the payload field
@extend_schema_field(AnyImportPayload)  # type: ignore
class ImportPayloadField(serializers.JSONField):
    pass


class ImportCreateSerializer(serializers.ModelSerializer):  # For POST only
    payload = ImportPayloadField()

    class Meta:
        model = Import
        fields = [
            "id",
            "type",
            "target_org",
            "created_by",
            "updated_at",
            "completed_at",
            "validated_at",
            "validation_status",
            "is_hidden",
            "payload",
        ]
        read_only_fields = [  # Can only specify type and payload
            "id",
            "target_org",  # Org is inferred from the request
            "created_by",
            "created_at",
            "updated_at",
            "completed_at",
            "validated_at",
            "validation_status",
            "is_hidden",
        ]

    def _get_destination_folder(self, folder_id: int) -> JobsTree:
        folder = JobsTree.objects.filter(
            id=folder_id,
        ).first()
        if not folder:
            raise serializers.ValidationError("Destination folder does not exist")
        if folder.owning_org != self.context["request"].organisation:
            raise serializers.ValidationError("Destination folder does not belong to the target organisation")
        if not folder.can_contain_inspections:
            raise serializers.ValidationError("Destination folder must be a folder that can contain inspections")
        if folder.secondary_org:
            raise serializers.ValidationError("Destination folder cannot belong to a contractor")

        return folder

    def _get_folder_standard(self, folder: JobsTree) -> Standard:
        return folder.standard_key if folder.standard_key else folder.primary_org.standard_key

    def validate(self, data):
        data = super().validate(data)

        imp_type = ImportTypeEnum(data["type"])
        expected_payload_cls = IMPORT_TYPE_TO_PAYLOAD_TYPE.get(imp_type)
        if not expected_payload_cls:
            return data

        try:
            payload = expected_payload_cls.model_validate(data.get("payload", {}))
        except pydantic.ValidationError as e:
            raise serializers.ValidationError(str(e))

        match payload:  # Payload specific validation
            case InspectionVideoMediaImportPayload(file_id=file_id):
                file_obj = FileList.objects.filter(pk=file_id).first()
                if not file_obj:
                    raise serializers.ValidationError("Target file does not exist")
                if not file_obj.is_accessible_by_org(self.context["request"].organisation):
                    raise serializers.ValidationError("Target file is not accessible by the organisation")
                if not file_obj.url:
                    raise serializers.ValidationError("Target file is not uploaded yet")

            case AnyInspectionImportPayload(root=format_specific_payload):
                destination_folder_id = format_specific_payload.destination_folder_id
                folder_obj = self._get_destination_folder(destination_folder_id)

                match format_specific_payload:  # Inspection import payload specific validation
                    case MSCC5DefectXMLInspectionImportPayload():
                        mscc5_standard = Standard.objects.get(name="MSCC5")
                        folder_standard = self._get_folder_standard(folder_obj)
                        if folder_standard != mscc5_standard:
                            raise serializers.ValidationError("Destination folder must be under MSCC5 standard")

        return data

    def create(self, validated_data) -> Import:
        imp = Import.objects.create(
            type=validated_data["type"],
            target_org=self.context["request"].organisation,
            payload=validated_data["payload"],
            created_by=self.context["request"].user,
        )
        if IMPORT_TYPE_TO_TRIGGER_TYPE[ImportTypeEnum(imp.type)] == ImportTriggerTypeEnum.ON_IMPORT_CREATION:
            # Successful validation for these only requires the creation to have succeeded, since there are no files
            imp.validation_status = ImportValidationStatusEnum.PASSED
            imp.validated_at = timezone.now()
            imp.status = ImportStatusEnum.PENDING
            imp.save()
        return imp


class ImportSerializer(serializers.ModelSerializer):  # For Get/List/Update
    payload = ImportPayloadField()

    class Meta:
        model = Import
        fields = [
            "id",
            "type",
            "target_org",
            "created_by",
            "created_at",
            "updated_at",
            "completed_at",
            "validated_at",
            "validation_status",
            "is_hidden",
            "status",
            "status_reason",
            "payload",
        ]
        read_only_fields = [  # Can edit status, status_reason, and is_hidden
            "id",
            "type",
            "target_org",
            "created_by",
            "created_at",
            "updated_at",
            "completed_at",
            "validated_at",
            "validation_status",
            "payload",
        ]

    def update(self, instance, validated_data):
        prev_status = instance.status
        instance = super().update(instance, validated_data)
        if instance.status == ImportStatusEnum.COMPLETED and prev_status != ImportStatusEnum.COMPLETED:
            instance.completed_at = timezone.now()
            instance.save()
        return instance


class ImportValidationErrorSerializer(serializers.Serializer):
    file_id = serializers.CharField()
    description = serializers.CharField()
    code = serializers.CharField()
    row_numbers = serializers.ListField(child=serializers.IntegerField())
    allowed_values = serializers.ListField(child=serializers.CharField())


class ImportValidationErrorResponseSerializer(serializers.Serializer):
    errors = ImportValidationErrorSerializer(many=True)


class ImportFileCreateSerializer(serializers.ModelSerializer):  # For POST only
    file = serializers.FileField(required=True, write_only=True, max_length=ImportFile.MAX_ORIGINAL_FILENAME_LENGTH)

    class Meta:
        model = ImportFile
        fields = [
            "id",
            "import_operation",
            "created_by",
            "created_at",
            "updated_at",
            "validated_at",
            "validation_status",
            "file_size",
            "original_filename",
            "blob_url",
            "extension",
            "mime_type",
            "file",
        ]
        read_only_fields = [  # No fields are editable
            "id",
            "import_operation",
            "created_by",
            "created_at",
            "updated_at",
            "validated_at",
            "validation_status",
            "file_size",
            "original_filename",
            "blob_url",
            "extension",
            "mime_type",
        ]

    def validate(self, data):
        data = super().validate(data)
        max_size = settings.IMPORTS_MAX_UPLOAD_SIZE
        if data["file"].size > max_size:
            raise serializers.ValidationError(
                f"File size {data['file'].size} exceeds maximum allowed size of {max_size} bytes"
            )

        import_obj = self.context["import_obj"]

        if IMPORT_TYPE_TO_TRIGGER_TYPE[ImportTypeEnum(import_obj.type)] != ImportTriggerTypeEnum.ON_FILE_UPLOAD:
            raise serializers.ValidationError("Files cannot be uploaded for this import type")

        mime_type = data["file"].content_type
        allowed_mime_types = ImportFile.TYPE_TO_ALLOWED_MIMETYPES.get(import_obj.type, [])
        if mime_type not in allowed_mime_types:
            raise serializers.ValidationError(f"File type {mime_type} is not allowed for this import type")

        return data

    def create(self, validated_data) -> ImportFile:
        """
        Expects "import_obj" and "blob_url" in serializer context.

        Does not handle file upload to storage.
        """
        file_obj = validated_data["file"]
        extension = Path(file_obj.name).suffix
        blob_with_extension = Path(self.context["blob_url"]).with_suffix(extension)
        import_file = ImportFile.objects.create(
            import_operation=self.context["import_obj"],
            created_by=self.context["request"].user,
            original_filename=file_obj.name,
            blob_url=str(blob_with_extension),
            extension=extension,
            mime_type=file_obj.content_type,
            file_size=file_obj.size,
        )
        import_file.import_operation.status = ImportStatusEnum.PENDING  # Assume one file per import for now
        import_file.import_operation.save()
        return import_file


class ImportFileSerializer(serializers.ModelSerializer):  # For Get/List
    class Meta:
        model = ImportFile
        fields = [
            "id",
            "import_operation",
            "created_at",
            "updated_at",
            "created_by",
            "validated_at",
            "validation_status",
            "file_size",
            "original_filename",
            "blob_url",
            "extension",
            "mime_type",
        ]
        read_only_fields = [  # No fields are editable
            "id",
            "import_operation",
            "created_at",
            "updated_at",
            "created_by",
            "validated_at",
            "validation_status",
            "file_size",
            "original_filename",
            "blob_url",
            "extension",
            "mime_type",
        ]


class ImportedAssetCreateSerializer(serializers.Serializer):
    """
    Serializer for creating ImportedAsset objects, thereby linking assets to an import
    """

    asset_uuids = serializers.ListSerializer(child=serializers.UUIDField(), required=True)

    def validate(self, attrs):
        attrs = super().validate(attrs)

        # Check that all assets exist and belong to the target organisation
        org = self.context["request"].organisation
        count = Asset.objects.filter(pk__in=attrs["asset_uuids"], organisation=org).count()
        if count != len(attrs["asset_uuids"]):
            raise serializers.ValidationError(
                "One or more assets do not exist or do not belong to the target organisation"
            )

        # Check not already linked
        if ImportedAsset.objects.filter(asset_id__in=attrs["asset_uuids"]).exists():
            raise serializers.ValidationError("One or more assets are already linked to an import")

        return attrs

    def update(self, instance: Import, validated_data: dict):
        asset_uuids = validated_data["asset_uuids"]
        join_objs = [ImportedAsset(asset_id=asset_id, import_obj=instance) for asset_id in asset_uuids]
        ImportedAsset.objects.bulk_create(join_objs)

        return instance


class ImportedInspectionCreateSerializer(serializers.Serializer):
    """
    Serializer for creating ImportedInspection objects, thereby linking inspections to an import.
    """

    inspection_uuids = serializers.ListSerializer(child=serializers.UUIDField(), required=True)

    def validate(self, attrs):
        attrs = super().validate(attrs)

        org = self.context["request"].organisation
        count = Inspection.objects.filter(pk__in=attrs["inspection_uuids"], asset__organisation=org).count()
        if count != len(attrs["inspection_uuids"]):
            raise serializers.ValidationError(
                "One or more inspections do not exist or do not belong to the target organisation"
            )

        if ImportedInspection.objects.filter(inspection_id__in=attrs["inspection_uuids"]).exists():
            raise serializers.ValidationError("One or more inspections are already linked to an import")

        return attrs

    def update(self, instance: Import, validated_data: dict):
        insp_uuids = validated_data["inspection_uuids"]
        join_objs = [ImportedInspection(inspection_id=insp_id, import_obj=instance) for insp_id in insp_uuids]
        ImportedInspection.objects.bulk_create(join_objs)

        return instance
