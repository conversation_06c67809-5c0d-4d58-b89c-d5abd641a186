import httpx
from django.core.management import BaseCommand, CommandError
from rest_framework import status
from vapar.clients import api
from yarl import URL


class BaseImportCommand(BaseCommand):
    """
    Template class for import commands. Implementations should implement the add_arguments and handle methods, which
    build the necessary payload then call initiate_import.
    """

    def create_import(self, api_settings: api.VaparAPISettings, org_id: int, import_type: str, payload: dict) -> str:
        """
        Send a request to create an import entity and return its ID
        """
        headers = {
            api.TARGET_ORG_ID_HEADER: str(org_id),
            "X-Api-Key": api_settings.secret_key,
        }
        req_data = {
            "type": import_type,
            "payload": payload,
        }
        res = httpx.post(
            url=str(URL(api_settings.base_url) / "api/v3/imports"),
            headers=headers,
            json=req_data,
        )
        if res.status_code != status.HTTP_201_CREATED:
            self.stderr.write(res.text)
        res.raise_for_status()
        data = res.json()
        import_id = data["id"]
        return import_id

    def initiate_import(
        self,
        api_settings: api.VaparAPISettings,
        org_id: int,
        original_filename: str,
        file_data: bytes,
        file_mime_type: str,
        import_type: str,
        payload: dict,
    ) -> tuple[str, str]:
        """
        Initiates an import request and uploads the file data to it. Prints any validation errors and attempts to
        clean up the import request if any errors occur.
        :param api_settings: The API settings to use for sending requests
        :param org_id: The ID of the organisation to import into
        :param original_filename: The local filename of the file being imported
        :param file_data: The binary data of the file being imported
        :param file_mime_type: The MIME type of the file being imported
        :param import_type: The ImportTypeEnum value for the import
        :param payload: Any additional import-specific data
        :return: A tuple of the import ID and the import file ID
        """

        # Create the import request
        import_id = self.create_import(api_settings, org_id, import_type, payload)
        self.stdout.write(self.style.SUCCESS(f"Import request created with ID: {import_id}"))

        # Attach the file to the import request
        headers = {
            api.TARGET_ORG_ID_HEADER: str(org_id),
            "X-Api-Key": api_settings.secret_key,
        }
        res = httpx.post(
            str(URL(api_settings.base_url) / "api/v3/imports" / import_id / "files"),
            headers=headers,
            files={"file": (original_filename, file_data, file_mime_type)},
        )
        if res.status_code == status.HTTP_400_BAD_REQUEST:
            ret_json = res.json()
            errors = ret_json.get("errors", [])
            other_details = ret_json if not errors else None
            self._print_validation_errors(errors, other_details)
            self._cleanup_import(api_settings, import_id, org_id)
            raise CommandError("Validation errors occurred")

        if res.status_code != status.HTTP_201_CREATED:
            self._cleanup_import(api_settings, import_id, org_id)
            res.raise_for_status()

        data = res.json()
        import_file_id = data["id"]

        self.stdout.write(self.style.SUCCESS(f"Import file uploaded with ID: {import_file_id}"))
        return import_id, import_file_id

    def _cleanup_import(self, api_settings: api.VaparAPISettings, import_id: str, org_id: int):
        headers = {
            api.TARGET_ORG_ID_HEADER: str(org_id),
            "X-Api-Key": api_settings.secret_key,
        }

        res = httpx.delete(
            url=str(URL(api_settings.base_url) / "api/v3/imports" / import_id),
            headers=headers,
        )
        res.raise_for_status()
        self.stdout.write(self.style.SUCCESS("Cleaned up import request"))

    def _print_validation_errors(self, errors: list[dict], other_details: dict | None = None):
        self.stderr.write("Validation errors occurred:")

        if other_details is not None:
            self.stderr.write(str(other_details))

        for error in errors:
            self.stderr.write(error["description"])
            if allowed_vals := error.get("allowedValues"):
                self.stderr.write(f"Allowed values: {allowed_vals}")
            self.stderr.write(f"Row numbers: {error['rowNumbers']}\n\n")
