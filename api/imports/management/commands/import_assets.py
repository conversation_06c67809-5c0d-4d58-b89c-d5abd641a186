from pathlib import Path

from django.core.management import CommandError
from django.conf import settings

from vapar.clients import api

from ._base_import_command import BaseImportCommand


class Command(BaseImportCommand):
    help = "Start an asset import operation from a local CSV file."

    def add_arguments(self, parser):
        parser.add_argument("org-id", type=int, help="ID of the organisation to import assets into")
        parser.add_argument("file-path", type=Path, help="Path to the CSV file to import")
        parser.add_argument(
            "--api-url",
            type=str,
            help="Override the API URL. Default is from VAPAR_API_BASE_URL env var",
            required=False,
        )
        parser.add_argument(
            "--api-key",
            type=str,
            help="Override the API secret key. Default is from VAPAR_API_SECRET_KEY env var",
            required=False,
        )

    def handle(self, *args, **options):
        org_id = options["org-id"]
        file_path = options["file-path"]
        base_url = options.get("api_url")
        api_key = options.get("api_key")

        if not file_path.is_absolute():
            file_path = settings.BASE_DIR / file_path

        if not file_path.is_file():
            raise CommandError(f"Path is not a file: '{file_path}'")

        file_data = file_path.read_bytes()

        provided = {}
        if base_url is not None:
            provided["base_url"] = base_url
        if api_key is not None:
            provided["secret_key"] = api_key
        api_settings = api.VaparAPISettings(**provided)

        self.stdout.write(f"Importing assets into organisation {org_id} from file: {file_path}")

        original_filename = file_path.name
        _import_id, _import_file_id = self.initiate_import(
            api_settings,
            org_id,
            original_filename,
            file_data,
            file_mime_type="text/csv",
            import_type="AS",
            payload={},
        )
