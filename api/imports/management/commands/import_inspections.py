from pathlib import Path

from django.conf import settings
from django.core.management import CommandError
from vapar.clients import api
from vapar.constants.imports import ImportInspectionFileFormatEnum

from ._base_import_command import BaseImportCommand

FORMAT_OPTIONS = [f.value for f in ImportInspectionFileFormatEnum]

# Some common file types we may end up using
EXT_TO_MIME_TYPE = {
    ".csv": "text/csv",
    ".xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    ".xml": "application/xml",
    ".json": "application/json",
    ".sqlite3": "application/vnd.sqlite3",
    ".mdb": "application/vnd.ms-access",
}


class Command(BaseImportCommand):
    help = "Start a coded inspection import operation from a local data file."

    def add_arguments(self, parser):
        parser.add_argument(
            "--org-id",
            type=int,
            help="ID of the organisation to import into",
            required=True,
        )
        parser.add_argument(
            "--dest-folder-id",
            type=int,
            help="ID of the folder the inspections will end up in",
            required=True,
        )
        parser.add_argument(
            "--file-path",
            type=Path,
            help="Path to the file to import",
            required=True,
        )
        parser.add_argument(
            "--format",
            type=str,
            help="Data format of the file",
            choices=FORMAT_OPTIONS,
            required=True,
        )
        parser.add_argument(
            "--api-url",
            type=str,
            help="Override the API URL. Default is from VAPAR_API_BASE_URL env var",
            required=False,
        )
        parser.add_argument(
            "--api-key",
            type=str,
            help="Override the API secret key. Default is from VAPAR_API_SECRET_KEY env var",
            required=False,
        )

    def handle(self, *args, **options):
        org_id = options["org_id"]
        dest_folder_id = options["dest_folder_id"]
        file_path = options["file_path"]
        file_format = options["format"]
        base_url = options.get("api_url")
        api_key = options.get("api_key")

        if not file_path.is_absolute():
            file_path = settings.BASE_DIR / file_path
        if not file_path.is_file():
            raise CommandError(f"Path is not a file: {file_path}")

        file_data = file_path.read_bytes()

        provided = {}
        if base_url is not None:
            provided["base_url"] = base_url
        if api_key is not None:
            provided["secret_key"] = api_key
        api_settings = api.VaparAPISettings(**provided)

        self.stdout.write(f"Importing inspections into organisation {org_id} from file: {file_path}")

        original_filename = file_path.name
        _import_id, _import_file_id = self.initiate_import(
            api_settings,
            org_id,
            original_filename,
            file_data,
            file_mime_type=EXT_TO_MIME_TYPE.get(Path(file_path).suffix, "application/octet-stream"),
            import_type="IN",
            payload={
                "format": file_format,
                "destinationFolderId": dest_folder_id,
            },
        )
