from pathlib import Path
from uuid import uuid4

from django.conf import settings
from django.db import transaction
from django_filters.rest_framework import DjangoFilterBackend
from djangorestframework_camel_case.parser import CamelCaseJ<PERSON>NParser
from djangorestframework_camel_case.render import Camel<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from drf_spectacular.utils import extend_schema
from rest_framework import generics, status
from rest_framework.filters import OrderingFilter
from rest_framework.generics import get_object_or_404
from rest_framework.parsers import MultiPartParser, FormParser
from rest_framework.response import Response
from vapar.constants.imports import ImportTypeEnum, ImportTriggerTypeEnum
from vapar.core.imports import IMPORT_TYPE_TO_TRIGGER_TYPE, ImportRequestQueueMessage

from .models import Import, ImportFile
from .queue_trigger import enqueue_import_message
from .serializers import (
    ImportSerializer,
    ImportCreateSerializer,
    ImportFileSerializer,
    ImportFileCreateSerializer,
    ImportValidationErrorResponseSerializer,
    ImportedAssetCreateSerializer,
    ImportedInspectionCreateSerializer,
)
from api.common.permissions import IsAuthenticated, IsServiceUser
from api.common.storage import put_to_platform_blob_storage


class ImportListCreateView(generics.ListCreateAPIView):
    queryset = Import.objects.filter(is_hidden=False)
    permission_classes = [IsAuthenticated]

    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ["type", "status", "status_reason", "validation_status"]
    ordering_fields = ["created_at", "updated_at", "completed_at"]
    ordering = ["-created_at"]

    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    def get_queryset(self):
        return self.queryset.filter(target_org=self.request.organisation)

    def get_serializer_class(self):
        if self.request.method == "POST":
            return ImportCreateSerializer
        return ImportSerializer

    @transaction.atomic
    def post(self, request, *args, **kwargs):
        """
        Create a new import operation.
        """
        ser = self.get_serializer(data=request.data)
        ser.is_valid(raise_exception=True)
        instance: Import = ser.save()

        if IMPORT_TYPE_TO_TRIGGER_TYPE[ImportTypeEnum(instance.type)] == ImportTriggerTypeEnum.ON_IMPORT_CREATION:
            enqueue_import_message(
                payload=ImportRequestQueueMessage(
                    import_id=instance.id,
                    target_org_id=instance.target_org_id,
                    created_at=instance.created_at,
                ),
                org=instance.target_org,
            )

        return Response(
            self.get_serializer(instance).data,
            status=status.HTTP_201_CREATED,
        )


class ImportRetrieveUpdateDeleteView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Import.objects.filter(is_hidden=False)
    serializer_class = ImportSerializer

    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    http_method_names = ["get", "patch", "delete"]
    lookup_url_kwarg = "id"

    def get_permissions(self):
        if self.request.method == "DELETE":
            return [IsAuthenticated(), IsServiceUser()]
        else:
            return [IsAuthenticated()]

    def get_queryset(self):
        return self.queryset.filter(target_org=self.request.organisation)


class ImportFileListCreateView(generics.ListCreateAPIView):
    queryset = ImportFile.objects.all()
    serializer_class = ImportFileCreateSerializer
    permission_classes = [IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]
    renderer_classes = [CamelCaseJSONRenderer]

    filter_backends = [OrderingFilter]

    ordering_fields = ["created_at", "updated_at"]
    ordering = ["-created_at"]

    def get_queryset(self):
        import_obj = self._get_import_obj()
        return self.queryset.filter(import_operation=import_obj)

    def _get_import_obj(self) -> Import:
        return get_object_or_404(
            Import, pk=self.kwargs["import_id"], target_org=self.request.organisation, is_hidden=False
        )

    @extend_schema(
        responses={
            status.HTTP_201_CREATED: ImportFileSerializer,
            status.HTTP_400_BAD_REQUEST: ImportValidationErrorResponseSerializer,
        },
    )
    @transaction.atomic
    def post(self, request, import_id, *args, **kwargs):
        """
        Upload a file for an import, validating and saving it.
        """

        file_uuid = uuid4()

        # The file extension is not known until the file has been validated by the serializer
        blob_url = str(Path(settings.BLOB_STORAGE_IMPORTS_CONTAINER, str(file_uuid)))
        import_obj = self._get_import_obj()
        context = {
            **self.get_serializer_context(),
            "import_obj": import_obj,
            "blob_url": blob_url,
        }
        data = request.data
        serializer = self.get_serializer(data=data, context=context)
        serializer.is_valid(raise_exception=True)
        import_file_obj: ImportFile = serializer.save()

        blob_url_with_extension = import_file_obj.blob_url
        container_name, blob_name = blob_url_with_extension.split("/", 1)

        errors = import_file_obj.validate(file_content=request.data["file"])
        if errors:
            transaction.set_rollback(True)
            serialized = ImportValidationErrorResponseSerializer(
                data={
                    "errors": [e.to_dict() for e in errors],
                }
            )
            serialized.is_valid(raise_exception=True)
            return Response(
                serialized.data,
                status=status.HTTP_400_BAD_REQUEST,
            )

        request.data["file"].seek(0)
        put_to_platform_blob_storage(
            file=request.data["file"].file,
            blob_name=blob_name,
            container_name=container_name,
            region=request.organisation.country,
            metadata={
                "import_id": str(import_id),
                "target_org_id": str(request.organisation.id),
            },
        )
        return Response(
            self.get_serializer(import_file_obj).data,
            status=status.HTTP_201_CREATED,
        )


class ImportedAssetCreateView(generics.GenericAPIView):
    serializer_class = ImportedAssetCreateSerializer
    permission_classes = [IsAuthenticated]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    queryset = Import.objects.filter(is_hidden=False)

    lookup_url_kwarg = "import_id"
    lookup_field = "pk"

    def get_queryset(self):
        return self.queryset.filter(target_org=self.request.organisation)

    @transaction.atomic
    def post(self, request, *args, **kwargs):
        """
        Link a set of assets to an import operation.
        """

        import_obj = self.get_object()
        serializer = self.get_serializer(data=request.data, instance=import_obj)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(data=serializer.validated_data, status=status.HTTP_201_CREATED)


class ImportedAssetRetrieveView(generics.RetrieveAPIView):
    serializer_class = ImportSerializer
    permission_classes = [IsAuthenticated]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    queryset = Import.objects.filter(is_hidden=False)

    lookup_url_kwarg = "asset_uuid"
    lookup_field = "assets"

    def get_queryset(self):
        return self.queryset.filter(target_org=self.request.organisation)

    def get(self, request, *args, **kwargs):
        """
        Retrieve the import operation that created the asset, if any.
        """
        return super().get(request, *args, **kwargs)


class ImportedInspectionCreateView(generics.GenericAPIView):
    serializer_class = ImportedInspectionCreateSerializer
    permission_classes = [IsAuthenticated]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    queryset = Import.objects.filter(is_hidden=False)

    lookup_url_kwarg = "import_id"
    lookup_field = "pk"

    def get_queryset(self):
        return self.queryset.filter(target_org=self.request.organisation)

    @transaction.atomic
    def post(self, request, *args, **kwargs):
        """
        Link a set of inspections to an import operation.
        """

        import_obj = self.get_object()
        serializer = self.get_serializer(data=request.data, instance=import_obj)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(data=serializer.validated_data, status=status.HTTP_201_CREATED)


class ImportedInspectionRetrieveView(generics.RetrieveAPIView):
    serializer_class = ImportSerializer
    permission_classes = [IsAuthenticated]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    queryset = Import.objects.filter(is_hidden=False)

    lookup_url_kwarg = "inspection_uuid"
    lookup_field = "inspections"

    def get_queryset(self):
        return self.queryset.filter(target_org=self.request.organisation)

    def get(self, request, *args, **kwargs):
        """
        Retrieve the import operation that created the inspection, if any.
        """
        return super().get(request, *args, **kwargs)
