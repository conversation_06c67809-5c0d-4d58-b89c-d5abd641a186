from typing import BinaryIO
from uuid import uuid4

from django.db import models
from django.utils import timezone
from vapar.constants.imports import (
    ImportValidationStatusEnum,
    ImportTypeEnum,
    ImportStatusEnum,
    ImportStatusReasonEnum,
    ImportFileValidationStatusEnum,
)
from vapar.core.imports import AnyImportPayload

from api.imports.validation import ImportValidationError, validate_assets_import, validate_inspections_import


class Import(models.Model):
    """
    Represents a generic import operation.
    """

    id = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    target_org = models.ForeignKey(
        "organisations.Organisations",
        on_delete=models.SET_NULL,
        null=True,
        related_name="imports",
    )

    created_by = models.ForeignKey(
        "users.CustomUser",
        on_delete=models.SET_NULL,
        null=True,
        related_name="imports",
    )
    created_at = models.DateTimeField(auto_now_add=True, editable=False)
    updated_at = models.DateTimeField(auto_now=True)
    completed_at = models.DateTimeField(null=True, default=None)

    validated_at = models.DateTimeField(null=True, default=None)
    validation_status = models.CharField(
        max_length=2,
        choices=ImportValidationStatusEnum.as_choices(),
        default=ImportValidationStatusEnum.NOT_YET_VALIDATED,
    )

    is_hidden = models.BooleanField(default=False)  # Soft delete for FE

    type = models.CharField(max_length=2, choices=ImportTypeEnum.as_choices())
    status = models.CharField(max_length=2, choices=ImportStatusEnum.as_choices(), default=ImportStatusEnum.UPLOADING)
    status_reason = models.CharField(max_length=2, null=True, choices=ImportStatusReasonEnum.as_choices(), default=None)

    payload = models.JSONField(editable=True)  # Any additional required data specific to the import type

    assets = models.ManyToManyField("inspections.Asset", through="ImportedAsset")
    inspections = models.ManyToManyField("inspections.Inspection", through="ImportedInspection")

    @property
    def parsed_payload(self) -> AnyImportPayload:
        return AnyImportPayload.model_validate(self.payload)


class ImportFile(models.Model):
    """
    Represents a file to be uploaded as part of an import operation.
    """

    MAX_FILE_URL_LENGTH = 1_000
    MAX_ORIGINAL_FILENAME_LENGTH = 255

    TYPE_TO_ALLOWED_MIMETYPES = {
        ImportTypeEnum.ASSETS: ["text/csv"],
        ImportTypeEnum.INSPECTIONS: ["application/xml", "text/xml"],
    }

    id = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    created_by = models.ForeignKey(
        "users.CustomUser",
        on_delete=models.SET_NULL,
        null=True,
        related_name="import_files",
    )
    import_operation = models.ForeignKey(
        Import,
        on_delete=models.SET_NULL,  # So that we keep a record of the file even if the parent import is deleted
        null=True,
        related_name="files",
    )
    # The filename provided by the user on upload
    original_filename = models.CharField(max_length=MAX_ORIGINAL_FILENAME_LENGTH)

    blob_url = models.CharField(max_length=MAX_FILE_URL_LENGTH)  # "container/filepath"
    extension = models.CharField(max_length=10)
    mime_type = models.CharField(max_length=255)
    file_size = models.PositiveBigIntegerField()

    created_at = models.DateTimeField(auto_now_add=True, editable=False)
    updated_at = models.DateTimeField(auto_now=True)

    # The time at which the file's existence and contents were validated
    validated_at = models.DateTimeField(null=True, default=None)
    validation_status = models.CharField(max_length=2, null=True, choices=ImportFileValidationStatusEnum.as_choices())

    def validate(self, file_content: BinaryIO) -> list[ImportValidationError]:
        """
        Validate the files contents, updating this import file and its parent import operation as necessary.


        :param file_content: A file-like object containing the file's contents
        :return: A list of encountered validation errors, if any
        """

        match self.import_operation.type:
            case ImportTypeEnum.ASSETS:
                errors = validate_assets_import(self, file_content)
            case ImportTypeEnum.INSPECTIONS:
                errors = validate_inspections_import(self, file_content)
            case _:  # Unimplemented import type
                errors = []

        if errors:
            self.validation_status = ImportFileValidationStatusEnum.FAILED
            self.import_operation.validation_status = ImportValidationStatusEnum.FAILED
        else:
            self.validation_status = ImportFileValidationStatusEnum.PASSED
            self.import_operation.validation_status = ImportValidationStatusEnum.PASSED

        now = timezone.now()
        self.validated_at = now
        self.import_operation.validated_at = now

        self.save()
        self.import_operation.save()

        return errors


class ImportedAsset(models.Model):
    """
    Join table to associate assets with imports
    """

    asset = models.ForeignKey("inspections.Asset", on_delete=models.CASCADE, related_name="imports", editable=False)
    import_obj = models.ForeignKey(Import, on_delete=models.CASCADE, related_name="imported_assets", editable=False)
    created_at = models.DateTimeField(auto_now_add=True, editable=False)

    class Meta:
        unique_together = ("asset", "import_obj")

    def __str__(self):
        return f"{self.asset.uuid} - {self.import_obj.id}"


class ImportedInspection(models.Model):
    """
    Join table to associate inspections with imports
    """

    inspection = models.ForeignKey(
        "inspections.Inspection", on_delete=models.CASCADE, related_name="imports", editable=False
    )
    import_obj = models.ForeignKey(
        Import, on_delete=models.CASCADE, related_name="imported_inspections", editable=False
    )
    created_at = models.DateTimeField(auto_now_add=True, editable=False)

    class Meta:
        unique_together = ("inspection", "import_obj")

    def __str__(self):
        return f"{self.inspection.uuid} - {self.import_obj.id}"
