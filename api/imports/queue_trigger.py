import logging
from functools import cache

from django.conf import settings

from azure.storage.queue import QueueClient, BinaryBase64DecodePolicy, BinaryBase64EncodePolicy
from vapar.core.imports import ImportRequestQueueMessage

from api.common.storage import get_platform_conn_str
from api.organisations.models import Organisations

log = logging.getLogger(__name__)


@cache
def _get_cached_queue_client(account_conn_str: str, queue_name: str) -> QueueClient:
    """Return a cached instance if possible - since the client is immutable."""
    return QueueClient.from_connection_string(
        conn_str=account_conn_str,
        queue_name=queue_name,
        message_encode_policy=BinaryBase64EncodePolicy(),
        message_decode_policy=BinaryBase64DecodePolicy(),
    )


def get_queue_client(region: str) -> QueueClient:
    return _get_cached_queue_client(
        account_conn_str=get_platform_conn_str(region=region),
        queue_name=settings.IMPORTS_AZURE_STORAGE_QUEUE_NAME,
    )


def enqueue_import_message(payload: ImportRequestQueueMessage, org: Organisations):
    client = get_queue_client(region=org.country)
    bytes_payload = payload.model_dump_json(by_alias=True).encode()
    msg = client.send_message(bytes_payload)
    log.info("Import message enqueued", extra={"message_id": msg.id, "import_id": payload.import_id})
