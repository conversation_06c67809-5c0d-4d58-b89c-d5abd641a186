from django.urls import path

from .views import (
    ImportListCreateView,
    ImportFileListCreateView,
    ImportRetrieveUpdateDeleteView,
    ImportedAssetCreateView,
    ImportedAssetRetrieveView,
    ImportedInspectionCreateView,
    ImportedInspectionRetrieveView,
)

urlpatterns = [
    path("imports", ImportListCreateView.as_view(), name="import-list-create"),
    path("imports/<uuid:id>", ImportRetrieveUpdateDeleteView.as_view(), name="import-retrieve-update-delete"),
    path("imports/<uuid:import_id>/files", ImportFileListCreateView.as_view(), name="import-file-list-create"),
    path("imports/<uuid:import_id>/assets", ImportedAssetCreateView.as_view(), name="imported-asset-create"),
    path("assets/<uuid:asset_uuid>/import", ImportedAssetRetrieveView.as_view(), name="imported-asset-retrieve"),
    path(
        "imports/<uuid:import_id>/inspections",
        ImportedInspectionCreateView.as_view(),
        name="imported-inspection-create",
    ),
    path(
        "inspections/<uuid:inspection_uuid>/import",
        ImportedInspectionRetrieveView.as_view(),
        name="imported-inspection-retrieve",
    ),
]
