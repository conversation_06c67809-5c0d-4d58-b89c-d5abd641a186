from datetime import datetime

from django.forms import model_to_dict
from django.http import Http404, HttpResponse, JsonResponse
from django.utils import timezone
from djangorestframework_camel_case.parser import Camel<PERSON>aseJ<PERSON>NParser
from djangorestframework_camel_case.render import Camel<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from drf_spectacular.utils import extend_schema
from rest_framework import serializers, status
from rest_framework.exceptions import NotFound, PermissionDenied, ValidationError
from rest_framework.fields import empty
from rest_framework.generics import (
    UpdateAPIView,
    ListAPIView,
    RetrieveAPIView,
    get_object_or_404,
    RetrieveUpdateAPIView,
)
from rest_framework.response import Response
from rest_framework.views import APIView

from api.actions.models import AuditList
from api.common.permissions import IsStandardUser, IsAuthenticated, HasInspectionAccess
from api.inspections.models import MapPointList
from api.recommendations import analytics
from api.recommendations.analytics import RepairRecommendationParty as Party
from api.recommendations.models import (
    Custom_Repair_Types as CustomRepairType,
    Custom_Repair_Values as CustomRepairValue,
    RepairRecommendation,
    RiskConsequence,
    RiskLikelihood,
)
from api.recommendations.tasks import tasks
from api.recommendations.serializers import (
    CustomRepairValueSerializer,
    RepairRecommendationSerializer,
    RiskConsequenceSerializer,
    RiskLikelyhoodSerializer,
    CustomRepairTypeSerializer,
    RepairPlanSerializer,
    RepairPlanActorSerializer,
    RepairPlanActorEnum,
)


CONTRACTOR_FIELDS = (
    "ready_to_view",
    "c_action_summary",
    "c_root_treatment",
    "c_cleaning_required",
    "c_patches_counted",
    "c_patching_details",
    "c_full_relining",
    "c_dig_up",
    "c_dig_up_details",
    "c_no_immediate_action",
    "c_other_action",
    "c_updated_at",
    "contractor_review",
)

OWNER_FIELDS = (
    "o_action_summary",
    "o_root_treatment",
    "o_cleaning_required",
    "o_patches_counted",
    "o_patching_details",
    "o_full_relining",
    "o_dig_up",
    "o_dig_up_details",
    "o_no_immediate_action",
    "o_other_action",
    "o_updated_at",
    "owner_review",
)

FREQUENCY_OPTIONS = (
    "None",
    "1 month",
    "2 months",
    "3 months",
    "4 months",
    "6 months",
    "1 year",
    "2 years",
    "3 years",
    "4 years",
    "5 years",
)


CSV_HEADERS = (
    "ID",
    "Location(Name)",
    "Upstream Node",
    "Downstream Node",
    "Direction",
    "Structural Grade",
    "Service Grade",
    "Date Captured",
    "Inspected Length",
    "Diameter",
    "Material",
    "Video File",
    "Folder Path",
    "Inspection Notes",
    "Workflow Status",
    "Inspection Frequency",
    "Asset ID",
    "Repair Notes",
    "No Actions",
    "Roots",
    "Clear Debris",
    "Patching Required",
    "Patches",
    "Digup Required",
    "Lining Required",
    "Likelihood Assessment",
    "Likelihood Comment",
    "Consquence Assessment",
    "Consequence Comment",
    "Creator",
)


def _build_custom_value_item(single_custom_type, inspection_id):
    custom_value_obj = CustomRepairValue.objects.filter(
        point_id=inspection_id,
        custom_Repair_Type=single_custom_type,
    ).first()

    custom_value = {}

    if single_custom_type.type == "boolean":
        custom_value["o_value_bool"] = custom_value_obj.o_value_bool if custom_value_obj else None
        custom_value["c_value_bool"] = custom_value_obj.c_value_bool if custom_value_obj else None

    if single_custom_type.type == "number":
        custom_value["o_value_number"] = custom_value_obj.o_value_number if custom_value_obj else None
        custom_value["c_value_number"] = custom_value_obj.c_value_number if custom_value_obj else None

    if single_custom_type.type == "text":
        custom_value["o_value_text"] = custom_value_obj.o_value_text if custom_value_obj else None
        custom_value["c_value_text"] = custom_value_obj.c_value_text if custom_value_obj else None

    custom_value["custom_type_id"] = single_custom_type.id
    custom_value["custom_type_name"] = single_custom_type.name

    return custom_value


class RepairRecommendationView(APIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    serializer_class = RepairRecommendationSerializer
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    def get_object(self, target_id):
        try:
            return RepairRecommendation.objects.get(target_id=target_id)
        except RepairRecommendation.DoesNotExist:
            raise Http404

    def serialize_repair_recommendation(self, inspection, userobj):
        upload_organization = inspection.associated_file.upload_org
        target_organization = inspection.associated_file.target_org

        org_custom_type = CustomRepairType.objects.filter(organisations=target_organization)

        completed_date = inspection.repair_completed_date

        if userobj.is_asset_owner():
            upload_for_same_org = False
            upload_for_different_org = None
            if upload_organization == target_organization:
                upload_for_same_org = True
        else:
            if upload_organization == target_organization:
                upload_for_same_org = True
                upload_for_different_org = False
            else:
                upload_for_same_org = False
                upload_for_different_org = True

        rr = self.get_object(inspection.id)

        if upload_for_same_org and userobj.is_asset_owner():
            rr.ready_to_view = False
            rr.save()
        data = self.serializer_class(rr).data
        data["is_asset_owner"] = userobj.is_asset_owner()
        data["upload_for_same_org"] = upload_for_same_org
        data["upload_for_different_org"] = upload_for_different_org

        custom_values = []

        for single_custom_type in org_custom_type:
            build_value = _build_custom_value_item(single_custom_type, inspection.id)
            if build_value:
                custom_values.append(build_value)

        data["custom"] = custom_values
        data["has_custom_type"] = len(custom_values) > 0
        data["frequency_options"] = FREQUENCY_OPTIONS
        data["repair_completed_date"] = completed_date
        data["likely"] = model_to_dict(rr.risk_likelihood) if rr.risk_likelihood else {}
        data["consequence"] = model_to_dict(rr.risk_consequence) if rr.risk_consequence else {}

        return data

    def get(self, request, inspection_id):
        userobj = request.user

        try:
            inspection = MapPointList.objects.get(pk=inspection_id)
        except MapPointList.DoesNotExist:
            return Response("Inspection not found", status=status.HTTP_404_NOT_FOUND)

        if inspection.associated_file is None:
            return Response("Associated File not found", status=status.HTTP_404_NOT_FOUND)

        data = self.serialize_repair_recommendation(inspection, userobj)

        return Response(data, status=status.HTTP_200_OK)

    def patch(self, request, inspection_id):
        try:
            inspection = MapPointList.objects.get(pk=inspection_id)
        except MapPointList.DoesNotExist:
            return Response("Inspection not found", status=status.HTTP_404_NOT_FOUND)

        if inspection.associated_file is None:
            return Response("Associated File not found", status=status.HTTP_404_NOT_FOUND)

        rr = self.get_object(inspection_id)
        serializer = self.serializer_class(rr, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        if serializer.validated_data is None or isinstance(serializer.validated_data, empty):
            return Response("Empty payload", status=status.HTTP_400_BAD_REQUEST)

        userobj = request.user
        validated_data = serializer.validated_data
        field_set = set(validated_data)

        now = timezone.now()

        if field_set.intersection(OWNER_FIELDS):
            if not userobj.is_asset_owner():
                raise PermissionDenied({"message": "Only Asset Owners can edit decision fields"})
        elif field_set.intersection(CONTRACTOR_FIELDS):
            if userobj.is_asset_owner():
                raise PermissionDenied({"message": "Only Contractors can edit recommendation fields"})

        if field_set.intersection(("inspection_frequency", "risk_consequence", "risk_likelihood")):
            if request.organisation != inspection.associated_file.target_org:
                raise PermissionDenied(
                    {
                        "message": "'inspection_frequency','risk_consequence','risk_likelihood' can only be updated by target organisation"
                    }
                )

        column_prefix = "o" if userobj.is_asset_owner() else "c"
        audit_description = "Update RR"

        if f"{column_prefix}_full_lining" in field_set:
            field_value = validated_data[f"{column_prefix}_full_lining"]
            rr.set_full_relining(field_value, owner=userobj.is_asset_owner())

        if f"{column_prefix}_no_immediate_action" in field_set:
            field_value = validated_data[f"{column_prefix}_no_immediate_action"]
            rr.set_no_immediate_action(field_value, owner=userobj.is_asset_owner())

        if f"{column_prefix}_patches_counted" in field_set:
            field_value = validated_data[f"{column_prefix}_patches_counted"]
            # TODO move validation to serializer
            if field_value != "" and not isinstance(field_value, int):
                return serializers.ValidationError(f"{column_prefix}_patches_counted must be a number")
            elif field_value < 0:
                return serializers.ValidationError(f"{column_prefix}_patches_counted must not be a negative")
            rr.set_patches_counted(field_value, owner=userobj.is_asset_owner())

        if "risk_likelihood" in field_set:
            # TODO move validation to serializer
            field_value = validated_data["risk_likelihood"]
            if field_value.value not in range(7):
                raise serializers.ValidationError("Invalid value for risk_likelihood_id")
            column_prefix = "risk"
            audit_description = "Update RR Likelyhood"

        if "risk_consequence" in field_set:
            # TODO move validation to serializer
            field_value = validated_data["risk_consequence"]
            if field_value.value not in range(7):
                raise serializers.ValidationError("Invalid value for risk_consequence_id")
            column_prefix = "risk"
            audit_description = "Update RR Consequence"

        if comment_fields := field_set.intersection(("consequence_comment", "likelihood_comment")):
            column_prefix = "risk"
            # picks `consequence` if both fields are updated as only a single Audit is created.
            # would require multiple AuditList.create calls to resolve this.
            field_name = min(comment_fields)
            audit_description = f"Update RR {field_name.split('_')[0].capitalize()}"

        for field_name, field_value in validated_data.items():
            setattr(rr, field_name, field_value)
        setattr(rr, f"{column_prefix}_updated_at", now)
        setattr(rr, f"{column_prefix}_updated_by", request.user)

        rr.save()

        AuditList.objects.create(
            event_type="Update",
            table="repairrecommendation",
            row_id=inspection_id,
            column=min(field_set),
            description=audit_description,
            date_of_modification=now,
            user=userobj,
        )

        data = self.serialize_repair_recommendation(inspection, request.user)

        party = Party.OWNER if userobj.is_asset_owner() else Party.CONTRACTOR
        analytics.send_repair_recommendation_updated_event(field_set, rr, party)

        return Response(data, status=status.HTTP_200_OK)


class CustomRepairValueView(RetrieveUpdateAPIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    serializer_class = CustomRepairValueSerializer
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    http_method_names = ["get", "patch"]
    pagination_class = None

    def _update_parent_recommendation(self, fieldname, target_id, updated_by):
        rr = RepairRecommendation.objects.filter(target_id=target_id).first()
        if rr is None:
            raise Http404

        if fieldname.startswith("c_"):
            setattr(rr, "c_updated_at", timezone.now())
            setattr(rr, "c_updated_by", updated_by)
        elif fieldname.startswith("o_"):
            setattr(rr, "o_updated_at", timezone.now())
            setattr(rr, "o_updated_by", updated_by)
        rr.save()

    def validate_custom_repair_value(self, custom_repair_type, field_name, value):
        mismatch = False

        if custom_repair_type.type == "boolean":
            if field_name not in ["o_value_bool", "c_value_bool"]:
                mismatch = True

        if custom_repair_type.type == "text":
            if field_name not in ["o_value_text", "c_value_text"]:
                mismatch = True

        if custom_repair_type.type == "number":
            if field_name not in ["o_value_number", "c_value_number"]:
                mismatch = True
            if not isinstance(value, int):
                try:
                    value = int(value)
                except ValueError:
                    return Response(
                        {"error": "Value must be a number"},
                        status=status.HTTP_404_NOT_FOUND,
                    )

            if value < 0:
                return Response(
                    {"error": "Value must not be negative"},
                    status=status.HTTP_404_NOT_FOUND,
                )

        if mismatch:
            raise serializers.ValidationError(
                f"{field_name} is not valid for custom repair {custom_repair_type.name} with type {custom_repair_type.type}"
            )

    @extend_schema(responses={200: CustomRepairValueSerializer(many=True)})
    def get(self, request, inspection_id):
        custom_repair_values = CustomRepairValue.objects.filter(point__id=inspection_id)
        if len(custom_repair_values) == 0:
            return Response(
                "No CustomRepairValues found for inspection_id " + str(inspection_id), status=status.HTTP_404_NOT_FOUND
            )

        serializer = self.serializer_class(custom_repair_values, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def patch(self, request, inspection_id):
        inspection = MapPointList.objects.filter(id=inspection_id).first()
        if inspection is None:
            raise serializers.ValidationError("inspection is required")

        custom_repair_type = request.data.get("custom_repair_type_id")
        org_id = inspection.associated_file.target_org.id
        custom_repair_type = CustomRepairType.objects.filter(id=custom_repair_type, organisations=org_id).first()

        if custom_repair_type is None:
            raise serializers.ValidationError("custom_repair_type not found for this organisation")

        custom_repair_value = CustomRepairValue.objects.filter(
            custom_Repair_Type=custom_repair_type, point_id=inspection
        ).first()

        data = request.data
        del data["custom_repair_type_id"]

        field_name = list(data.keys())[0]
        self.validate_custom_repair_value(
            custom_repair_type=custom_repair_type,
            field_name=field_name,
            value=data[field_name],
        )

        description = f"Update {field_name} for {custom_repair_type.name} on {inspection.id}"
        event_type = "Update"

        # if custom repair value exists, update it, otherwise create it
        if custom_repair_value:
            serializer = self.serializer_class(custom_repair_value, data=data, partial=True)
            serializer.is_valid(raise_exception=True)
            instance = serializer.save()
        else:
            data["point"] = inspection.id
            data["custom_Repair_Type"] = custom_repair_type.id

            serializer = self.serializer_class(data=data)
            serializer.is_valid(raise_exception=True)
            instance = serializer.save()

            description = f"Create {field_name} for {custom_repair_type.name} on {inspection.id}"
            event_type = "Create"

        # update parent recommendation
        self._update_parent_recommendation(field_name, inspection_id, request.user)

        AuditList.objects.create(
            event_type=event_type,
            table="custom_repair_values",
            row_id=instance.id,
            column=field_name,
            description=description,
            date_of_modification=timezone.now(),
            user=request.user,
        )

        return Response(serializer.data, status=status.HTTP_200_OK)


class RepairCompletedView(APIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    @extend_schema(request=dict, responses={204: bool})
    def patch(self, request, inspection_id):
        completed_date = request.data["repair_completed_date"]

        try:
            inspection = MapPointList.objects.get(id=inspection_id)
        except MapPointList.DoesNotExist:
            return NotFound("Inspection not found")

        setattr(inspection, "repair_completed_date", completed_date)
        inspection.save()

        description = "Update repair completed date "
        now = timezone.now()
        AuditList.objects.create(
            event_type="Update",
            table="mappointlist",
            row_id=inspection_id,
            column="repair_completed_date",
            description=description,
            date_of_modification=now,
            user=request.user,
        )

        return Response(True, status=status.HTTP_204_NO_CONTENT)


class RepairCustomTypeList(ListAPIView):
    serializer_class = CustomRepairTypeSerializer
    permission_classes = [IsAuthenticated, IsStandardUser]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]
    queryset = CustomRepairType.objects.all()
    pagination_class = None

    def get_queryset(self, org_id):
        if org_id is None:
            raise ValidationError("No organisation id provided")
        return CustomRepairType.objects.filter(organisations=org_id)

    def get(self, request, inspection_id):
        inspection = MapPointList.objects.filter(id=inspection_id).first()

        if not inspection_id:
            return NotFound("Inspection not found")

        org_id = inspection.associated_file.target_org.id or inspection.inspection.asset.organisation.id

        custom_types = self.get_queryset(org_id)
        serializer = self.serializer_class(custom_types, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


class RepairCopySuggestionView(APIView):
    permission_classes = [IsAuthenticated, IsStandardUser]

    @extend_schema(request=dict, responses={status.HTTP_200_OK: dict})
    def post(self, request, inspection_id):
        userobj = request.user
        copy_suggestion = request.data["copySuggestion"]

        try:
            rr = RepairRecommendation.objects.get(target_id=inspection_id)
        except RepairRecommendation.DoesNotExist:
            raise NotFound("RepairRecommendation not found")

        try:
            custom_rr = CustomRepairValue.objects.filter(point_id=inspection_id)
        except CustomRepairValue.DoesNotExist:
            custom_rr = None

        if rr:
            suggest_relining = getattr(rr, "full_relining")
            suggest_action_summary = getattr(rr, "action_summary")
            suggest_root_treatment = getattr(rr, "root_treatment")
            suggest_cleaning_required = getattr(rr, "cleaning_required")
            suggest_patches_counted = getattr(rr, "patches_counted")
            suggest_patching_details = getattr(rr, "patching_details")
            suggest_dig_up = getattr(rr, "dig_up")
            suggest_dig_up_details = getattr(rr, "dig_up_details")
            suggest_no_immediate_action = getattr(rr, "no_immediate_action")
            suggest_other_action = getattr(rr, "other_action")

            c_suggest_relining = getattr(rr, "c_full_relining")
            c_suggest_action_summary = getattr(rr, "c_action_summary")
            c_suggest_root_treatment = getattr(rr, "c_root_treatment")
            c_suggest_cleaning_required = getattr(rr, "c_cleaning_required")
            c_suggest_patches_counted = getattr(rr, "c_patches_counted")
            c_suggest_patching_details = getattr(rr, "c_patching_details")
            c_suggest_dig_up = getattr(rr, "c_dig_up")
            c_suggest_dig_up_details = getattr(rr, "c_dig_up_details")
            c_suggest_no_immediate_action = getattr(rr, "c_no_immediate_action")
            c_suggest_other_action = getattr(rr, "c_other_action")

            if copy_suggestion == "copyVaparSuggestion" and userobj.is_asset_owner():
                analytics.send_repair_recommendation_applied_event(rr, Party.VAPAR, Party.OWNER, datetime.utcnow())

                setattr(rr, "o_root_treatment", suggest_root_treatment)
                setattr(rr, "o_cleaning_required", suggest_cleaning_required)
                setattr(rr, "o_patches_counted", suggest_patches_counted)
                setattr(rr, "o_patching_details", suggest_patching_details)
                setattr(rr, "o_dig_up", suggest_dig_up)
                setattr(rr, "o_no_immediate_action", suggest_no_immediate_action)
                setattr(rr, "o_dig_up_details", suggest_dig_up_details)
                setattr(rr, "o_action_summary", suggest_action_summary)
                setattr(rr, "o_relining", suggest_relining)
                setattr(rr, "o_other_action", suggest_other_action)
                setattr(rr, "o_updated_at", datetime.today())
                setattr(rr, "o_updated_by", request.user)
                rr.save()

                return Response(model_to_dict(rr), status=status.HTTP_200_OK)

            if copy_suggestion == "copyVaparSuggestion" and not userobj.is_asset_owner():
                analytics.send_repair_recommendation_applied_event(rr, Party.VAPAR, Party.CONTRACTOR, datetime.utcnow())

                setattr(rr, "c_root_treatment", suggest_root_treatment)
                setattr(rr, "c_cleaning_required", suggest_cleaning_required)
                setattr(rr, "c_patches_counted", suggest_patches_counted)
                setattr(rr, "c_patching_details", suggest_patching_details)
                setattr(rr, "c_dig_up", suggest_dig_up)
                setattr(rr, "c_no_immediate_action", suggest_no_immediate_action)
                setattr(rr, "c_dig_up_details", suggest_dig_up_details)
                setattr(rr, "c_action_summary", suggest_action_summary)
                setattr(rr, "c_relining", suggest_relining)
                setattr(rr, "c_other_action", suggest_other_action)
                setattr(rr, "c_updated_at", datetime.today())
                setattr(rr, "c_updated_by", request.user)
                rr.save()

                return Response(model_to_dict(rr), status=status.HTTP_200_OK)

            if copy_suggestion == "copyContractorSuggestion":
                analytics.send_repair_recommendation_applied_event(rr, Party.CONTRACTOR, Party.OWNER, datetime.utcnow())

                setattr(rr, "o_root_treatment", c_suggest_root_treatment)
                setattr(rr, "o_cleaning_required", c_suggest_cleaning_required)
                setattr(rr, "o_patches_counted", c_suggest_patches_counted)
                setattr(rr, "o_patching_details", c_suggest_patching_details)
                setattr(rr, "o_dig_up", c_suggest_dig_up)
                setattr(rr, "o_no_immediate_action", c_suggest_no_immediate_action)
                setattr(rr, "o_dig_up_details", c_suggest_dig_up_details)
                setattr(rr, "o_action_summary", c_suggest_action_summary)
                setattr(rr, "o_relining", c_suggest_relining)
                setattr(rr, "o_other_action", c_suggest_other_action)
                setattr(rr, "o_other_action", c_suggest_other_action)

                if custom_rr:
                    for crr in custom_rr:
                        c_value_text = getattr(crr, "c_value_text")
                        c_value_bool = getattr(crr, "c_value_bool")
                        c_value_number = getattr(crr, "c_value_number")

                        setattr(crr, "o_value_text", c_value_text)
                        setattr(crr, "o_value_bool", c_value_bool)
                        setattr(crr, "o_value_number", c_value_number)
                        crr.save()

                setattr(rr, "o_updated_at", datetime.today())
                setattr(rr, "o_updated_by", request.user)
                rr.save()

                description = copy_suggestion + " repair recommendation"
                now = timezone.now()
                AuditList.objects.create(
                    event_type="Update",
                    table="repairrecommendation",
                    row_id=inspection_id,
                    column="Multi",
                    description=description,
                    date_of_modification=now,
                    user=request.user,
                )

                return Response(model_to_dict(rr), status=status.HTTP_200_OK)


class BulkRepairRecommendationsView(APIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    @extend_schema(request=dict, responses={status.HTTP_201_CREATED: list[dict]})
    def post(self, request):
        inspection_ids = []
        if "inspection_ids" in request.data:
            inspection_ids = request.data["inspection_ids"]

        outlist, error = tasks.run_many_repair_recommendations(inspection_ids, request.user)

        if error:
            Response(
                {"error": "Failed to generate repair recommendations"},
                status.HTTP_403_FORBIDDEN,
            )

        return Response(outlist, status.HTTP_200_OK)


class RepairRecommendationCSVView(APIView):
    permission_classes = (IsAuthenticated, IsStandardUser)
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    @extend_schema(request=dict, responses={(status.HTTP_201_CREATED, "text/csv"): str})
    def post(self, request):
        userobj = request.user

        inspection_ids = []
        for inspection_id in request.data["inspection_ids"]:
            try:
                int_inspection_id = int(inspection_id)
                inspection_ids.append(int_inspection_id)
            except Exception:
                continue

        org_name = request.organisation.full_name
        user_email = str(userobj.email)

        target_org = MapPointList.objects.get(id=inspection_ids[0]).associated_file.target_org
        custom_repair_header = list(
            CustomRepairType.objects.filter(organisations=target_org).values_list("name", flat=True)
        )

        headerlist = list(CSV_HEADERS)
        headerlist[25:25] = custom_repair_header

        if len(inspection_ids) > 100:
            task = tasks.repair_recommendations_csv_task.delay(
                inspection_ids,
                org_name,
                user_email,
                request.organisation.is_asset_owner,
                headerlist,
                custom_repair_header,
            )
            return JsonResponse({"task_id": task.id}, status=202)

        csvfile = tasks.generate_repair_recommendation_csv(
            inspection_ids,
            org_name,
            request.organisation.is_asset_owner,
            headerlist,
            custom_repair_header,
            filename="repairsrecommend.csv",
        )

        description = f"Repair recommendation CSV requested by {request.user.first_name} {request.user.last_name}"

        AuditList.objects.create(
            event_type="Export RR",
            table="MappointList",
            column="Multi",
            row_id=inspection_ids[0] if len(inspection_ids) else None,
            description=description,
            date_of_modification=timezone.now(),
            user=request.user,
        )

        with open(csvfile, "r", encoding="utf-8") as fp:
            response = HttpResponse(
                fp.read(),
                content_type="text/csv",
                headers={"Content-Disposition": 'attachment; filename="repairsrecommend.csv"'},
            )

        return response


class RiskLikelyhoodView(UpdateAPIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    queryset = RiskLikelihood.objects.all()
    serializer_class = RiskLikelyhoodSerializer


class RiskConsequenceView(UpdateAPIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    queryset = RiskConsequence.objects.all()
    serializer_class = RiskConsequenceSerializer


class RepairPlanView(RetrieveAPIView):
    permission_classes = [IsAuthenticated, IsStandardUser, HasInspectionAccess]
    queryset = RepairRecommendation.objects.all()
    serializer_class = RepairPlanSerializer
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    def get_object(self, request, inspection_id):
        repairplan = get_object_or_404(RepairRecommendation, target__inspection__uuid=inspection_id)
        self.check_object_permissions(request, repairplan.target.inspection)
        return repairplan

    def get(self, request, inspection_id, *args, **kwargs):
        repairplan = self.get_object(request, inspection_id)
        serializer = self.serializer_class(repairplan)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def post(self, request, inspection_id, *args, **kwargs):
        serializer = self.serializer_class(
            data=request.data, context={"inspection_id": inspection_id, "request": request}
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response(serializer.data, status=status.HTTP_201_CREATED)

    def patch(self, request, inspection_id, *args, **kwargs):
        serializer = self.serializer_class(
            instance=self.get_object(request, inspection_id),
            data=request.data,
            partial=True,
            context={"request": request},
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response(serializer.data, status=status.HTTP_200_OK)


class RepairPlanItemListView(APIView):
    permission_classes = [IsAuthenticated, IsStandardUser, HasInspectionAccess]
    queryset = RepairRecommendation.objects.all()
    serializer_class = RepairPlanActorSerializer
    parser_classes = [CamelCaseJSONParser]
    renderer_classes = [CamelCaseJSONRenderer]

    def get_object(self, request, inspection_id, actor):
        repairplan = get_object_or_404(RepairRecommendation, target__inspection__uuid=inspection_id)
        self.check_object_permissions(request, repairplan.target.inspection)
        if actor not in iter(RepairPlanActorEnum):
            raise ValidationError('Actor not one of "vapar", "contractor", or "owner"')
        return repairplan

    def get(self, request, inspection_id, actor, *args, **kwargs):
        repairplan = self.get_object(request, inspection_id, actor)
        serializer = self.serializer_class(repairplan, context={"actor": actor, "inspection_id": inspection_id})
        return Response(serializer.data, status=status.HTTP_200_OK)

    def patch(self, request, inspection_id, actor, *args, **kwargs):
        repairplan = self.get_object(request, inspection_id, actor)

        serializer = self.serializer_class(
            repairplan, data=request.data, context={"actor": actor, "inspection_id": inspection_id}
        )
        serializer.is_valid(raise_exception=True)

        repairplan = serializer.update(repairplan, serializer.validated_data)
        response_data = serializer.to_representation(repairplan)
        return Response(response_data, status=status.HTTP_200_OK)
