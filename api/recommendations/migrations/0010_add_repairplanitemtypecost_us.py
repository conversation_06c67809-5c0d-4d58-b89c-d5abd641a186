from django.db import migrations

from api.common.enums import RepairPlanItemTypeEnum


def insert_repair_item_types(apps, schema_editor):
    RepairPlanItemTypeCost = apps.get_model("recommendations", "RepairPlanItemTypeCost")
    RepairPlanItemType = apps.get_model("recommendations", "RepairPlanItemType")

    # Create PATCH price table
    repair_item_type = RepairPlanItemType.objects.get(name=RepairPlanItemTypeEnum.PATCH.value)
    RepairPlanItemTypeCost.objects.bulk_create(
        [
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=297, cost_lookup=10, region="US"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=309, cost_lookup=12, region="US"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=322, cost_lookup=18, region="US"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=336, cost_lookup=21, region="US"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=361, cost_lookup=24, region="US"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=385, cost_lookup=27, region="US"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=424, cost_lookup=30, region="US"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=449, cost_lookup=36, region="US"),
        ],
    )

    # Create LINING price table
    repair_item_type = RepairPlanItemType.objects.get(name=RepairPlanItemTypeEnum.LINING.value)
    RepairPlanItemTypeCost.objects.bulk_create(
        [
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=165, cost_lookup=18, region="US"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=213, cost_lookup=21, region="US"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=243, cost_lookup=24, region="US"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=320, cost_lookup=27, region="US"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=378, cost_lookup=30, region="US"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=408, cost_lookup=36, region="US"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=465, cost_lookup=66, region="US"),
        ],
    )

    # Create JUNCTION price table
    # NOTE: This is currently a custom repair type.
    # repair_item_type = RepairPlanItemType.objects.get(name=RepairPlanItemTypeEnum.JUNCTION.value)
    # RepairPlanItemTypeCost.objects.bulk_create(
    #     [
    #         RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=503, cost_alternative=2208, cost_lookup=150, region="US"),
    #     ],
    # )

    # Create CLEANING price table
    repair_item_type = RepairPlanItemType.objects.get(name=RepairPlanItemTypeEnum.CLEANING.value)
    RepairPlanItemTypeCost.objects.bulk_create(
        [
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=50, cost_lookup=66, region="US"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=61, cost_lookup=72, region="US"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=99, cost_lookup=84, region="US"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=99, cost_lookup=90, region="US"),
        ],
    )

    # Create ROOT_REMOVAL price table
    repair_item_type = RepairPlanItemType.objects.get(name=RepairPlanItemTypeEnum.ROOT_REMOVAL.value)
    RepairPlanItemTypeCost.objects.bulk_create(
        [
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=50, cost_lookup=66, region="US"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=61, cost_lookup=72, region="US"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=99, cost_lookup=84, region="US"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=99, cost_lookup=90, region="US"),
        ],
    )

    RepairPlanItemTypeCost.objects.bulk_create(
        [
            RepairPlanItemTypeCost(
                repair_item_type=RepairPlanItemType.objects.get(name=RepairPlanItemTypeEnum.DIG_UP.value),
                cost_base=20000,
                cost_lookup=None,
                region="US",
            ),
        ],
    )


class Migration(migrations.Migration):

    dependencies = [
        ("recommendations", "0009_add_repairplanitemtypecost_uk"),
    ]

    operations = [
        migrations.RunPython(code=insert_repair_item_types, reverse_code=migrations.RunPython.noop),
    ]
