# Generated by Django 4.1.2 on 2025-04-08 01:37

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("recommendations", "0004_add_custom_types_02"),
    ]

    operations = [
        migrations.CreateModel(
            name="RepairPlanItemType",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("Patch", "PATCH"),
                            ("Cleaning", "CLEANING"),
                            ("Lining", "LINING"),
                            ("Root Removal", "ROOT_REMOVAL"),
                            ("Dig Up", "DIG_UP"),
                            ("No Action", "NO_ACTION"),
                            ("Custom", "CUSTOM"),
                            ("Other", "OTHER"),
                        ],
                        max_length=200,
                    ),
                ),
                ("metadata", models.JSONField()),
            ],
            options={
                "db_table": "service_repair_plan_item_type",
            },
        ),
    ]
