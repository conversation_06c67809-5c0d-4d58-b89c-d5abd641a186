from django.db import migrations

from api.common.enums import RepairPlanItemTypeEnum


def insert_repair_item_types(apps, schema_editor):
    RepairPlanItemTypeCost = apps.get_model("recommendations", "RepairPlanItemTypeCost")
    RepairPlanItemType = apps.get_model("recommendations", "RepairPlanItemType")

    # Create PATCH price table
    repair_item_type = RepairPlanItemType.objects.get(name=RepairPlanItemTypeEnum.PATCH.value)
    RepairPlanItemTypeCost.objects.bulk_create(
        [
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=100, cost_lookup=100, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=100, cost_lookup=150, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=100, cost_lookup=225, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=100, cost_lookup=250, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=100, cost_lookup=300, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=100, cost_lookup=375, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=100, cost_lookup=400, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=100, cost_lookup=450, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=100, cost_lookup=525, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=100, cost_lookup=600, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=100, cost_lookup=675, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=100, cost_lookup=750, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=100, cost_lookup=900, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=100, cost_lookup=1050, region="UK"),
        ],
    )

    # Create LINING price table
    repair_item_type = RepairPlanItemType.objects.get(name=RepairPlanItemTypeEnum.LINING.value)
    RepairPlanItemTypeCost.objects.bulk_create(
        [
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=850, cost_lookup=100, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=850, cost_lookup=150, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=850, cost_lookup=225, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=850, cost_lookup=250, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=850, cost_lookup=300, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=850, cost_lookup=375, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=850, cost_lookup=400, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=850, cost_lookup=450, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=850, cost_lookup=525, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=850, cost_lookup=600, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=850, cost_lookup=675, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=850, cost_lookup=750, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=850, cost_lookup=900, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=850, cost_lookup=1050, region="UK"),
        ],
    )

    # Create JUNCTION price table
    # NOTE: This is currently a custom repair type.
    # repair_item_type = RepairPlanItemType.objects.get(name=RepairPlanItemTypeEnum.JUNCTION.value)
    # RepairPlanItemTypeCost.objects.bulk_create(
    #     [
    #         RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=503, cost_alternative=2208, cost_lookup=150, region="UK"),
    #         RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=503, cost_alternative=2338, cost_lookup=225, region="UK"),
    #         RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=503, cost_alternative=2403, cost_lookup=250, region="UK"),
    #         RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=535, cost_alternative=2708, cost_lookup=300, region="UK"),
    #         RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=595, cost_alternative=2846, cost_lookup=375, region="UK"),
    #         RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=655, cost_alternative=2984, cost_lookup=400, region="UK"),
    #         RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=715, cost_alternative=3122, cost_lookup=450, region="UK"),
    #         RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=775, cost_alternative=3260, cost_lookup=525, region="UK"),
    #         RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=835, cost_alternative=3398, cost_lookup=600, region="UK"),
    #         RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=895, cost_alternative=3536, cost_lookup=675, region="UK"),
    #         RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=955, cost_alternative=3674, cost_lookup=750, region="UK"),
    #         RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=1015, cost_alternative=3812, cost_lookup=900, region="UK"),
    #         RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=1075, cost_alternative=3950, cost_lookup=1050, region="UK"),
    #     ],
    # )

    # Create CLEANING price table
    repair_item_type = RepairPlanItemType.objects.get(name=RepairPlanItemTypeEnum.CLEANING.value)
    RepairPlanItemTypeCost.objects.bulk_create(
        [
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=2.5, cost_lookup=150, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=5, cost_lookup=225, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=5, cost_lookup=250, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=5, cost_lookup=300, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=5, cost_lookup=375, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=5, cost_lookup=400, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=5, cost_lookup=450, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=5, cost_lookup=525, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=5, cost_lookup=600, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=8, cost_lookup=675, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=8, cost_lookup=750, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=8, cost_lookup=900, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=8, cost_lookup=1050, region="UK"),
        ],
    )

    # Create ROOT_REMOVAL price table
    repair_item_type = RepairPlanItemType.objects.get(name=RepairPlanItemTypeEnum.ROOT_REMOVAL.value)
    RepairPlanItemTypeCost.objects.bulk_create(
        [
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=7, cost_lookup=150, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=9, cost_lookup=225, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=10, cost_lookup=250, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=14, cost_lookup=300, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=16, cost_lookup=375, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=17, cost_lookup=400, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=21, cost_lookup=450, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=25, cost_lookup=525, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=31, cost_lookup=600, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=34, cost_lookup=675, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=39, cost_lookup=750, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=47, cost_lookup=900, region="UK"),
            RepairPlanItemTypeCost(repair_item_type=repair_item_type, cost_base=55, cost_lookup=1050, region="UK"),
        ],
    )

    RepairPlanItemTypeCost.objects.bulk_create(
        [
            RepairPlanItemTypeCost(
                repair_item_type=RepairPlanItemType.objects.get(name=RepairPlanItemTypeEnum.DIG_UP.value),
                cost_base=5000,
                cost_lookup=None,
                region="UK",
            ),
        ],
    )


class Migration(migrations.Migration):

    dependencies = [
        ("recommendations", "0008_add_repairplanitemtypecost"),
    ]

    operations = [
        migrations.RunPython(code=insert_repair_item_types, reverse_code=migrations.RunPython.noop),
    ]
