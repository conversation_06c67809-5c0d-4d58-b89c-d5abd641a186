# Generated by Django 4.1.2 on 2024-03-14 04:44

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("organisations", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("inspections", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Custom_Repair_Types",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("type", models.CharField(max_length=100)),
                (
                    "organisations",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="organisations.organisations",
                    ),
                ),
            ],
            options={
                "db_table": "service_custom_repair_types",
            },
        ),
        migrations.CreateModel(
            name="RiskConsequence",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(blank=True, max_length=200, null=True)),
                ("value", models.IntegerField(blank=True, null=True)),
            ],
            options={
                "db_table": "service_risk_consequence",
            },
        ),
        migrations.CreateModel(
            name="RiskLikelihood",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(blank=True, max_length=200, null=True)),
                ("value", models.IntegerField(blank=True, null=True)),
            ],
            options={
                "db_table": "service_risk_likelihood",
            },
        ),
        migrations.CreateModel(
            name="RepairRecommendation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("inspection_parameters", models.TextField(blank=True, null=True)),
                ("action_summary", models.TextField(blank=True, null=True)),
                ("root_treatment", models.BooleanField(default=False)),
                ("cleaning_required", models.BooleanField(default=False)),
                ("patches_counted", models.PositiveIntegerField(blank=True, null=True)),
                ("patching_details", models.BooleanField(default=False)),
                ("full_relining", models.BooleanField(default=False)),
                ("dig_up", models.BooleanField(default=False)),
                ("dig_up_details", models.TextField(blank=True, null=True)),
                ("no_immediate_action", models.BooleanField(default=False)),
                ("other_action", models.BooleanField(blank=True, default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(blank=True, null=True)),
                ("c_action_summary", models.TextField(blank=True, null=True)),
                ("c_root_treatment", models.BooleanField(default=False)),
                ("c_cleaning_required", models.BooleanField(default=False)),
                (
                    "c_patches_counted",
                    models.PositiveIntegerField(blank=True, null=True),
                ),
                ("c_patching_details", models.BooleanField(default=False)),
                ("c_full_relining", models.BooleanField(default=False)),
                ("c_dig_up", models.BooleanField(default=False)),
                ("c_dig_up_details", models.TextField(blank=True, null=True)),
                ("c_no_immediate_action", models.BooleanField(default=False)),
                ("c_other_action", models.BooleanField(default=False)),
                (
                    "c_updated_at",
                    models.DateTimeField(blank=True, default=None, null=True),
                ),
                ("contractor_review", models.TextField(blank=True, null=True)),
                ("ready_to_view", models.BooleanField(default=True)),
                ("o_action_summary", models.TextField(blank=True, null=True)),
                ("o_root_treatment", models.BooleanField(default=False)),
                ("o_cleaning_required", models.BooleanField(default=False)),
                (
                    "o_patches_counted",
                    models.PositiveIntegerField(blank=True, null=True),
                ),
                ("o_patching_details", models.BooleanField(default=False)),
                ("o_full_relining", models.BooleanField(default=False)),
                ("o_dig_up", models.BooleanField(default=False)),
                ("o_dig_up_details", models.TextField(blank=True, null=True)),
                ("o_no_immediate_action", models.BooleanField(default=False)),
                ("o_other_action", models.BooleanField(default=False)),
                ("o_updated_at", models.DateTimeField(blank=True, null=True)),
                ("owner_review", models.TextField(blank=True, null=True)),
                ("inspection_frequency", models.TextField(blank=True, null=True)),
                ("likelihood_comment", models.TextField(blank=True, null=True)),
                ("consequence_comment", models.TextField(blank=True, null=True)),
                (
                    "risk_updated_at",
                    models.DateTimeField(blank=True, default=None, null=True),
                ),
                (
                    "c_updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="c_updated_by_set",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "o_updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="o_updated_by_set",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "risk_consequence",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="recommendations.riskconsequence",
                    ),
                ),
                (
                    "risk_likelihood",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="recommendations.risklikelihood",
                    ),
                ),
                (
                    "risk_updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="risk_updated_by_set",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "target",
                    models.OneToOneField(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="inspections.mappointlist",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="r_updated_by_set",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "service_repairrecommendation",
            },
        ),
        migrations.CreateModel(
            name="Custom_Repair_Values",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "c_value_text",
                    models.CharField(blank=True, max_length=200, null=True),
                ),
                ("c_value_number", models.IntegerField(blank=True, null=True)),
                ("c_value_bool", models.BooleanField(blank=True, null=True)),
                (
                    "o_value_text",
                    models.CharField(blank=True, max_length=200, null=True),
                ),
                ("o_value_number", models.IntegerField(blank=True, null=True)),
                ("o_value_bool", models.BooleanField(blank=True, null=True)),
                (
                    "custom_Repair_Type",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="recommendations.custom_repair_types",
                    ),
                ),
                (
                    "point",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="inspections.mappointlist",
                    ),
                ),
            ],
            options={
                "db_table": "service_custom_repair_values",
            },
        ),
    ]
