# Generated by Django 4.1.2 on 2024-07-30 03:06

from django.db import migrations, connection


def reset_sequence(apps, schema_editor):
    with connection.cursor() as cursor:
        cursor.execute(
            "SELECT setval(pg_get_serial_sequence('service_custom_repair_types', 'id'), COALESCE(MAX(id), 1), max(id) IS NOT null) FROM service_custom_repair_types"
        )


def create_utility_custom_repair_type(apps, schema_editor):
    Organisations = apps.get_model("organisations", "Organisations")
    Custom_Repair_Types = apps.get_model("recommendations", "Custom_Repair_Types")

    org_names = ["City of Geelong", "Knox City Council"]
    organisations = Organisations.objects.filter(full_name__in=org_names)

    Custom_Repair_Types.objects.bulk_create(
        [
            Custom_Repair_Types(organisations=organisation, name="Utility", type="boolean")
            for organisation in organisations
        ]
    )


def create_reclean_custom_repair_type(apps, schema_editor):
    Organisations = apps.get_model("organisations", "Organisations")
    Custom_Repair_Types = apps.get_model("recommendations", "Custom_Repair_Types")

    organisations = Organisations.objects.filter(full_name="Northumbrian Water")

    Custom_Repair_Types.objects.bulk_create(
        [
            Custom_Repair_Types(organisations=organisation, name="Re-clean", type="boolean")
            for organisation in organisations
        ]
    )


def create_resurvey_custom_repair_type(apps, schema_editor):
    Organisations = apps.get_model("organisations", "Organisations")
    Custom_Repair_Types = apps.get_model("recommendations", "Custom_Repair_Types")

    organisations = Organisations.objects.filter(full_name="Northumbrian Water")

    Custom_Repair_Types.objects.bulk_create(
        [
            Custom_Repair_Types(organisations=organisation, name="Re-survey", type="boolean")
            for organisation in organisations
        ]
    )


class Migration(migrations.Migration):

    dependencies = [
        ("recommendations", "0002_auto_20240730_0306"),
    ]

    operations = [
        migrations.RunPython(code=reset_sequence, reverse_code=migrations.RunPython.noop),
        migrations.RunPython(code=create_utility_custom_repair_type, reverse_code=migrations.RunPython.noop),
        migrations.RunPython(code=create_reclean_custom_repair_type, reverse_code=migrations.RunPython.noop),
        migrations.RunPython(code=create_resurvey_custom_repair_type, reverse_code=migrations.RunPython.noop),
    ]
