from django.db import migrations

from api.common.enums import RepairPlanItemTypeEnum


def insert_repair_item_types(apps, schema_editor):
    RepairPlanItemType = apps.get_model("recommendations", "RepairPlanItemType")

    RepairPlanItemType.objects.bulk_create(
        [
            RepairPlanItemType(name=RepairPlanItemTypeEnum.PATCH.value, metadata=[{"key": "Count", "value": 0}]),
            RepairPlanItemType(name=RepairPlanItemTypeEnum.CLEANING.value, metadata=[]),
            RepairPlanItemType(name=RepairPlanItemTypeEnum.LINING.value, metadata=[]),
            RepairPlanItemType(name=RepairPlanItemTypeEnum.ROOT_REMOVAL.value, metadata=[]),
            RepairPlanItemType(name=RepairPlanItemTypeEnum.DIG_UP.value, metadata=[{"key": "Details", "value": None}]),
            RepairPlanItemType(name=RepairPlanItemTypeEnum.NO_ACTION.value, metadata=[]),
            RepairPlanItemType(name=RepairPlanItemTypeEnum.CUSTOM.value, metadata=[]),
            RepairPlanItemType(name=RepairPlanItemTypeEnum.OTHER.value, metadata=[]),
        ]
    )


class Migration(migrations.Migration):

    dependencies = [
        ("recommendations", "0005_repairplanitemtype"),
    ]

    operations = [
        migrations.RunPython(code=insert_repair_item_types, reverse_code=migrations.RunPython.noop),
    ]
