# Generated by Django 4.1.2 on 2025-04-10 01:29

from django.db import migrations, models
import django.db.models.deletion
import django_countries.fields


class Migration(migrations.Migration):

    dependencies = [
        ("recommendations", "0006_add_repairplanitemtypes"),
    ]

    operations = [
        migrations.CreateModel(
            name="RepairPlanItemTypeCost",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("cost_base", models.DecimalField(decimal_places=2, max_digits=10)),
                ("cost_alternative", models.DecimalField(decimal_places=2, max_digits=10, null=True, blank=True)),
                (
                    "cost_lookup",
                    models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
                ),
                (
                    "region",
                    django_countries.fields.CountryField(default="AU", max_length=2),
                ),
                (
                    "repair_item_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="recommendations.repairplanitemtype",
                    ),
                ),
            ],
            options={
                "db_table": "service_repair_plan_item_type_cost",
            },
        ),
        migrations.AddConstraint(
            model_name="repairplanitemtypecost",
            constraint=models.UniqueConstraint(
                fields=("repair_item_type", "region", "cost_lookup"),
                name="unique_repair_item_type_region",
            ),
        ),
    ]
