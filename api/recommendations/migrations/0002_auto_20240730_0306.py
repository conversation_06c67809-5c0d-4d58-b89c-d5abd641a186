# Generated by Django 4.1.2 on 2024-07-30 03:06

from django.db import migrations, connection


def reset_sequence(apps, schema_editor):
    with connection.cursor() as cursor:
        cursor.execute(
            "SELECT setval(pg_get_serial_sequence('service_custom_repair_types', 'id'), COALESCE(MAX(id), 1), max(id) IS NOT null) FROM service_custom_repair_types"
        )


def create_custom_repair_type(apps, schema_editor):
    Organisations = apps.get_model("organisations", "Organisations")
    Custom_Repair_Types = apps.get_model("recommendations", "Custom_Repair_Types")

    countries = ["AU", "NZ", "US"]
    organisations = Organisations.objects.filter(country__in=countries).exclude(
        custom_repair_types__name="No. Junctions"
    )

    Custom_Repair_Types.objects.bulk_create(
        [
            Custom_Repair_Types(organisations=organisation, name="No. Junctions", type="number")
            for organisation in organisations
        ]
    )


class Migration(migrations.Migration):

    dependencies = [
        ("recommendations", "0001_initial"),
    ]

    operations = [
        migrations.RunPython(code=reset_sequence, reverse_code=migrations.RunPython.noop),
        migrations.RunPython(code=create_custom_repair_type, reverse_code=migrations.RunPython.noop),
    ]
