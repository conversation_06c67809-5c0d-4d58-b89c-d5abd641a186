from datetime import datetime, timezone
from typing import List

from azure.storage.blob import BlobServiceClient
from django.core.mail import EmailMultiAlternatives
from django.db.models import ExpressionWrapper, F, <PERSON><PERSON><PERSON><PERSON>, DateTimeField
from django.conf import settings
from api.common import storage
from api.common.storage import get_platform_storage_region_base_url
from api.inspections.models import MapPointList
from api.recommendations.models import Custom_Repair_Values, RepairRecommendation


def get_custom_row(inspection_id: int) -> dict:
    cdata = []

    custom_dict = {}
    customdata = {}
    cdict = {}
    odict = {}
    cstring = ""
    ostring = ""

    try:
        multirow = Custom_Repair_Values.objects.filter(point_id=inspection_id)
        for singlerow in multirow:
            cdata.append(singlerow)
    except Exception:
        singlerow = Custom_Repair_Values()
        singlerow.custom_Repair_Type = None
        singlerow.c_value_text = ""
        singlerow.c_value_number = 0
        singlerow.c_value_bool = False
        singlerow.o_value_text = ""
        singlerow.o_value_number = 0
        singlerow.o_value_bool = False
        cdata.append(singlerow)

    for row in cdata:
        type_name = row.custom_Repair_Type.name.strip()
        type_type = row.custom_Repair_Type.type.strip()
        if type_type == "boolean":
            type_type = "bool"

        c_value = getattr(row, "c_value_" + type_type, "")
        o_value = getattr(row, "o_value_" + type_type, "")

        cstring = f"Custom Type: {type_name}, Value: {str(c_value)};"
        ostring = f"Custom Type: {type_name}, Value: {str(o_value)};"

        cdict[type_name] = c_value
        odict[type_name] = o_value

    customdata["contractor"] = cstring
    customdata["owner"] = ostring
    custom_dict["contractor"] = cdict
    custom_dict["owner"] = odict

    return custom_dict


def get_uploaded_org_type(inspection_id: int) -> bool:
    try:
        pointobj = MapPointList.objects.get(id=inspection_id)
        uploaded_by_contractor = (
            pointobj.associated_file.upload_org.is_contractor and pointobj.associated_file.target_org.is_asset_owner
        )
        return uploaded_by_contractor
    except MapPointList.DoesNotExist:
        return False


def get_rr_data(ids: List[int], is_asset_owner: bool):
    data = []
    repairs = RepairRecommendation.objects.filter(target_id__in=ids).annotate(
        date=ExpressionWrapper(F("target__date_captured"), output_field=DateTimeField()),
        asset_id=ExpressionWrapper(F("target__asset_id"), output_field=CharField()),
    )

    repair_ids = repairs.values_list("target_id", flat=True)
    missing_ids = set(ids) - set(repair_ids)
    data = list(repairs)

    for inspection_id in missing_ids:
        rr = RepairRecommendation()
        rr.target_id = inspection_id  # type: ignore
        rr.asset_id = ""  # type: ignore
        rr.created_at = datetime.now(timezone.utc)
        rr.action_summary = ""
        rr.root_treatment = False
        rr.cleaning_required = False
        rr.patching_details = False
        rr.patches_counted = 0
        rr.full_relining = False
        rr.dig_up = False
        rr.other_action = False
        rr.risk_likelihood = None
        rr.risk_consequence = None
        rr.likelihood_comment = ""
        rr.consequence_comment = ""
        if is_asset_owner:
            rr.o_action_summary = ""
            rr.o_root_treatment = False
            rr.o_cleaning_required = False
            rr.o_patching_details = False
            rr.o_patches_counted = False
            rr.o_full_relining = False
            rr.o_dig_up = False
            rr.o_other_action = False
        else:
            rr.c_action_summary = ""
            rr.c_root_treatment = False
            rr.c_cleaning_required = False
            rr.c_patching_details = False
            rr.c_patches_counted = False
            rr.c_full_relining = False
            rr.c_dig_up = False
            rr.c_other_action = False

        data.append(rr)

    return data


def send_rr_email_to_client(filename: str, sas_read_token: str, user_email: str):
    subject = "Your repair recommendation csv is ready to download."
    from_email = "<EMAIL>"
    to = [user_email]

    text_content = "Your repair recommendation csv is ready. Please note that the link is only valid for an hour from the time you receive this email. Please click the link below."
    base_url = get_platform_storage_region_base_url()
    link_url = f"{base_url}{settings.BLOB_STORAGE_CSV_CONTAINER}/{filename}?{sas_read_token}"
    html_content = (
        "<p>Your repair recommendation csv is ready to be downloaded! <strong>Please note that this link is only valid for an hour from the time you receive this email.</strong> Please click the link below.</p>"
        '<a href="' + link_url + '" target="_blank" >Download Link</a><br>'
        "<p>Thanks for using our service!</p>"
        "<strong>VAPAR</strong>"
    )
    msg = EmailMultiAlternatives(subject, text_content, from_email, to=to)
    msg.attach_alternative(html_content, "text/html")
    msg.send()


def upload_csv(filename: str) -> str:
    sas_write_token = storage.get_platform_storage_sas_token(
        container_name=settings.BLOB_STORAGE_CSV_CONTAINER, write_access=True
    )
    service_client = BlobServiceClient(account_url=get_platform_storage_region_base_url(), credential=sas_write_token)
    # Upload the created file, use local_file_name for the blob name
    # container_client = service_client.get_container_client(container)
    blob_client = service_client.get_blob_client(container=settings.BLOB_STORAGE_CSV_CONTAINER, blob=filename)
    with open(filename, mode="rb") as upload_data:
        blob_client.upload_blob(upload_data)
    sas_read_token = storage.get_platform_storage_sas_token(container_name=settings.BLOB_STORAGE_CSV_CONTAINER)
    return sas_read_token
