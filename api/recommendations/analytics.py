from datetime import datetime
from enum import Enum

from django.conf import settings
from pydantic import BaseModel

from api.common.analytics import get_event_client
from api.recommendations.models import RepairRecommendation
from vapar.clients.events import AbstractEventClient


class RepairRecommendationParty(str, Enum):
    OWNER = "owner"
    CONTRACTOR = "contractor"
    VAPAR = "vapar"


class CustomValueKind(str, Enum):
    STR = "str"
    INT = "int"
    BOOL = "bool"


class RepairRecommendationComparedValues(BaseModel):
    """Repair recommendation values that are duplicated between the owner, contractor and VAPAR."""

    action_summary: str | None = None
    root_treatment: bool | None = None
    cleaning_required: bool | None = None
    patches_counted: int | None = None
    patching_details: bool | None = None
    full_relining: bool | None = None
    dig_up: bool | None = None
    dig_up_details: str | None = None
    no_immediate_action: bool | None = None
    other_action: bool | None = None

    model_config = {
        "extra": "ignore",
    }


class RepairPlanComparedValues(BaseModel):
    """Repair plan values"""

    likelihood: str | None = None
    consequence: str | None = None
    likelihood_comment: str | None = None
    consequence_comment: str | None = None
    frequency: str | None = None
    action_summary: str | None = None
    root_treatment: bool | None = None
    cleaning_required: bool | None = None
    patches_counted: int | None = None
    patching_details: bool | None = None
    full_relining: bool | None = None
    dig_up: bool | None = None
    dig_up_details: str | None = None
    no_immediate_action: bool | None = None
    other_action: bool | None = None

    model_config = {
        "extra": "ignore",
    }


class RepairRecommendationCustomValue(BaseModel):

    name: str
    kind: CustomValueKind
    str_value: str | None = None
    int_value: int | None = None
    bool_value: bool | None = None


class RepairRecommendationCreatedEvent(BaseModel):
    video_id: int
    repair_recommendation_id: int
    created_at: datetime

    values: RepairRecommendationComparedValues


class RepairRecommendationAppliedEvent(BaseModel):
    video_id: int
    repair_recommendation_id: int
    updated_at: datetime
    copied_from: RepairRecommendationParty
    copied_to: RepairRecommendationParty

    previous_values: RepairRecommendationComparedValues
    new_values: RepairRecommendationComparedValues

    model_config = {
        "populate_by_name": True,
    }


class RepairRecommendationUpdatedEvent(BaseModel):
    video_id: int
    repair_recommendation_id: int
    updated_at: datetime
    updated_by: RepairRecommendationParty

    values: RepairRecommendationComparedValues

    risk_likelihood: str | None = None
    risk_consequence: str | None = None


class RepairPlanCreatedUpdatedEvent(BaseModel):
    video_id: int
    repair_recommendation_id: int
    created_at: datetime

    values: RepairPlanComparedValues


_UNPREFIXED_FIELDS = set(RepairRecommendationComparedValues.model_fields.keys())
_PARTY_TO_PREFIX = {
    RepairRecommendationParty.OWNER: "o_",
    RepairRecommendationParty.CONTRACTOR: "c_",
    RepairRecommendationParty.VAPAR: "",
}


def _get_prefixed_for_party(party: RepairRecommendationParty) -> dict[str, str]:
    return {_PARTY_TO_PREFIX[party] + field: field for field in _UNPREFIXED_FIELDS}


def send_repair_recommendation_created_event(
    rr: RepairRecommendation,
    client: AbstractEventClient | None = None,
):
    """
    Send an event to the client indicating that a repair recommendation was created.

    :param rr: The repair recommendation object that was created
    :param client: Override the client to use. If None, the default client will be used.
    """

    client = client or get_event_client(settings.REPAIR_RECOMMENDATION_CREATED_EVENTHUB_NAME)

    event = RepairRecommendationCreatedEvent(
        video_id=rr.target.associated_file.id,
        repair_recommendation_id=rr.id,
        created_at=rr.created_at,
        values=RepairRecommendationComparedValues(
            action_summary=rr.action_summary,
            root_treatment=rr.root_treatment,
            cleaning_required=rr.cleaning_required,
            patches_counted=rr.patches_counted,
            patching_details=rr.patching_details,
            full_relining=rr.full_relining,
            dig_up=rr.dig_up,
            no_immediate_action=rr.no_immediate_action,
        ),
    )

    client.sync_send_events(event.model_dump_json())


def send_repair_recommendation_updated_event(
    fields_set: set[str],
    rr: RepairRecommendation,
    party: RepairRecommendationParty,
    client: AbstractEventClient | None = None,
):
    """
    Send an event to the client indicating that a repair recommendation was updated.

    :param fields_set: The set of fields that were updated
    :param rr: The repair recommendation object that was updated
     :param party: The party that updated the recommendation.
    :param client: Override the client to use. If None, the default client will be used.
    """

    client = client or get_event_client(settings.REPAIR_RECOMMENDATION_UPDATED_EVENTHUB_NAME)

    prefixed_to_unprefixed = _get_prefixed_for_party(party)
    prefixed_key_set = set(prefixed_to_unprefixed.keys())
    needed_fields = fields_set & prefixed_key_set

    fields_to_updated_value = {
        prefixed_to_unprefixed[field_name]: getattr(rr, field_name) for field_name in needed_fields
    }

    if "risk_likelihood" in fields_set:
        fields_to_updated_value["risk_likelihood"] = rr.risk_likelihood.name
    if "risk_consequence" in fields_set:
        fields_to_updated_value["risk_consequence"] = rr.risk_consequence.name

    event = RepairRecommendationUpdatedEvent(
        video_id=rr.target.associated_file.id,
        repair_recommendation_id=rr.id,
        updated_at=rr.updated_at,
        updated_by=party,
        values=RepairRecommendationComparedValues.model_validate(fields_to_updated_value),
    )
    client.sync_send_events(event.model_dump_json(exclude_unset=True, by_alias=True))


def send_repair_recommendation_applied_event(
    rr: RepairRecommendation,
    copied_from: RepairRecommendationParty,
    copied_to: RepairRecommendationParty,
    updated_at: datetime,
    client: AbstractEventClient | None = None,
):
    """
    Send an event to the client indicating that recommendation values were copied from one party to another.

    :param rr: The repair recommendation object that was updated
    :param copied_from: The party that the values were copied from
    :param copied_to: The party that the values were copied to
    :param updated_at: The time the update occurred
    :param client: Override the client to use. If None, the default client will be used.
    """

    client = client or get_event_client(settings.REPAIR_RECOMMENDATION_APPLIED_EVENTHUB_NAME)

    previous_values = {field: getattr(rr, prefixed) for prefixed, field in _get_prefixed_for_party(copied_to).items()}
    new_values = {field: getattr(rr, prefixed) for prefixed, field in _get_prefixed_for_party(copied_from).items()}

    event = RepairRecommendationAppliedEvent(
        video_id=rr.target.associated_file.id,
        repair_recommendation_id=rr.id,
        updated_at=updated_at,
        copied_from=copied_from,
        copied_to=copied_to,
        previous_values=RepairRecommendationComparedValues.model_validate(previous_values),
        new_values=RepairRecommendationComparedValues.model_validate(new_values),
    )

    client.sync_send_events(event.model_dump_json(by_alias=True))


def send_repair_plan_created_updated_event(
    rr: RepairRecommendation,
    client: AbstractEventClient | None = None,
):
    """
    Send an event to the client indicating that a repair recommendation was created.

    :param rr: The repair recommendation object that was created
    :param client: Override the client to use. If None, the default client will be used.
    """

    client = client or get_event_client(settings.REPAIR_RECOMMENDATION_CREATED_EVENTHUB_NAME)

    event = RepairPlanCreatedUpdatedEvent(
        video_id=rr.target.associated_file.id,
        repair_recommendation_id=rr.id,
        created_at=rr.created_at,
        values=RepairPlanComparedValues(
            likelihood=rr.risk_likelihood.name if rr.risk_likelihood else None,
            consequence=rr.risk_consequence.name if rr.risk_consequence else None,
            likelihood_comment=rr.likelihood_comment,
            consequence_comment=rr.consequence_comment,
            frequency=rr.inspection_frequency,
            action_summary=rr.action_summary,
            root_treatment=rr.root_treatment,
            cleaning_required=rr.cleaning_required,
            patches_counted=rr.patches_counted,
            patching_details=rr.patching_details,
            full_relining=rr.full_relining,
            dig_up=rr.dig_up,
            dig_up_details=rr.dig_up_details,
            no_immediate_action=rr.no_immediate_action,
            other_action=rr.other_action,
        ),
    )

    client.sync_send_events(event.model_dump_json())
