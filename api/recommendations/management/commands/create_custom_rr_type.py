import logging

from django.core.management import BaseCommand

from api.recommendations.models import Custom_Repair_Types
from api.organisations.models import Organisations

log = logging.getLogger(__name__)

class Command(BaseCommand):
    help = "Add a new custom repair type to an organisation"

    def add_arguments(self, parser):
        parser.add_argument("org_id", type=int, help="Organisation ID")
        parser.add_argument("name", type=str, help="Name of the repair type")
        parser.add_argument("type", type=str, help="boolean, number or text")

    def handle(self, *args, **options):
        org_id = options["org_id"]
        rr_name = options["name"]
        rr_type = options["type"]

        org = Organisations.objects.get(id=org_id)
        Custom_Repair_Types.objects.get_or_create(organisations=org, name=rr_name, type=rr_type)
        log.info("Added new repair type.")