from django.urls import path

from api.recommendations.views import (
    BulkRepairRecommendationsView,
    CustomRepairValueView,
    RepairCompletedView,
    RepairCopySuggestionView,
    RepairRecommendationCSVView,
    RepairRecommendationView,
    RepairCustomTypeList,
    RepairPlanView,
    RepairPlanItemListView,
)


urlpatterns = [
    path(
        "repairs/<int:inspection_id>",
        RepairRecommendationView.as_view(),
        name="repair_recommendation",
    ),
    path(
        "repairs/<int:inspection_id>/custom-value",
        CustomRepairValueView.as_view(),
        name="repair_custom_value",
    ),
    path(
        "repairs/<int:inspection_id>/custom-types",
        RepairCustomTypeList.as_view(),
        name="repair_custom_type",
    ),
    path(
        "repairs/<int:inspection_id>/complete",
        RepairCompletedView.as_view(),
        name="repair_recommendation_complete",
    ),
    path(
        "repairs/<int:inspection_id>/copy-suggestion",
        RepairCopySuggestionView.as_view(),
        name="repair_suggestion_copy",
    ),
    path(
        "repairs/generate",
        BulkRepairRecommendationsView.as_view(),
        name="repair_recommendation_bulk_process",
    ),
    path(
        "repair/generate-csv",
        RepairRecommendationCSVView.as_view(),
        name="repair_generate_csv",
    ),
    path(
        "inspections/<uuid:inspection_id>/repairplan",
        RepairPlanView.as_view(),
        name="repair_plan",
    ),
    path(
        "inspections/<uuid:inspection_id>/repairplan/<actor>",
        RepairPlanItemListView.as_view(),
        name="repair_plan_item_list",
    ),
]
