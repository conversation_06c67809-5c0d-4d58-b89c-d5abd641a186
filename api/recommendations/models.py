from django.db import models
from django_countries.fields import CountryField

from api.common.enums import RepairPlanItemTypeEnum
from api.inspections.models import MapPointList
from api.users.models import CustomUser
from api.organisations.models import Organisations


class RiskLikelihood(models.Model):
    name = models.CharField(max_length=200, null=True, blank=True)
    value = models.IntegerField(null=True, blank=True)

    def __str__(self):
        return self.name

    class Meta:
        db_table = "service_risk_likelihood"


class RiskConsequence(models.Model):
    name = models.CharField(max_length=200, null=True, blank=True)
    value = models.IntegerField(null=True, blank=True)

    def __str__(self):
        return self.name

    class Meta:
        db_table = "service_risk_consequence"


class RepairRecommendation(models.Model):
    inspection_parameters = models.TextField(blank=True, null=True)
    action_summary = models.TextField(null=True, blank=True)
    root_treatment = models.BooleanField(default=False)
    cleaning_required = models.BooleanField(default=False)
    patches_counted = models.PositiveIntegerField(blank=True, null=True)
    patching_details = models.BooleanField(default=False)
    full_relining = models.BooleanField(default=False)
    dig_up = models.BooleanField(default=False)
    dig_up_details = models.TextField(null=True, blank=True)
    no_immediate_action = models.BooleanField(default=False)
    other_action = models.BooleanField(default=False, blank=True)
    target = models.OneToOneField(MapPointList, on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=True)
    updated_at = models.DateTimeField(blank=True, null=True)
    updated_by = models.ForeignKey(
        CustomUser,
        related_name="r_updated_by_set",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )

    c_action_summary = models.TextField(null=True, blank=True)
    c_root_treatment = models.BooleanField(default=False)
    c_cleaning_required = models.BooleanField(default=False)
    c_patches_counted = models.PositiveIntegerField(blank=True, null=True)
    c_patching_details = models.BooleanField(default=False)
    c_full_relining = models.BooleanField(default=False)
    c_dig_up = models.BooleanField(default=False)
    c_dig_up_details = models.TextField(null=True, blank=True)
    c_no_immediate_action = models.BooleanField(default=False)
    c_other_action = models.BooleanField(default=False)
    c_updated_at = models.DateTimeField(default=None, blank=True, null=True)
    c_updated_by = models.ForeignKey(
        CustomUser,
        related_name="c_updated_by_set",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )
    contractor_review = models.TextField(null=True, blank=True)
    ready_to_view = models.BooleanField(default=True)

    o_action_summary = models.TextField(null=True, blank=True)
    o_root_treatment = models.BooleanField(default=False)
    o_cleaning_required = models.BooleanField(default=False)
    o_patches_counted = models.PositiveIntegerField(blank=True, null=True)
    o_patching_details = models.BooleanField(default=False)
    o_full_relining = models.BooleanField(default=False)
    o_dig_up = models.BooleanField(default=False)
    o_dig_up_details = models.TextField(null=True, blank=True)
    o_no_immediate_action = models.BooleanField(default=False)
    o_other_action = models.BooleanField(default=False)
    o_updated_at = models.DateTimeField(blank=True, null=True)
    o_updated_by = models.ForeignKey(
        CustomUser,
        related_name="o_updated_by_set",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )
    owner_review = models.TextField(null=True, blank=True)
    inspection_frequency = models.TextField(null=True, blank=True)
    risk_likelihood = models.ForeignKey(RiskLikelihood, on_delete=models.SET_NULL, null=True, blank=True)
    risk_consequence = models.ForeignKey(RiskConsequence, on_delete=models.SET_NULL, null=True, blank=True)
    likelihood_comment = models.TextField(null=True, blank=True)
    consequence_comment = models.TextField(null=True, blank=True)
    risk_updated_at = models.DateTimeField(default=None, blank=True, null=True)
    risk_updated_by = models.ForeignKey(
        CustomUser,
        related_name="risk_updated_by_set",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )

    def set_full_relining(self, value, owner=True):
        attrib_prefix = "o" if owner else "c"
        setattr(self, f"{attrib_prefix}_full_relining", value)
        if value is True:
            setattr(self, f"{attrib_prefix}_root_treatment", False)
            setattr(self, f"{attrib_prefix}_cleaning_required", False)
            setattr(self, f"{attrib_prefix}_patches_counted", 0)
            setattr(self, f"{attrib_prefix}_patching_details", False)
            setattr(self, f"{attrib_prefix}_no_immediate_action", False)
            setattr(self, f"{attrib_prefix}_dig_up_details", "")
            setattr(self, f"{attrib_prefix}_other_action", False)

    def set_no_immediate_action(self, value, owner=True):
        attrib_prefix = "o" if owner else "c"
        setattr(self, f"{attrib_prefix}_no_immediate_action", value)
        if value is True:
            setattr(self, f"{attrib_prefix}_full_relining", False)
            setattr(self, f"{attrib_prefix}_root_treatment", False)
            setattr(self, f"{attrib_prefix}_cleaning_required", False)
            setattr(self, f"{attrib_prefix}_patches_counted", 0)
            setattr(self, f"{attrib_prefix}_patching_details", False)
            setattr(self, f"{attrib_prefix}_dig_up", False)
            setattr(self, f"{attrib_prefix}_dig_up_details", "")
            setattr(self, f"{attrib_prefix}_other_action", False)

    def set_patches_counted(self, value, owner=True):
        attrib_prefix = "o" if owner else "c"
        setattr(self, f"{attrib_prefix}_patching_details", bool(value))

    class Meta:
        db_table = "service_repairrecommendation"


class Custom_Repair_Types(models.Model):
    organisations = models.ForeignKey(Organisations, on_delete=models.SET_NULL, null=True, blank=True)
    name = models.CharField(max_length=100)
    type = models.CharField(max_length=100)

    class Meta:
        db_table = "service_custom_repair_types"


class Custom_Repair_Values(models.Model):
    point = models.ForeignKey(MapPointList, on_delete=models.SET_NULL, null=True, blank=True)
    custom_Repair_Type = models.ForeignKey(Custom_Repair_Types, on_delete=models.SET_NULL, null=True, blank=True)
    c_value_text = models.CharField(max_length=200, null=True, blank=True)
    c_value_number = models.IntegerField(null=True, blank=True)
    c_value_bool = models.BooleanField(null=True, blank=True)
    o_value_text = models.CharField(max_length=200, null=True, blank=True)
    o_value_number = models.IntegerField(null=True, blank=True)
    o_value_bool = models.BooleanField(null=True, blank=True)

    class Meta:
        db_table = "service_custom_repair_values"


class RepairPlanItemType(models.Model):
    name = models.CharField(
        max_length=200,
        blank=True,
        # default=RepairPlanItemTypeEnum.OTHER,
        choices=RepairPlanItemTypeEnum.as_choices(),
    )
    metadata = models.JSONField()

    class Meta:
        db_table = "service_repair_plan_item_type"


class RepairPlanItemTypeCost(models.Model):
    cost_base = models.DecimalField(decimal_places=2, max_digits=10)
    cost_alternative = models.DecimalField(decimal_places=2, max_digits=10, blank=True, null=True, default=None)
    cost_lookup = models.PositiveIntegerField(blank=True, null=True, default=None)
    repair_item_type = models.ForeignKey(RepairPlanItemType, on_delete=models.CASCADE, null=False)
    region = CountryField(null=False, default="AU")

    class Meta:
        db_table = "service_repair_plan_item_type_cost"
        constraints = [
            models.UniqueConstraint(
                fields=["repair_item_type", "region", "cost_lookup"], name="unique_repair_item_type_region"
            )
        ]
