import uuid

from django.utils import timezone
from rest_framework import serializers

from api.actions.models import AuditList
from api.common.enums import RepairPlanActorEnum, RepairPlanItemTypeEnum, CustomRepairItemDataTypeEnum
from api.inspections.models import Asset, Inspection
from api.recommendations import analytics
from api.recommendations.models import (
    Custom_Repair_Values as CustomRepairValue,
    Custom_Repair_Types as CustomRepairType,
    RepairPlanItemTypeCost,
    RepairRecommendation,
    RiskLikelihood,
    RiskConsequence,
)
from api.recommendations.tasks.tasks import (
    _process_inspections,
    generate_repair_recommendations,
    update_or_create_recommendation,
)


class RepairRecommendationSerializer(serializers.ModelSerializer):
    class Meta:
        model = RepairRecommendation
        fields = "__all__"


class RiskLikelyhoodSerializer(serializers.ModelSerializer):
    class Meta:
        model = RiskLikelihood
        fields = "__all__"


class RiskConsequenceSerializer(serializers.ModelSerializer):
    class Meta:
        model = RiskConsequence
        fields = "__all__"


class CustomRepairValueSerializer(serializers.ModelSerializer):
    class Meta:
        model = CustomRepairValue
        fields = "__all__"


class CustomRepairTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = CustomRepairType
        exclude = ["organisations"]


_ACTOR_TO_PREFIX = {
    RepairPlanActorEnum.VAPAR: "",
    RepairPlanActorEnum.OWNER: "o_",
    RepairPlanActorEnum.CONTRACTOR: "c_",
}


class RepairItemSerializer(serializers.Serializer):
    id = serializers.UUIDField(read_only=True)
    last_updated = serializers.DateTimeField(read_only=True)
    repair_type = serializers.ChoiceField(choices=RepairPlanItemTypeEnum.as_choices(), write_only=True)
    # repair_cost_estimate = serializers.DecimalField(max_digits=10, decimal_places=2)
    custom_type_id = serializers.IntegerField(allow_null=True, default=None)
    custom_data_type = serializers.ChoiceField(
        read_only=True, allow_null=True, choices=CustomRepairItemDataTypeEnum.as_choices()
    )
    actor = serializers.ChoiceField(choices=RepairPlanActorEnum.as_choices(), write_only=True)
    metadata = serializers.ListSerializer(child=serializers.DictField(), write_only=True)

    def validate(self, attrs):
        attrs = super().validate(attrs)

        if self.instance is None:  # Creating
            if attrs["repair_type"] == RepairPlanItemTypeEnum.CUSTOM.value:  # Custom repair type
                org = Inspection.objects.get(uuid=self.context["inspection_id"]).asset.organisation
                repair_type_obj = CustomRepairType.objects.filter(pk=attrs["custom_type_id"], organisations=org).first()
                if repair_type_obj is None:
                    raise serializers.ValidationError("Invalid custom repair type id")

                value_metadata = next((pair for pair in attrs["metadata"] if pair["key"] == "CustomValue"), None)
                if not value_metadata:
                    raise serializers.ValidationError("Custom repair type requires CustomValue in metadata")

                # Allow null values to remove the custom repair type value
                if value_metadata["value"] is not None:
                    data_type = CustomRepairItemDataTypeEnum(repair_type_obj.type)
                    try:
                        data_type.as_type(value_metadata["value"])
                    except ValueError:
                        raise serializers.ValidationError("Invalid value for custom repair type")

            else:  # Built-in repair type
                if attrs.get("custom_type_id") is not None:
                    raise serializers.ValidationError(
                        "Custom repair type id is not allowed for non-custom repair types"
                    )

        else:  # Updating
            if "repair_type" in attrs:
                raise serializers.ValidationError("Cannot update repair type")
            if "actor" in attrs:
                raise serializers.ValidationError("Cannot update actor")
            if "custom_type_id" in attrs:
                raise serializers.ValidationError("Cannot update custom type id")
            if "custom_data_type" in attrs:
                raise serializers.ValidationError("Cannot update custom data type")

        return attrs

    def create(self, validated_data):
        """
        Create a repair item for a repair - this actually involves an update to either the RepairRecommendation or
        a related CustomRepairValue object.
        """

        rr = RepairRecommendation.objects.get(target__inspection__uuid=self.context["inspection_id"])
        actor = RepairPlanActorEnum(validated_data["actor"])
        prefix = _ACTOR_TO_PREFIX[actor]
        repair_type = RepairPlanItemTypeEnum(validated_data["repair_type"])
        if repair_type == RepairPlanItemTypeEnum.CUSTOM:

            repair_type_obj = CustomRepairType.objects.get(pk=validated_data["custom_type_id"])
            custom_repair_value, _created = CustomRepairValue.objects.get_or_create(
                custom_Repair_Type=repair_type_obj,
                point=rr.target,
            )

            metadata_value = next(pair["value"] for pair in validated_data["metadata"] if pair["key"] == "CustomValue")
            data_type = CustomRepairItemDataTypeEnum(repair_type_obj.type)
            field_name = f"{prefix}{data_type.as_field_name()}"

            # If metadata_value is null, set the field to None to remove the value
            if metadata_value is None:
                setattr(custom_repair_value, field_name, None)
            else:
                typed_value = data_type.as_type(metadata_value)
                setattr(custom_repair_value, field_name, typed_value)

            custom_repair_value.save()
            return rr

        if repair_type == RepairPlanItemTypeEnum.NO_ACTION:
            setattr(rr, f"{prefix}no_immediate_action", True)
        elif repair_type == RepairPlanItemTypeEnum.PATCH:
            setattr(rr, f"{prefix}patching_details", True)
            patch_count_metadata = next((pair for pair in validated_data["metadata"] if pair["key"] == "Count"), None)
            patch_count = int(patch_count_metadata["value"]) if patch_count_metadata else None
            setattr(rr, f"{prefix}patches_counted", patch_count)
        elif repair_type == RepairPlanItemTypeEnum.CLEANING:
            setattr(rr, f"{prefix}cleaning_required", True)
        elif repair_type == RepairPlanItemTypeEnum.LINING:
            setattr(rr, f"{prefix}full_relining", True)
        elif repair_type == RepairPlanItemTypeEnum.ROOT_REMOVAL:
            setattr(rr, f"{prefix}root_treatment", True)
        elif repair_type == RepairPlanItemTypeEnum.DIG_UP:
            setattr(rr, f"{prefix}dig_up", True)
            dig_up_details_metadata = next(
                (pair for pair in validated_data["metadata"] if pair["key"] == "Details"), None
            )
            dig_up_details = dig_up_details_metadata["value"] if dig_up_details_metadata else None
            setattr(rr, f"{prefix}dig_up_details", dig_up_details)
        elif repair_type == RepairPlanItemTypeEnum.OTHER:
            setattr(rr, f"{prefix}other_action", True)

        rr.save()
        rr.refresh_from_db()
        return rr

    def set_default(self):
        """
        Build a new repair item for a repair with default values.
        This actually involves an update to either the RepairRecommendation or a related CustomRepairValue object.
        """
        rr = RepairRecommendation.objects.get(target__inspection__uuid=self.context["inspection_id"])
        actor = RepairPlanActorEnum(self.validated_data["actor"])
        prefix = _ACTOR_TO_PREFIX[actor]
        repair_type = RepairPlanItemTypeEnum(self.validated_data["repair_type"])

        if repair_type == RepairPlanItemTypeEnum.CUSTOM:
            repair_type_obj = CustomRepairType.objects.get(pk=self.validated_data["custom_type_id"])
            custom_repair_value, _created = CustomRepairValue.objects.get_or_create(
                custom_Repair_Type=repair_type_obj,
                point=rr.target,
            )

            data_type = CustomRepairItemDataTypeEnum(repair_type_obj.type)
            field_name = f"{prefix}{data_type.as_field_name()}"

            # For set_default, always set to None to clear the value
            setattr(custom_repair_value, field_name, None)
            custom_repair_value.save()

        if repair_type == RepairPlanItemTypeEnum.NO_ACTION:
            setattr(rr, f"{prefix}no_immediate_action", False)
        elif repair_type == RepairPlanItemTypeEnum.PATCH:
            setattr(rr, f"{prefix}patching_details", False)
            setattr(rr, f"{prefix}patches_counted", 0)
        elif repair_type == RepairPlanItemTypeEnum.CLEANING:
            setattr(rr, f"{prefix}cleaning_required", False)
        elif repair_type == RepairPlanItemTypeEnum.LINING:
            setattr(rr, f"{prefix}full_relining", False)
        elif repair_type == RepairPlanItemTypeEnum.ROOT_REMOVAL:
            setattr(rr, f"{prefix}root_treatment", False)
        elif repair_type == RepairPlanItemTypeEnum.DIG_UP:
            setattr(rr, f"{prefix}dig_up", False)
            setattr(rr, f"{prefix}dig_up_details", None)
        elif repair_type == RepairPlanItemTypeEnum.OTHER:
            setattr(rr, f"{prefix}other_action", False)

        rr.save()
        rr.refresh_from_db()
        return rr


def calculate_repair_item_cost(
    repair_item_type: RepairPlanItemTypeEnum,
    asset_diameter: int,
    asset_length: int,
    cost_region: str,
    cost_modifier: int | None = None,
) -> float:
    """Use custom logic for repair item types to determine a estimated repair cost.

    Uses a lookup value to calculate the estimated costs for each individual repair item, usually asset diameter.
    Applies a length unit modifier on certain repair items when appropriate. Also apply an arbitrary modifer to account
    for repair type specific adjustments.
    """

    # Fetch the related cost data for the repair type
    cost_model_qs = RepairPlanItemTypeCost.objects.filter(
        repair_item_type__name=repair_item_type.value, region=cost_region
    )
    # Set the the minimum asset length to 1
    asset_length = max(asset_length, 1)

    cost_estimate = 0.0
    match repair_item_type:
        case RepairPlanItemTypeEnum.DIG_UP:
            cost_model = cost_model_qs.first()
            if cost_model is not None:
                cost_estimate = cost_model_qs.first().cost_base
        case RepairPlanItemTypeEnum.PATCH:  #  | RepairPlanItemTypeEnum.JUNCTION:
            cost_model = cost_model_qs.filter(cost_lookup=asset_diameter).first()
            if cost_model is not None:
                cost_estimate = float(cost_model.cost_base)
                # Use 'count' modifier if value supplied
                if cost_modifier:
                    cost_estimate = cost_estimate * cost_modifier
        case RepairPlanItemTypeEnum.LINING | RepairPlanItemTypeEnum.CLEANING | RepairPlanItemTypeEnum.ROOT_REMOVAL:
            cost_model = cost_model_qs.filter(cost_lookup=asset_diameter).first()
            if cost_model is not None:
                cost_estimate = float(cost_model.cost_base)
                # Cost for these repair types are per 'unit' of asset length
                cost_estimate = cost_estimate * asset_length
        case _:
            pass

    return round(float(cost_estimate), 2)


def repair_recommendation_to_repair_plan_items_repr(
    instance: RepairRecommendation, actor: RepairPlanActorEnum
) -> list[dict]:
    """
    Return a representation of the repair plan items for a single repair recommendation party.
    """

    # NOTE: UUIDs here are just placeholders

    repair_item_reprs = []
    prefix = _ACTOR_TO_PREFIX[actor]

    common_attribs = {
        "id": str(uuid.uuid4()),
        "last_updated": instance.updated_at,
        "actor": actor,
        "custom_type_id": None,
        "custom_data_type": None,
    }

    # Get details required for calculating repair costs
    related_inspection: Inspection = Inspection.objects.get(legacy_id=instance.target_id)
    # Get relevant Inspection details
    inspection_details = related_inspection.get_inspection_values(get_value_dict=True, use_header_names=True)
    try:
        inspection_length = float(inspection_details.get("LengthSurveyed", 1))
    except ValueError:
        inspection_length = 0.0
    related_asset: Asset = related_inspection.asset
    # Get relevant Asset details
    asset_details = related_asset.get_asset_values(get_value_dict=True, use_header_names=True)
    try:
        asset_diameter = int(asset_details.get("HeightDiameter", 0))
    except ValueError:
        asset_diameter = 0
    # use inspection length as fall-back when pipe length isn't set
    try:
        asset_length = float(asset_details.get("PipeUnitLength", inspection_length))
    except ValueError:
        asset_length = 0.0
    # asset_type = asset_details["UseOfDrainSewer"]
    asset_region = related_asset.organisation.country

    # special case for 'No Action' plans
    if getattr(instance, f"{prefix}no_immediate_action"):
        repair_item_reprs.append(
            {
                **common_attribs,
                "repair_type": RepairPlanItemTypeEnum.NO_ACTION.value,
                "repair_cost_estimate": 0.0,
                "metadata": [],
            },
        )
    else:
        if getattr(instance, f"{prefix}patching_details"):
            repair_item_type = RepairPlanItemTypeEnum.PATCH
            try:
                number_of_patches = int(getattr(instance, f"{prefix}patches_counted"))
            except ValueError:
                number_of_patches = 0
            repair_item_reprs.append(
                {
                    **common_attribs,
                    "repair_type": repair_item_type.value,
                    "repair_cost_estimate": calculate_repair_item_cost(
                        repair_item_type,
                        asset_diameter,
                        asset_length,
                        asset_region,
                        cost_modifier=number_of_patches,
                    ),
                    "metadata": [
                        {"key": "Count", "value": number_of_patches},
                    ],
                }
            )
        if getattr(instance, f"{prefix}cleaning_required"):
            repair_item_type = RepairPlanItemTypeEnum.CLEANING
            repair_item_reprs.append(
                {
                    **common_attribs,
                    "repair_type": repair_item_type.value,
                    "repair_cost_estimate": calculate_repair_item_cost(
                        repair_item_type, asset_diameter, asset_length, asset_region
                    ),
                    "metadata": [],
                }
            )
        if getattr(instance, f"{prefix}full_relining"):
            repair_item_type = RepairPlanItemTypeEnum.LINING
            repair_item_reprs.append(
                {
                    **common_attribs,
                    "repair_type": repair_item_type.value,
                    "repair_cost_estimate": calculate_repair_item_cost(
                        repair_item_type, asset_diameter, asset_length, asset_region
                    ),
                    "metadata": [],
                }
            )
        if getattr(instance, f"{prefix}root_treatment"):
            repair_item_type = RepairPlanItemTypeEnum.ROOT_REMOVAL
            repair_item_reprs.append(
                {
                    **common_attribs,
                    "repair_type": repair_item_type.value,
                    "repair_cost_estimate": calculate_repair_item_cost(
                        repair_item_type, asset_diameter, asset_length, asset_region
                    ),
                    "metadata": [],
                }
            )
        if getattr(instance, f"{prefix}dig_up"):
            repair_item_type = RepairPlanItemTypeEnum.DIG_UP
            repair_item_reprs.append(
                {
                    **common_attribs,
                    "repair_type": repair_item_type.value,
                    "repair_cost_estimate": calculate_repair_item_cost(
                        repair_item_type, asset_diameter, asset_length, asset_region
                    ),
                    "metadata": [
                        {"key": "Details", "value": getattr(instance, f"{prefix}dig_up_details")},
                    ],
                }
            )
        if getattr(instance, f"{prefix}other_action"):
            repair_item_type = RepairPlanItemTypeEnum.OTHER
            repair_item_reprs.append(
                {
                    **common_attribs,
                    "repair_type": repair_item_type.value,
                    "repair_cost_estimate": calculate_repair_item_cost(
                        repair_item_type, asset_diameter, asset_length, asset_region
                    ),
                    "metadata": [],
                }
            )

    # Custom repair items
    for custom_value in instance.target.custom_repair_values_set.select_related("custom_Repair_Type"):
        data_type = CustomRepairItemDataTypeEnum(custom_value.custom_Repair_Type.type)
        field_name = f"{prefix}{data_type.as_field_name()}"

        if (val := getattr(custom_value, field_name, None)) is not None:
            formatted_val = data_type.to_str_value(val)
            repair_item_reprs.append(
                {
                    **common_attribs,
                    "repair_type": RepairPlanItemTypeEnum.CUSTOM.value,
                    "repair_cost_estimate": 0.0,
                    "custom_type_id": custom_value.custom_Repair_Type_id,
                    "custom_data_type": custom_value.custom_Repair_Type.type,
                    "metadata": [
                        {"key": "CustomValue", "value": formatted_val},
                    ],
                }
            )

    return repair_item_reprs


class RepairPlanActorSerializer(serializers.Serializer):

    actor = serializers.ChoiceField(choices=RepairPlanActorEnum.as_choices(), read_only=True)
    items = RepairItemSerializer(many=True)
    general_notes = serializers.CharField(required=False, allow_blank=True)
    action_summary = serializers.CharField(required=False, allow_blank=True, allow_null=True)

    def to_representation(self, instance: RepairRecommendation):
        actor = self.context["actor"]
        prefix = _ACTOR_TO_PREFIX[actor]
        action_summary = getattr(instance, f"{prefix}action_summary")
        return {
            "actor": actor,
            "items": repair_recommendation_to_repair_plan_items_repr(instance, actor),
            "general_notes": "",  # TODO - no notes fields in RepairRecommendation
            "action_summary": action_summary if action_summary else None,
        }

    def update(self, instance, validated_data):
        actor = self.context["actor"]
        if "action_summary" in validated_data:
            prefix = _ACTOR_TO_PREFIX[actor]
            value = validated_data["action_summary"]
            setattr(instance, f"{prefix}action_summary", value if value else None)
        instance.save()

        # Handle items
        if "items" in validated_data:
            # Retrieve the current state of the Repair Plan Actor instance.
            repair_plan_items = repair_recommendation_to_repair_plan_items_repr(instance, actor)
            # Sets any existing items to their default values.
            for repair_plan_item in repair_plan_items:
                repair_plan_item_serializer = RepairItemSerializer(
                    data=repair_plan_item, context={"actor": actor, "inspection_id": instance.target.inspection_id}
                )
                repair_plan_item_serializer.is_valid(raise_exception=True)
                repair_plan_item_serializer.set_default()

            # Update the list based on the payload received.
            items_serializer = RepairItemSerializer(
                data=validated_data["items"],
                context={"actor": actor, "inspection_id": instance.target.inspection_id},
                many=True,
            )
            items_serializer.is_valid(raise_exception=True)
            items_serializer.save()
        instance.refresh_from_db()
        return instance


class RepairPlanSerializer(serializers.Serializer):
    """
    Representation of a repair plan for a single inspection.
    """

    # This is a proxy for the old RepairRecommendation model

    id = serializers.IntegerField(read_only=True)
    inspection_id = serializers.UUIDField(read_only=True)

    created_at = serializers.DateTimeField(read_only=True)
    updated_at = serializers.DateTimeField(read_only=True)

    general_notes = serializers.CharField(required=False, allow_null=True)
    junctions = serializers.IntegerField(required=False, allow_null=True)
    consequence = serializers.CharField(required=False, allow_null=True)
    consequence_comment = serializers.CharField(required=False, allow_null=True)
    likelihood = serializers.CharField(required=False, allow_null=True)
    likelihood_comment = serializers.CharField(required=False, allow_null=True)
    frequency = serializers.CharField(required=False, allow_null=True)

    vapar_plan = RepairPlanActorSerializer(read_only=True)
    owner_plan = RepairPlanActorSerializer(read_only=True)
    contractor_plan = RepairPlanActorSerializer(read_only=True)

    def validate_consequence(self, value):
        if value and not RiskConsequence.objects.filter(name=value).exists():
            raise serializers.ValidationError("Invalid RiskConsequence")
        return value

    def validate_likelihood(self, value):
        if value and not RiskLikelihood.objects.filter(name=value).exists():
            raise serializers.ValidationError("Invalid RiskLikelihood")
        return value

    def validate(self, attrs):
        validated = super().validate(attrs)
        if self.instance is None:  # Creating
            insp_id = self.context.get("inspection_id")
            if not Inspection.objects.filter(uuid=insp_id).exists():
                raise serializers.ValidationError("Invalid inspection id")
            if RepairRecommendation.objects.filter(target__inspection_id=insp_id).exists():
                raise serializers.ValidationError("RepairRecommendation already exists for this inspection")
        return validated

    def create(self, validated_data) -> RepairRecommendation:
        """
        On create, the 'inspection_id' field needs to be passed in through context.
        """

        insp_id = self.context.get("inspection_id")
        inspection = Inspection.objects.get(uuid=insp_id)

        rr_data = generate_repair_recommendations(
            _process_inspections([inspection.mappointlist]),
        )
        update_or_create_recommendation(rr_data[0], self.context["request"].user)

        instance = RepairRecommendation.objects.get(target=inspection.mappointlist)
        if consequence := validated_data.get("consequence"):
            instance.risk_consequence = RiskConsequence.objects.get(name=consequence)
        if consequence_comment := validated_data.get("consequence_comment"):
            instance.consequence_comment = consequence_comment
        if likelihood := validated_data.get("likelihood"):
            instance.risk_likelihood = RiskLikelihood.objects.get(name=likelihood)
        if likelihood_comment := validated_data.get("likelihood_comment"):
            instance.likelihood_comment = likelihood_comment
        if freq := validated_data.get("frequency"):
            instance.inspection_frequency = freq

        # TODO - junctions, general_notes

        instance.save()

        AuditList.objects.create(
            event_type="Create",
            table="repairrecommendation",
            row_id=instance.target.id,
            description="Create RR",
            date_of_modification=timezone.now(),
            user=self.context["request"].user,
        )

        analytics.send_repair_plan_created_updated_event(instance)

        return instance

    def update(self, instance: RepairRecommendation, validated_data) -> RepairRecommendation:
        """
        Update the top-level fields of the repair plan.
        """
        now = timezone.now()
        # Explicitly set fields to None if they are not in validated_data
        instance.risk_consequence = (
            RiskConsequence.objects.get(name=validated_data.get("consequence"))
            if validated_data.get("consequence")
            else None
        )
        audit_description = "Update RR Consequence" if instance.risk_consequence else "Update RR"

        instance.consequence_comment = validated_data.get("consequence_comment", None)
        audit_description = "Update RR Consequence Comment" if instance.consequence_comment else audit_description

        instance.risk_likelihood = (
            RiskLikelihood.objects.get(name=validated_data.get("likelihood"))
            if validated_data.get("likelihood")
            else None
        )
        audit_description = "Update RR Likelihood" if instance.risk_likelihood else audit_description

        instance.likelihood_comment = validated_data.get("likelihood_comment", None)
        audit_description = "Update RR Likelihood Comment" if instance.likelihood_comment else audit_description

        instance.inspection_frequency = validated_data.get("frequency", None)

        # TODO - junctions, general_notes

        instance.updated_at = now
        instance.save()

        AuditList.objects.create(
            event_type="Update",
            table="repairrecommendation",
            row_id=instance.target.id,
            column=min(set(validated_data)),
            description=audit_description,
            date_of_modification=now,
            user=self.context["request"].user,
        )

        analytics.send_repair_plan_created_updated_event(instance)

        return instance

    def to_representation(self, instance: RepairRecommendation):
        vapar_repr = RepairPlanActorSerializer(instance=instance, context={"actor": RepairPlanActorEnum.VAPAR}).data
        owner_repr = RepairPlanActorSerializer(instance=instance, context={"actor": RepairPlanActorEnum.OWNER}).data
        contractor_repr = RepairPlanActorSerializer(
            instance=instance, context={"actor": RepairPlanActorEnum.CONTRACTOR}
        ).data

        repair_plan_repr = {
            "id": str(uuid.uuid4()),  # TODO: Just a placeholder
            "created_at": instance.created_at,
            "updated_at": instance.updated_at,
            "inspection_id": str(instance.target.inspection_id),
            "consequence": instance.risk_consequence.name if instance.risk_consequence else None,
            "consequence_comment": instance.consequence_comment if instance.consequence_comment else None,
            "likelihood": instance.risk_likelihood.name if instance.risk_likelihood else None,
            "likelihood_comment": instance.likelihood_comment if instance.likelihood_comment else None,
            "frequency": instance.inspection_frequency,
            "general_notes": "",
            "junctions": 0,  # TODO - Not sure where to source this from
            "vapar_plan": vapar_repr,
            "owner_plan": owner_repr,
            "contractor_plan": contractor_repr,
        }

        return repair_plan_repr
