file_names_request_schema = {
    "type": "object",
    "properties": {
        "file_names": {"type": "array", "items": {"type": "string"}, "description": "Comma separated string"},
        "manual_qa": {"type": "boolean"},
        "sewer_data": {"type": "boolean"},
        "standard_key": {"type": "integer"},
        "results_file_id": {"type": "integer"},
        "job_id": {"type": "integer"},
    },
    "required": ["file_names"],
}


file_name_request_schema = {
    "type": "object",
    "properties": {
        "file_name": {"type": "string"},
        "manual_qa": {"type": "boolean"},
        "sewer_data": {"type": "boolean"},
        "standard_key": {"type": "integer"},
        "results_file_id": {"type": "integer"},
        "job_id": {"type": "integer"},
        "download_url": {"type": "string"},
    },
    "required": ["file_name", "download_url"],
}

xml_create_job_request_schema = {
    "type": "object",
    "properties": {
        "file_id": {"type": "integer"},
        "file": {"type": "string", "format": "binary"},
    },
}
