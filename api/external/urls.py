from django.urls import path

from .views import (
    ExternalInspectionsXMLView,
    ExternalJobsListView,
    ExternalSASView,
    ExternalStandardsListView,
    ExternalSubmitJobView,
    ExternalSubmitJobsView,
    ExternalUploadProgressView,
    ExternalVideoImagesView,
    ExternalVideoResultsView,
    ExternalSubmitXmlJobView,
    ExternalFrameView,
    ExternalInspectionListView,
    ExternalExportRetrieveView,
    ExternalExportCreateView,
    ExternalExportOutputListView,
)

urlpatterns = [
    path("external/job/submit", ExternalSubmitJobView.as_view()),
    path("external/standards", ExternalStandardsListView.as_view()),
    path("external/video/images", ExternalVideoImagesView.as_view()),
    path("external/video/results", ExternalVideoResultsView.as_view()),
    path("external/upload/paths", ExternalSubmitJobsView.as_view()),
    path("external/upload/progress", ExternalUploadProgressView.as_view()),
    path("external/inspections/xml", ExternalInspectionsXMLView.as_view()),
    path("external/joblist", ExternalJobsListView.as_view()),
    path("external/sas", ExternalSASView.as_view()),
    path("external/job/submit/xml", ExternalSubmitXmlJobView.as_view()),
    path("external/inspections/<int:id>/frame", ExternalFrameView.as_view()),
    path("external/inspections", ExternalInspectionListView.as_view()),
    path("external/exports", ExternalExportCreateView.as_view()),
    path("external/exports/<uuid:uuid>", ExternalExportRetrieveView.as_view()),
    path("external/exports/<uuid:uuid>/outputs", ExternalExportOutputListView.as_view()),
]
