# Generated by Django 4.1.2 on 2024-03-19 06:40

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("inspections", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="UserActions",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("user_name", models.CharField(max_length=200)),
                ("organisation", models.Char<PERSON>ield(max_length=200)),
                ("action", models.Char<PERSON>ield(max_length=200)),
                ("login_timestamp", models.DateTimeField()),
            ],
            options={
                "db_table": "service_useractions",
            },
        ),
        migrations.CreateModel(
            name="AuditList",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("event_type", models.CharField(blank=True, max_length=20, null=True)),
                ("table", models.CharField(blank=True, max_length=100, null=True)),
                ("row_id", models.IntegerField(blank=True, null=True)),
                ("column", models.CharField(blank=True, max_length=100, null=True)),
                (
                    "description",
                    models.CharField(blank=True, max_length=1000, null=True),
                ),
                ("date_of_modification", models.DateTimeField(blank=True, null=True)),
                (
                    "asset_id",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="inspections.mappointlist",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "service_auditlist",
            },
        ),
    ]
