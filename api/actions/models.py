from django.db import models

from api.users.models import CustomUser
from api.inspections.models import MapPointList


class UserActions(models.Model):
    user_name = models.CharField(max_length=200, blank=False)
    organisation = models.CharField(max_length=200, blank=False)
    action = models.CharField(max_length=200, blank=False)
    login_timestamp = models.DateTimeField(blank=False)

    class Meta:
        db_table = "service_useractions"


class AuditList(models.Model):
    event_type = models.CharField(max_length=20, blank=True, null=True)
    table = models.CharField(max_length=100, blank=True, null=True)
    row_id = models.IntegerField(blank=True, null=True)
    column = models.Char<PERSON>ield(max_length=100, blank=True, null=True)
    description = models.CharField(max_length=1000, blank=True, null=True)
    date_of_modification = models.DateTimeField(blank=True, null=True)
    user = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True)
    asset_id = models.ForeignKey(MapPointList, on_delete=models.SET_NULL, null=True)

    class Meta:
        db_table = "service_auditlist"
