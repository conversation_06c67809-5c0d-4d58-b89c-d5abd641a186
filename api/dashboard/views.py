from typing import Any

from django.db.models import Count, Q, Sum
from django.db.models.fields import Float<PERSON>ield
from django.db.models.functions import Cast
from django.http import JsonResponse
from drf_spectacular.utils import extend_schema, OpenApiParameter
from rest_framework import generics
from rest_framework import status as response_status
from rest_framework.exceptions import NotFound
from rest_framework.response import Response
from rest_framework.status import HTTP_200_OK

from api.common.permissions import IsStandardUser, IsAuthenticated
from api.dashboard.serializer import CustomDisplayDashboardSerializer
from api.inspections.models import MapPointList
from api.organisations.models import AssetOwners, Organisations

DASHBOARD_PARAMS = [
    OpenApiParameter("organisation", int, required=True, description="Organisation ID"),
]


class DashboardBaseView(generics.GenericAPIView):
    permission_classes = [IsAuthenticated, IsStandardUser]
    pagination_class = None

    userobj: Any = None
    is_asset_owner: bool = False

    def get_queryset(self, limit_chainage=False):
        self.userobj = self.request.user

        self.is_asset_owner = self.request.organisation.is_asset_owner

        organisation = self.request.query_params.get("organisation", None)

        try:
            organisation = Organisations.objects.get(id=organisation)
        except (Organisations.DoesNotExist, ValueError):
            raise NotFound("Organisation not found")

        if self.is_asset_owner:
            points = MapPointList.objects.select_related("associated_file").filter(
                ~Q(associated_file__hidden=True), Q(associated_file__target_org=self.request.organisation)
            )
        else:
            points = MapPointList.objects.select_related("associated_file").filter(
                ~Q(associated_file__hidden=True),
                Q(associated_file__target_org=organisation),
                Q(associated_file__upload_org=self.request.organisation),
            )

        if limit_chainage:
            points = points.filter(~Q(chainage__gt=10000))

        queryset = points.filter(deleted_at__isnull=True)

        return queryset


class DashboardListView(DashboardBaseView):

    serializer_class = CustomDisplayDashboardSerializer

    @extend_schema(parameters=DASHBOARD_PARAMS)
    def get(self, request, *args, **kwargs):
        """
        Returns list of inspections with dashboard relevent data
        """
        points = self.get_queryset().order_by("date_captured")
        points = points.exclude(geometry__isnull=True)
        is_ao_exist = AssetOwners.objects.filter(org=self.request.organisation).exists()

        if self.is_asset_owner and is_ao_exist:
            related_contractor_count = self.request.organisation.assetowners.contractor.count()

            if related_contractor_count > 0:
                related_contractors = self.request.organisation.assetowners.contractor.all()
                points = points.exclude(
                    associated_file__upload_org__contractors__in=related_contractors, status="Uploaded"
                )

        ret_json = CustomDisplayDashboardSerializer(points, many=True)

        return Response(ret_json.data, response_status.HTTP_200_OK)


class DashboardStructuralScoresView(DashboardBaseView):
    @extend_schema(
        parameters=DASHBOARD_PARAMS,
        responses={HTTP_200_OK: dict[str, Any]},
    )
    def get(self, request, *args, **kwargs):
        """
        Returns the number of inspections for each structural score (1-5)
        """

        # After getting the queryset, we filter again for the range between 1 to 5
        points = self.get_queryset().filter(condition_rating__range=(1, 5)).values("condition_rating")
        # Not all asset onwers have a related assetowner object because we only create asset onwer when we assign a contractor for them
        # Here if ao exist, then there is a contractor, so we need to remove those points where status = uploaded on dashboard
        # Because asset owners should not be able to see the scores before handover
        is_ao_exist = AssetOwners.objects.filter(org=request.organisation).exists()

        if self.is_asset_owner and is_ao_exist:
            related_contractor_count = request.organisation.assetowners.contractor.count()

            if related_contractor_count > 0:
                related_contractors = request.organisation.assetowners.contractor.all()
                points = points.exclude(
                    associated_file__upload_org__contractors__in=related_contractors, status="Uploaded"
                )

        structural_score_1 = points.annotate(
            structural_score1=Count("condition_rating", filter=Q(condition_rating=1))
        ).aggregate(Sum("structural_score1"))
        structural_score_2 = points.annotate(
            structural_score2=Count("condition_rating", filter=Q(condition_rating=2))
        ).aggregate(Sum("structural_score2"))
        structural_score_3 = points.annotate(
            structural_score3=Count("condition_rating", filter=Q(condition_rating=3))
        ).aggregate(Sum("structural_score3"))
        structural_score_4 = points.annotate(
            structural_score4=Count("condition_rating", filter=Q(condition_rating=4))
        ).aggregate(Sum("structural_score4"))
        structural_score_5 = points.annotate(
            structural_score5=Count("condition_rating", filter=Q(condition_rating=5))
        ).aggregate(Sum("structural_score5"))

        structural_score_array = [
            structural_score_1.get("structural_score1__sum"),
            structural_score_2.get("structural_score2__sum"),
            structural_score_3.get("structural_score3__sum"),
            structural_score_4.get("structural_score4__sum"),
            structural_score_5.get("structural_score5__sum"),
        ]

        build_response = {"structural_scores": structural_score_array}

        return JsonResponse(build_response)


class DashboardServiceScoresView(DashboardBaseView):
    @extend_schema(
        parameters=DASHBOARD_PARAMS,
        responses={HTTP_200_OK: list[dict]},
    )
    def get(self, request, *args, **kwargs):
        """
        Returns the number of inspections for each service score (1-5)
        """
        points = self.get_queryset().filter(service_condition_rating__range=(1, 5)).values("service_condition_rating")
        is_ao_exist = AssetOwners.objects.filter(org=request.organisation).exists()

        if self.is_asset_owner and is_ao_exist:
            related_contractor_count = request.organisation.assetowners.contractor.count()

            if related_contractor_count > 0:
                related_contractors = request.organisation.assetowners.contractor.all()
                points = points.exclude(
                    associated_file__upload_org__contractors__in=related_contractors, status="Uploaded"
                )

        service_score_1 = points.annotate(
            service_score1=Count("service_condition_rating", filter=Q(service_condition_rating=1))
        ).aggregate(Sum("service_score1"))
        service_score_2 = points.annotate(
            service_score2=Count("service_condition_rating", filter=Q(service_condition_rating=2))
        ).aggregate(Sum("service_score2"))
        service_score_3 = points.annotate(
            service_score3=Count("service_condition_rating", filter=Q(service_condition_rating=3))
        ).aggregate(Sum("service_score3"))
        service_score_4 = points.annotate(
            service_score4=Count("service_condition_rating", filter=Q(service_condition_rating=4))
        ).aggregate(Sum("service_score4"))
        service_score_5 = points.annotate(
            service_score5=Count("service_condition_rating", filter=Q(service_condition_rating=5))
        ).aggregate(Sum("service_score5"))

        service_score_array = [
            service_score_1.get("service_score1__sum"),
            service_score_2.get("service_score2__sum"),
            service_score_3.get("service_score3__sum"),
            service_score_4.get("service_score4__sum"),
            service_score_5.get("service_score5__sum"),
        ]

        build_response = {"service_scores": service_score_array}

        return JsonResponse(build_response)


class DashboardStatusView(DashboardBaseView):
    @extend_schema(
        parameters=DASHBOARD_PARAMS,
        responses={HTTP_200_OK: list[dict]},
    )
    def get(self, request, *args, **kwargs):
        """
        Returns a list of all possible statuses and the number of inspections in each
        """
        points = self.get_queryset().values("status")
        points = points.order_by("status").annotate(total=Count("status"))
        data = list(points)

        # If one of them has no number, the key will be missed, so we add key and value 0 for this case
        # better way to do this is by implementing CASE-WHEN in queryset

        if len(data) < 8:
            if not any(d["status"] == "Actioned" for d in data):
                data.append({"status": "Actioned", "total": 0})
            if not any(d["status"] == "Archived" for d in data):
                data.append({"status": "Archived", "total": 0})
            if not any(d["status"] == "Complete" for d in data):
                data.append({"status": "Complete", "total": 0})
            if not any(d["status"] == "Decision" for d in data):
                data.append({"status": "Decision", "total": 0})
            if not any(d["status"] == "Repair Plan" for d in data):
                data.append({"status": "Repair Plan", "total": 0})
            if not any(d["status"] == "Planned" for d in data):
                data.append({"status": "Planned", "total": 0})
            if not any(d["status"] == "Reviewed" for d in data):
                data.append({"status": "Reviewed", "total": 0})
            if not any(d["status"] == "Uploaded" for d in data):
                data.append({"status": "Uploaded", "total": 0})

        return JsonResponse(data, safe=False)


class DashboardChainageView(DashboardBaseView):
    @extend_schema(
        parameters=DASHBOARD_PARAMS,
        responses={HTTP_200_OK: list[dict]},
    )
    def get(self, request, *args, **kwargs):
        """
        Returns list of inspections with date_captured and chainage
        """
        points = self.get_queryset(limit_chainage=True).exclude(date_captured__isnull=True).values("date_captured")
        points = points.order_by("date_captured").annotate(chainage=Sum(Cast("chainage", FloatField())))
        data = list(points)

        return JsonResponse(data, safe=False)
