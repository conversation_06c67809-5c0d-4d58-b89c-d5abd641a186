from drf_spectacular.utils import extend_schema_field
from rest_framework import serializers
from drf_extra_fields.geo_fields import <PERSON><PERSON><PERSON> as _PointField

from api.inspections.models import VideoFrames


@extend_schema_field(field=dict[str, int])
class PointField(_PointField):
    pass


class CustomDisplayDashboardSerializer(serializers.Serializer):

    id = serializers.IntegerField(read_only=True)
    geometry = PointField(required=False)
    name = serializers.CharField(max_length=200, read_only=True)
    chainage = serializers.FloatField(read_only=True)

    asset_id = serializers.CharField(read_only=True)
    date_captured = serializers.DateField(read_only=True, format="%d/%m/%Y")  # type: ignore
    status = serializers.CharField(read_only=True)
    ratings = serializers.SerializerMethodField(read_only=True)

    def get_ratings(self, obj) -> dict[str, int | None]:
        frames_not_classified = (
            VideoFrames.objects.filter(parent_video=obj.associated_file).filter(is_hidden=False).count()
        )

        condition_rating = None
        service_condition_rating = None

        if frames_not_classified <= 0:
            condition_rating = int(obj.condition_rating) if obj.condition_rating is not None else None
            service_condition_rating = (
                int(obj.service_condition_rating) if obj.service_condition_rating is not None else None
            )

        return {"condition_rating": condition_rating, "service_condition_rating": service_condition_rating}
