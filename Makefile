SHELL=/bin/bash
.SHELLFLAGS = -ec

# Include the .env file if available and any supporting make files for
# Docker directives or Azure or other
-include .env
include make/*.mk

# Run each command in the same shell context instead of spawning a new
# independent shell for each command
.ONESHELL:

# Export all the variables from this make file, including those in the .env file
# So that the build targets get access to the variables at run time
.EXPORT_ALL_VARIABLES:


# Targets that are specific to this project should be created here.
DOCKER_IMAGE_NAME=vapar-api
