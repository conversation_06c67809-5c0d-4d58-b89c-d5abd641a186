vapar @ git+https://x-token-auth:${BITBUCKET_VAPAR_REPO_TOKEN}@bitbucket.org/teamvapar/vapar.git@v0.9.11
annotated-types==0.5.0
ansiwrap==0.8.4
anyio==4.0.0
appnope==0.1.3
argon2-cffi==21.3.0
argon2-cffi-bindings==21.2.0
asgiref==3.5.2
astroid==2.15.6
asttokens==2.1.0
async-timeout==4.0.3
asyncio==3.4.3
attrs==24.3.0
azure-common==1.1.23
azure-core==1.26.1
azure-storage-blob==12.14.1
azure-storage-queue==12.5.0
backcall==0.2.0
beautifulsoup4==4.11.1
black==22.12.0
bleach==5.0.1
Brotli==1.0.9
cairocffi==1.6.1
CairoSVG==2.7.1
certifi==2022.9.24
cffi==1.15.1
chardet==5.0.0
charset-normalizer==2.1.1
click==8.1.3
colorama==0.4.6
coreapi==2.3.3
coreschema==0.0.4
coverage==7.4.3
cryptography==38.0.4
cssselect2==0.7.0
debugpy==1.6.3
decorator==5.1.1
defusedxml==0.7.1
dill==0.3.7
Django==4.1.2
django-cors-headers==3.13.0
django-countries==7.4.2
django-debug-toolbar==4.1.0
django-filter==22.1
django-password-validators==1.7.0
django-redis==5.4.0
django-rest-swagger==2.2.0
django-templated-mail==1.1.1
django-treebeard==4.4
djangorestframework==3.14.0
djangorestframework-camel-case==1.3.0
djangorestframework-simplejwt==5.3.1
docker==6.0.1
drf-extra-fields==3.4.1
entrypoints==0.4
escapism==1.0.1
et-xmlfile==1.1.0
exceptiongroup==1.1.3
executing==1.2.0
fastjsonschema==2.16.2
fonttools==4.38.0
h11==0.14.0
halo==0.0.31
html5lib==1.1
httpcore==0.18.0
httpx==0.25.0
humanfriendly==10.0
idna==3.4
importlib-metadata==5.0.0
inflection==0.5.1
iniconfig==2.0.0
ipykernel==6.10.0
ipython==8.6.0
ipython-genutils==0.2.0
iso8601==1.1.0
isodate==0.6.1
isort==5.12.0
itypes==1.2.0
JayDeBeApi==1.2.3
jedi==0.18.1
Jinja2==3.1.2
JPype1==1.4.1
jsonschema==4.17.0
jupyter-client==6.1.5
jupyter-highlight-selected-word==0.2.0
jupyter-repo2docker==2022.10.0
jupyter-server==1.15.6
jupyter_contrib_core==0.4.2
jupyter_contrib_nbextensions==0.7.0
jupyter_core==5.0.0
jupyter_nbextensions_configurator==0.6.1
jupyterlab-pygments==0.2.2
lazy-object-proxy==1.9.0
log-symbols==0.0.14
lxml==4.9.1
Markdown==3.4.1
MarkupSafe==2.1.1
matplotlib-inline==0.1.6
mccabe==0.7.0
mistune==2.0.4
msrest==0.7.1
mypy-extensions==1.0.0
nbclassic==0.4.8
nbclient==0.7.0
nbconvert==7.2.5
nbformat==5.7.0
nbstripout==0.6.1
nest-asyncio==1.5.6
notebook==6.5.2
notebook_shim==0.2.2
numpy==1.24.1
oauthlib==3.2.2
openapi-codec==1.3.2
opencv-python==********
openpyxl==3.0.10
outcome==1.2.0
packaging==24.1
pandas==1.5.3
pandocfilters==1.5.0
papermill==2.4.0
parso==0.8.3
pathlib==1.0.1
pathspec==0.11.2
pexpect==4.8.0
pickleshare==0.7.5
Pillow==9.4.0
pluggy==1.3.0
prometheus-client==0.15.0
prompt-toolkit==3.0.32
psutil==5.9.4
psycopg2-binary==2.9.5
ptyprocess==0.7.0
pure-eval==0.2.2
pycparser==2.21
pydantic==2.7.1
pydantic-settings==2.1.0
pydantic_core==2.18.2
pydyf==0.9.0
Pygments==2.15.0
PyJWT==2.6.0
pylint==2.16.1
pyparsing==3.0.9
pyphen==0.13.2
pyrsistent==0.19.2
pytest==7.4.2
pytest-asyncio==0.23.5.post1
pytest-cov==4.1.0
pytest-django==4.5.2
python-dateutil==2.8.2
python-decouple==3.6
python-dotenv==0.21.0
python-json-logger==2.0.7
python3-openid==3.2.0
pytz==2022.5
PyYAML==6.0
pyzmq==24.0.1
redis==4.4.2
requests==2.28.1
requests-oauthlib==1.3.1
ruamel.yaml==0.17.21
ruamel.yaml.clib==0.2.7
ruff==0.5.3
semver==2.13.0
Send2Trash==1.8.0
sentry-sdk==1.11.0
simplejson==3.17.6
six==1.16.0
sniffio==1.3.0
social-auth-app-django==4.0.0
social-auth-core==4.3.0
sortedcontainers==2.4.0
soupsieve==2.3.2.post1
spinners==0.0.24
sqlparse==0.4.3
stack-data==0.6.1
tenacity==8.1.0
termcolor==2.1.1
terminado==0.17.0
textwrap3==0.9.2
timeago==1.0.16
tinycss2==1.2.1
toml==0.10.2
tomli==2.0.1
tomlkit==0.12.1
tornado==6.2
tqdm==4.64.1
traitlets==5.5.0
trio==0.22.2
typing_extensions==4.6.1
uritemplate==4.1.1
urllib3==1.26.12
wcwidth==0.2.5
weasyprint==52.5
webencodings==0.5.1
websocket-client==1.4.2
wrapt==1.15.0
zipp==3.10.0
zopfli==0.2.2
jsonref==1.1.0
djangorestframework-api-key==3.0.0
drf-spectacular==0.27.2
yarl==1.18
xmltodict==0.14.2
