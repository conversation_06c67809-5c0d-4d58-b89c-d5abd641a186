# Changelog

## [3.6.12] 2024-10-23
### Added
- Added Backend Tasks infrastructure for handling export reports ([AD-2486](https://teamvapar.atlassian.net/browse/AD-2486))
- Added DELETE endpoint for Inspection ([AD-2519](https://teamvapar.atlassian.net/browse/AD-2519))
- Added DELETE endpoint for Frames ([AD-2518](https://teamvapar.atlassian.net/browse/AD-2518))
- Added more data integrity rules to database ([AD-2480](https://teamvapar.atlassian.net/browse/AD-2480))
- Added PACP8 standard ([AD-2464](https://teamvapar.atlassian.net/browse/AD-2464))
- Added PACP8 export type ([AD-2459](https://teamvapar.atlassian.net/browse/AD-2459))
### Changed
- Changed end node placement on PDF report to align with end of pipe image ([AD-2513](https://teamvapar.atlassian.net/browse/AD-2513))
- Changed PACP7 defect repair alignment ([AD-2445](https://teamvapar.atlassian.net/browse/AD-2445))
### Removed
### Fixed
- Fixed .avi filetype support ([AD-2490](https://teamvapar.atlassian.net/browse/AD-2490))
- Fixed .mpg filetype support ([AD-2436](https://teamvapar.atlassian.net/browse/AD-2436))
- Fixed structural score calculation for AU standards ([AD-2532](https://teamvapar.atlassian.net/browse/AD-2532))
- Fixed 'Play Video' link handlers for old and new style Inspection IDs ([AD-2541](https://teamvapar.atlassian.net/browse/AD-2541))
- Fixed PDF link generation to use VAPAR ID for token generation ([AD-2542](https://teamvapar.atlassian.net/browse/AD-2542))
- Fixed AssetValues being created for different standards than existing values ([AD-2529](https://teamvapar.atlassian.net/browse/AD-2529))
- Fixed occurances of mixed standards in AssetValue sets ([AD-2528](https://teamvapar.atlassian.net/browse/AD-2528))
- Fixed 500 error when 'Play Video' link is pressed ([AD-2512](https://teamvapar.atlassian.net/browse/AD-2512))
- Fixed timeout error during ExtractFrame activity (PF) ([AD-2509](https://teamvapar.atlassian.net/browse/AD-2509))
- Fixed status count not taking into account filters ([AD-2506](https://teamvapar.atlassian.net/browse/AD-2506))
- Fixed validation modal hanging when attempting to 'continue with errors' ([AD-2496](https://teamvapar.atlassian.net/browse/AD-2496))
- Fixed issue with search query not being cleared when folder selection is changed ([AD-2468](https://teamvapar.atlassian.net/browse/AD-2468))
- Fixed deleted inspections not being cleared from selection count ([AD-2477](https://teamvapar.atlassian.net/browse/AD-2477))


## [3.5]
### Added
- Added generic GET /files endpoint ([AD-1863](https://teamvapar.atlassian.net/browse/AD-1863))
- Added createdAt field to LoginSerializer for CustomUser ([AD-2184](https://teamvapar.atlassian.net/browse/AD-2184))
- Added default_repair_param to a standard for use when creating an org ([AD-1742](https://teamvapar.atlassian.net/browse/AD-1742))
- Added new defect records via migration ([AD-1903](https://teamvapar.atlassian.net/browse/AD-1903)) ([AD-1904](https://teamvapar.atlassian.net/browse/AD-1904)) ([AD-1936](https://teamvapar.atlassian.net/browse/AD-1936))
- Added user and org details to defect_updated analytics events ([AD-2297](https://teamvapar.atlassian.net/browse/AD-2297))
- Added POST inspections/videos/<id>/frames endpoint ([AD-1984](https://teamvapar.atlassian.net/browse/AD-1984)) ([AD-2127](https://teamvapar.atlassian.net/browse/AD-2127))
- Added POST assets/match-or-create endpoint ([AD-2042](https://teamvapar.atlassian.net/browse/AD-2042))
- Added GET inspections2/ optional query params: status, use_inspection_filters, date_captured, asset_id, folder_id ([AD-2147](https://teamvapar.atlassian.net/browse/AD-2147))
- Added PATCH inspections2/<uuid> optional query param: use_header_names ([AD-2147](https://teamvapar.atlassian.net/browse/AD-2147))
- Created 'exports' app and export models ([AD-1996](https://teamvapar.atlassian.net/browse/AD-1996))
- Added POST /exports endpoint ([AD-1997](https://teamvapar.atlassian.net/browse/AD-1997))
- Added GET /exports and GET /exports/<id>/payload endpoints ([AD-2303](https://teamvapar.atlassian.net/browse/AD-2303))
- Added GET and POST exports/<uuid>/outputs endpoints, added PATCH exports/<uuid> endpoint ([AD-1999](https://teamvapar.atlassian.net/browse/AD-1999))
- Added use_header_names query param to GET /inspections/<uuid> ([AD-2280](https://teamvapar.atlassian.net/browse/AD-2280))
- Added more filtering options to GET /defects ([AD-2280](https://teamvapar.atlassian.net/browse/AD-2280))
- Added GET /inspections2/<uuid>/frames endpoint ([AD-2280](https://teamvapar.atlassian.net/browse/AD-2280))
- Added GET /exports endpoint ([AD-2000](https://teamvapar.atlassian.net/browse/AD-2000))
- Added frame state data to defect update events ([AD-2310](https://teamvapar.atlassian.net/browse/AD-2310))
- Added statusCounts to export list endpoint ([AD-2369](https://teamvapar.atlassian.net/browse/AD-2369))
- Added is_hidden flag to export model ([AD-2382](https://teamvapar.atlassian.net/browse/AD-2382))
- Added asset analytics events ([AD-2308](https://teamvapar.atlassian.net/browse/AD-2308))
- Added inspection analytics events ([AD-2309](https://teamvapar.atlassian.net/browse/AD-2309))
- Added pre-export validation step ([AD-2335](https://teamvapar.atlassian.net/browse/AD-2335))
### Changed
- Updated some defects via migrations ([AD-1902](https://teamvapar.atlassian.net/browse/AD-1902)) ([AD-2144](https://teamvapar.atlassian.net/browse/AD-2144)) ([AD-2188](https://teamvapar.atlassian.net/browse/AD-2188))
- Filled in missing upload_completed_time on existing files ([AD-2238](https://teamvapar.atlassian.net/browse/AD-2238))
- Added extra information to AuditList description when deleting a processing file ([AD-2227](https://teamvapar.atlassian.net/browse/AD-2227))
- Updated defect scoring to use vaparlib implementation ([AD-2150](https://teamvapar.atlassian.net/browse/AD-2150))
- Updated asset, defect, and IAM exports to include/replace some values ([AD-2271](https://teamvapar.atlassian.net/browse/AD-2271)) ([AD-2274](https://teamvapar.atlassian.net/browse/AD-2274))
- Updated DefectScores objects via migration for CSRQ ([AD-2328](https://teamvapar.atlassian.net/browse/AD-2328))
- Improved performance of Inspections page filtering and ordering ([AD-2259](https://teamvapar.atlassian.net/browse/AD-2259))
- Refactor internal blob storage handling ([AD-2418](https://teamvapar.atlassian.net/browse/AD-2418))
### Removed
- Legacy Scoring look-up table
- Legacy Exports code
- MappointList migration command
### Fixed
- Fixed upload only users permissions on standards list ([AD-2282](https://teamvapar.atlassian.net/browse/AD-2282))
- Fixed recently processed list showing failed & processing files ([AD-2240](https://teamvapar.atlassian.net/browse/AD-2238))
- Fixed NZ Direction handling ([AD-2194](https://teamvapar.atlassian.net/browse/AD-2194))
- Fixed missing filter records via migration + ensure creation of filter records on user create ([AD-2301](https://teamvapar.atlassian.net/browse/AD-2301))
- Fixed POST inspections2/<uuid> handling of null values ([AD-2147](https://teamvapar.atlassian.net/browse/AD-2147))
- Fixed PATCH inspections2/<uuid> handling of valid values not yet created ([AD-2147](https://teamvapar.atlassian.net/browse/AD-2147))
- Fixed GET inspections/<id>/frames always generating a SAS token, even when one is in the cache ([AD-2280](https://teamvapar.atlassian.net/browse/AD-2280))
- Fixed PATCH inspections/move/<id> to accept inspection uuids ([AD-2384](https://teamvapar.atlassian.net/browse/2384))
- Fixed inspection direction filtering only matching enum values ([AD-2338](https://teamvapar.atlassian.net/browse/AD-2338))
- Fix fetching video when just checking existence ([AD-2418](https://teamvapar.atlassian.net/browse/AD-2418))

## [3.4.10] 2024-07-04
### Added
- Expand search fields to all in table ([AD-2100](https://teamvapar.atlassian.net/browse/AD-2100))
### Changed
- Default folder in inspection model to None ([AD-2041](https://teamvapar.atlassian.net/browse/AD-2041))
- Renamed Inspection pydantic model to InspectionModel ([AD-2147](https://teamvapar.atlassian.net/browse/AD-2147))
### Removed
### Fixed
- Ignore case when checking for duplicate AssetIDs ([AD-2112](https://teamvapar.atlassian.net/browse/AD-2112))

## [3.4.5] 2024-06-27
### Added
### Changed
### Removed
### Fixed
- Fixed switching assets in validation ([AD-2090](https://teamvapar.atlassian.net/browse/AD-2090))

## [3.4.4] 2024-06-27
### Added
### Changed
### Removed
- removed migrations from startup
### Fixed

## [3.4.3] 2024-06-27
### Added
### Changed
### Removed
### Fixed
- Improved asset list performance: added caching + ordering ([AD-2016](https://teamvapar.atlassian.net/browse/AD-2016))

## [3.4.2] 2024-06-20
### Added
### Changed
### Removed
### Fixed
- Fixed defect score migrations ([AD-1956](https://teamvapar.atlassian.net/browse/AD-1956))
- Fix gradings to have option inspections

## [3.4.0] 2024-06-13
### Added
- Added service user flag and API key authentication ([AD-1843](https://teamvapar.atlassian.net/browse/AD-1843))
- Supporting search on the asset list ([AD-1849](https://teamvapar.atlassian.net/browse/AD-1849))
- Delete assets endpoint ([AD-2023](https://teamvapar.atlassian.net/browse/AD-2023))
### Changed
- Forced standard header options to be an array, and constrain the data type field. ([AD-1778](https://teamvapar.atlassian.net/browse/AD-1778))
- Consolidated the asset validation and asset endpoints. Added some tests ([AD-1834](https://teamvapar.atlassian.net/browse/AD-1834))
- Configure logging using shared lib config ([AD-1940](https://teamvapar.atlassian.net/browse/AD-1940))
### Removed
### Fixed
- view_disabled logic moved so not affected by caching and added per user ([AD-1910](https://teamvapar.atlassian.net/browse/AD-1910))
- Get CSV exports working with inspections rather than mpls ([AD-2010](https://teamvapar.atlassian.net/browse/AD-2010))

## [3.3.25] 2024-06-06
### Added
### Changed
### Removed
### Fixed
- Fixed PDF and MDB export failures ([AD-2015](https://teamvapar.atlassian.net/browse/AD-2015))

## [3.3.24] 2024-06-04
### Added
### Changed
### Removed
### Fixed
- Improved inspection query on exports to fix timeouts ([AD-1966](https://teamvapar.atlassian.net/browse/AD-1966))

## [3.3.23] 2024-05-21
### Added
### Changed
### Removed
### Fixed
- Fixed event trigger for uploaded file from external api ([AD-1957](https://teamvapar.atlassian.net/browse/AD-1957))
- Fixed defect scores and modellist records, reset to production values with additions ([AD-1956](https://teamvapar.atlassian.net/browse/AD-1956))

## [3.3.22] 2024-05-20
### Added
### Changed
### Removed
### Fixed
- Fixed Location patching

## [3.3.21] 2024-05-17
### Added
### Changed
### Removed
### Fixed
- Fixed error when updating inspection notes to blank ([AD-1945](https://teamvapar.atlassian.net/browse/AD-1945))
- Fixed CSV exports with file date ([AD-1942](https://teamvapar.atlassian.net/browse/AD-1942))

## [3.3.19] 2024-05-16
### Added
### Changed
### Removed
### Fixed
- Fixed check on empty request object ([AD-1926](https://teamvapar.atlassian.net/browse/AD-1926))

## [3.3.18] 2024-05-16
### Added
### Changed
### Removed
### Fixed
- Fixed Inspections Upload column field name ([AD-1879](https://teamvapar.atlassian.net/browse/AD-1879))

## [3.3.17] 2024-05-15
### Added
### Changed
### Removed
### Fixed
- Fixed diameter reference in csv exports ([AD-1887](https://teamvapar.atlassian.net/browse/AD-1887))

## [3.3.16] 2024-05-13
### Added
### Changed
- Consolidated the asset validation and asset endpoints. Added some tests ([AD-1834](https://teamvapar.atlassian.net/browse/AD-1834))
- Forced standard header options to be an array, and constrain the data type field. ([AD-1778](https://teamvapar.atlassian.net/browse/AD-1778))
### Removed
### Fixed
- Fix failing activation method ([AD-1893](https://teamvapar.atlassian.net/browse/AD-1893))
- Fixed error on GET /inspections2 ordering ([AD-1882](https://teamvapar.atlassian.net/browse/AD-1882))
- Fixed incorrect repairs payload property ([AD-1892](https://teamvapar.atlassian.net/browse/AD-1892))
- Fixed is_option not handling NoneType ([AD-1885](https://teamvapar.atlassian.net/browse/AD-1885))
- Enforce clock value range, migrate to remove existing invalid vals ([AD-1880](https://teamvapar.atlassian.net/browse/AD-1880))
- Fixed string operation on integer in csv exports ([AD-1887](https://teamvapar.atlassian.net/browse/AD-1887))

## [3.3.9] 2024-05-02
### Added
### Changed
### Removed
### Fixed
- Fixed inspections performance regression ([AD-1853](https://teamvapar.atlassian.net/browse/AD-1853))

## [3.3.8] 2024-04-30
### Added
### Changed
- Updated error response objects for more detailed messaging ([AD-1825](https://teamvapar.atlassian.net/browse/AD-1825))
### Removed
### Fixed
- Fixed Weasyprint version dependancy issue ([AD-1813]((https://teamvapar.atlassian.net/browse/AD-1813)))
- Fixed metadata (inspection details) updates issues ([AD-1837](https://teamvapar.atlassian.net/browse/AD-1837))
- Fixed duplicate asset ID create/update issues ([AD-1836](https://teamvapar.atlassian.net/browse/AD-1836))
- Fixed chainage number field type and default value ([AD-1817](https://teamvapar.atlassian.net/browse/AD-1817))

## [3.3.6] 2024-04-17
### Added
- Added cracks, fractures and surface damage (spalling and corrosion) defect codes data with migration ([AD-1747](https://teamvapar.atlassian.net/browse/AD-1747))
- Added roots defect codes data with migration ([AD-1802](https://teamvapar.atlassian.net/browse/AD-1802))
- Added GET asset list and POST asset endpoints ([AD-1722](https://teamvapar.atlassian.net/browse/AD-1722))
- Added PATCH asset endpoint ([AD-1724](https://teamvapar.atlassian.net/browse/AD-1724))
- Added POST inspection endpoint ([AD-1725](https://teamvapar.atlassian.net/browse/AD-1725))
- Added VAPAR id generation for new inspections ([AD-1806](https://teamvapar.atlassian.net/browse/AD-1806))
### Changed
- Changed headers with options to also validate against a default set of options ([AD-1474](https://teamvapar.atlassian.net/browse/AD-1474))
### Removed
### Fixed
- Fixed MPL data migration command and added integrity checks ([AD-1731](https://teamvapar.atlassian.net/browse/AD-1731))

## [3.3.1] 2024-04-05
### Added
### Changed
- Updated activation email template + minor changes to activation endpoint ([AD-1744](https://teamvapar.atlassian.net/browse/AD-1744))
### Removed
### Fixed

## [3.3.0] 2024-04-04
### Added
- Added vapar shared lib as dependency ([AD-1674](https://teamvapar.atlassian.net/browse/AD-1674))
- Added analytics wrapper client for eventhub ([AD-1674](https://teamvapar.atlassian.net/browse/AD-1674))
- Added analytics events for repair recommendations and defect creation ([AD-1674](https://teamvapar.atlassian.net/browse/AD-1674), [AD-1354](https://teamvapar.atlassian.net/browse/AD-1354))
- Added Joint displacement, surface damage and deposits defect codes data with migration ([AD-1699](https://teamvapar.atlassian.net/browse/AD-1699))
### Changed
- Renamed top level module from 'vapar' to 'api' ([AD-1674](https://teamvapar.atlassian.net/browse/AD-1674))
- Updated tests to use a test db ([AD-1689](https://teamvapar.atlassian.net/browse/AD-1689))
### Removed
- Removed migration files and regenerated them. ([AD-1689](https://teamvapar.atlassian.net/browse/AD-1689))
### Fixed
- Fixed Clock validation for defects ([AD-1500](https://teamvapar.atlassian.net/browse/AD-1500))
- Fixed InspectionFilter model not generating default records ([AD-1745](https://teamvapar.atlassian.net/browse/AD-1690))
- Fix inpections loading performance ([AD-1729](https://teamvapar.atlassian.net/browse/AD-1729))
- Run repair recommendations when bulk updating away from uploaded status ([AD-1751](https://teamvapar.atlassian.net/browse/AD-1751))

## [3.2.48] 2024-03-14
### Added
### Changed
### Removed
### Fixed
- Hotfixed date conversion in report exports ([AD-1692](https://teamvapar.atlassian.net/browse/AD-1692))

## [3.2.47] 2024-04-14
### Added
### Changed
- Update checks in webhook endpoint for AU region uploads
### Removed
### Fixed


## [3.2.46] 2024-02-27
### Added
### Changed
### Removed
### Fixed
- Fixed date handling in serialization transformation ([AD-1682](https://teamvapar.atlassian.net/browse/AD-1682))

## [3.2.45] 2024-02-21
### Added
### Changed
### Removed
- Remove org type mapping on fetch orgs endpoint ([AD-2248](https://teamvapar.atlassian.net/browse/AD-2248)
### Fixed
- Fixed Defect export not mapping length and date formats ([AD-1653](https://teamvapar.atlassian.net/browse/AD-1653))
- Standard value validation not returning correct type ([AD-1647](https://teamvapar.atlassian.net/browse/AD-1647))
- Fixed patch count on full pipe repair ([AD-1604](https://teamvapar.atlassian.net/browse/AD-1604))
- Fixed updating as asset value via validation is now syncing to all relevant mpl records ([AD-1641](https://teamvapar.atlassian.net/browse/AD-1641))
- Fixed the direction syncing issue when using the validation tool. ([AD-1625](https://teamvapar.atlassian.net/browse/AD-1625))
- Fixed inspection model not supporting empty strings on some fields ([AD-1666](https://teamvapar.atlassian.net/browse/AD-1666))

## [3.2.39] 2024-02-15
### Added
### Changed
### Removed
### Fixed
- Fixed defect chainage and date formatting in defect report ([AD-1620](https://teamvapar.atlassian.net/browse/AD-1620))
- Fixed default ordering after inspection merging ([AD-1612](https://teamvapar.atlassian.net/browse/AD-1612))
- Fixed XML export General Remarks mssing Vapar ID ([AD-1617](https://teamvapar.atlassian.net/browse/AD-1617))

## [3.2.36] 2024-02-08
### Added
### Changed
### Removed
### Fixed
- Fixed missing defect data from defect report ([AD-1589](https://teamvapar.atlassian.net/browse/AD-1589))

## [3.2.35] 2024-01-31
### Added
### Changed
### Removed
### Fixed
- Fixed Unreviewed contractor/asset owner inspections permissions ([AD-1548](https://teamvapar.atlassian.net/browse/AD-1548)) 
- Fixed bulk export of repairrecommendations/defects ([AD-1550](https://teamvapar.atlassian.net/browse/AD-1550))
- Fixed repair recommendations generation on all status from 'Review' ([AD-1534](https://teamvapar.atlassian.net/browse/AD-1534))
- Fixed Inspection Length sorting ([AD-1567](https://teamvapar.atlassian.net/browse/AD-1567))

## [3.2.31] 2024-01-18
### Added
### Changed
### Removed
### Fixed
- Fixed repair recommendations for whole pipe repairs not showing ([AD-1510](https://teamvapar.atlassian.net/browse/AD-1510))
- Fixed inspection sorting on folders ([AD-1527](https://teamvapar.atlassian.net/browse/AD-1527))

## [3.2.29] 2024-01-15
### Added
### Changed
### Removed
### Fixed
- Fixed inspection standard exports ([AD-1512](https://teamvapar.atlassian.net/browse/AD-1512))

## [3.2.28] 2024-01-15
### Added
### Changed
- Changed the field display names for inspection details ([AD-1492](https://teamvapar.atlassian.net/browse/AD-1492))
### Removed
### Fixed
- Fixed inspection status counts ([AD-1485](https://teamvapar.atlassian.net/browse/AD-1485))
- Fixed matching check with correct instance ([AD-1486](https://teamvapar.atlassian.net/browse/AD-1486))
- Fixed inpsection list not filtering by search terms ([AD-1489](ttps://teamvapar.atlassian.net/browse/AD-1489))

## [3.2.24] 2024-01-11
### Added
### Changed
### Removed
### Fixed
- Fixed ordering of the file upload date on inspection list table ([AD-1451](https://teamvapar.atlassian.net/browse/AD-1451))

## [3.2.23] 2024-01-10
### Added
- Added org_type and pagination query params to organisations GET endpoint ([AD-1422](https://teamvapar.atlassian.net/browse/AD-1422))
- Supporting ordering fields that are nested in the inspections "file" record ([AD-1451](https://teamvapar.atlassian.net/browse/AD-1451))
### Changed
### Removed
### Fixed
- Set Weasyprint to 52.5 for pango dependancies ([AD-1452](https://teamvapar.atlassian.net/browse/AD-1452))
- Fixed direction filtering ([AD-1404](https://teamvapar.atlassian.net/browse/AD-1404))
- Fixed Inspection Patch error message ([AD-1472](https://teamvapar.atlassian.net/browse/AD-1472))
- Fixed Repair Recommendations generation calculation ([AD-1453]((https://teamvapar.atlassian.net/browse/AD-1453)))
- Fixed Inspection Value Standard Header reference in PATCH ([AD-1469](https://teamvapar.atlassian.net/browse/AD-1469))
- Fixed units showing feet incorrectly ([AD-1455](https://teamvapar.atlassian.net/browse/AD-1455))

## [3.2.15] 2023-12-14
### Added
- Added pagination, sorting and search to all Userlist ([AD-1323](https://teamvapar.atlassian.net/browse/AD-1323))
- Added short name to Org sorting and search ([AD-1350](https://teamvapar.atlassian.net/browse/AD-1350))
- Set the ordering of the header list to match existing FE form ([AD-1391](https://teamvapar.atlassian.net/browse/AD-1391))
- Added PATCH endpoint for inspections2/ ([AD-1404](https://teamvapar.atlassian.net/browse/AD-1404))
### Changed
### Removed
### Fixed
- Fixed updating Inspection folder reference during bulk move ([AD-1573](https://teamvapar.atlassian.net/browse/AD-1573))

## [3.2.11] 2023-11-29
### Added
### Changed
### Removed
### Fixed
- Add Header UUID and name to inspection values ([AD-1322](https://teamvapar.atlassian.net/browse/AD-1322))
- Add inspection name and start/end node details to infoasset report ([AD-1318](https://teamvapar.atlassian.net/browse/AD-1318))
- Add is_editable column + set Structural and Service grade to filterable ([AD-1313](https://teamvapar.atlassian.net/browse/AD-1313))
- Remove Users pagination class to allow all records ([AD-1341](https://teamvapar.atlassian.net/browse/AD-1341))
- Migration to update "Abandoned" type defect scores ([AD-1317](https://teamvapar.atlassian.net/browse/AD-1317))

## [3.2.7] 2023-11-22
### Added
### Changed
- Add header uuid to response ([AD-1301](https://teamvapar.atlassian.net/browse/AD-1301))
- Add ExternalFrameView ([AD-1254](https://teamvapar.atlassian.net/browse/AD-1254))
### Removed
### Fixed
- Allow assetId and lenght to be NoneType ([AD-1291](https://teamvapar.atlassian.net/browse/AD-1291))
- Add is_active to UserDetail seriliazer ([AD-1302](https://teamvapar.atlassian.net/browse/AD-1302))
- Set repair_param default to repair params rules object ([AD-1311](https://teamvapar.atlassian.net/browse/AD-1311))

## [3.2.1] 2023-11-09
### Added
- Add upstream adn downstream node to External API json payload ([AD-1273](https://teamvapar.atlassian.net/browse/AD-1273))
### Changed
- Simplify inspection list filtering ([AD-1250](https://teamvapar.atlassian.net/browse/AD-1250))
- Updated NZ V4 Lateral Open and Blank defect codes ([AD-1270](https://teamvapar.atlassian.net/browse/AD-1270))
### Removed
### Fixed
- Allow pydantic NoneType for inspection user ([AS-1280](https://teamvapar.atlassian.net/browse/AD-1280))
- Repair Recommendations not using correct chainage ([AD-1269](https://teamvapar.atlassian.net/browse/AD-1269))

## [3.2.0] 2023-11-01
### Added
- Added get organisations endpoint ([AD-1246](https://teamvapar.atlassian.net/browse/AD-1246))
- Set up unit tests ([AD-818](https://teamvapar.atlassian.net/browse/AD-818))
- UserList and UserDetail endpoint ([AD-1150](https://teamvapar.atlassian.net/browse/AD-1150))
- Added UserOrganisationContextOptions endpoint ([AD-1226](https://teamvapar.atlassian.net/browse/AD-1226))
- Add reset password and resend activation endpoints ([AD-1213](https://teamvapar.atlassian.net/browse/AD-1226))
### Changed
- Changed standard header srializer tp inclide mapped fields ([AD-1216](https://teamvapar.atlassian.net/browse/AD-1216))
### Removed
- Deprecated Decision Made status and add org permission to inspection list
### Fixed
- Clean up some bits and pieces, added matching logic to new endpoint ([AD-1010](https://teamvapar.atlassian.net/browse/AD-1010))
- Validation - MSCC5 compliance ([AD-1208](https://teamvapar.atlassian.net/browse/AD-1208))
- Organisation country not updating on PATCH ([AD-1248](https://teamvapar.atlassian.net/browse/AD-1248))

## [3.1.15] 2023-10-06
### Added
### Changed
- Updated Frame payload and user relations in external API
### Removed
### Fixed

## [3.1.14] 2023-10-06
### Added
- Added Vapar version to API headers
### Changed
- Defect change tracking improvement
### Removed
### Fixed
- Fixed wrong standard header naming

## [3.1.13] 2023-10-03
### Added
### Changed
- Update standard headers ordering and display flag
### Removed
### Fixed

## [3.1.12] 2023-09-20
### Added
### Changed
### Removed
### Fixed
- Fixed float operation on None values ([AD-1199](https://teamvapar.atlassian.net/browse/AD-1199))

## [3.1.11] 2023-09-14
### Added
### Changed
### Removed
### Fixed
- Fixed PDF chainage errors ([AD-1191](https://teamvapar.atlassian.net/browse/AD-1191))
- Fixed case sensitivity on emails ([AD-1194](https://teamvapar.atlassian.net/browse/AD-1194))

## [3.1.10] 2023-09-08
### Added
### Changed
### Removed
### Fixed
- Fixed missing frame property ([AD-1187](https://teamvapar.atlassian.net/browse/AD-1187))
- Fixed shareable image render ([AD-1175](https://teamvapar.atlassian.net/browse/AD-1175))

## [3.1.9] 2023-09-06
### Added
### Changed
### Removed
### Fixed
- Fixed null property conditional checks ([AD-1169](https://teamvapar.atlassian.net/browse/AD-1169))
- Fixed password reset ([AD-410](https://teamvapar.atlassian.net/browse/AD-410))

## [3.1.8] 2023-08-23
### Added
### Changed
### Removed
### Fixed
- Fixed Defect CSV with continuous defects ([AD-1149](https://teamvapar.atlassian.net/browse/AD-1149)) 
- InfoAsset Asset CSV not containing correct material data ([AD-1073](https://teamvapar.atlassian.net/browse/AD-1073))

## [3.1.7] 2023-08-15
### Added
### Changed
- Updated webhook endpoint to only process if upload_completed not set
- Use more compatible cache key for SAS Token
### Removed
### Fixed


## [3.1.6] 2023-08-11
### Added
### Changed
### Removed
### Fixed
- Fixed processing associated file not being updated

## [3.1.5] 2023-08-10
### Added
### Changed
- Enqueue processing function for external file upload
### Removed
### Fixed

## [3.1.4] 2023-08-08
### Added
### Changed
### Removed
### Fixed
- Fixed non-string boolean in video upload and set upload_completed ([AD-231](https://teamvapar.atlassian.net/browse/AD-231))

## [3.1.3] 2023-08-07
### Added
### Changed
- Improve caching of tokens
### Removed
### Fixed
- Fixed status counts ([AD-1119](https://teamvapar.atlassian.net/browse/AD-1119))

## [3.1.2] 2023-08-03
### Added
### Changed
### Removed
### Fixed
- Fixed PDF frames not showing ([AD-1112](https://teamvapar.atlassian.net/browse/AD-1112))

## [3.1.1] 2023-08-02
### Added
### Changed
### Removed
### Fixed
- Fixed Repair Recommendations not being created on status change ([AD-1113](https://teamvapar.atlassian.net/browse/AD-1113))

## [3.1.0] 2023-07-28
### Added
- Added InspectionFilterDetail view - get method ([AD-1019](https://teamvapar.atlassian.net/browse/AD-1019))
- Added InspectionFilterDetail view - patch method ([AD-1018](https://teamvapar.atlassian.net/browse/AD-1018))
- Update frame quantity and units when updating a frame ([AD-1004](https://teamvapar.atlassian.net/browse/AD-1004))
- Added ProcessList endpoint to receive Azire BlobCreate event payload ([AD-1103](https://teamvapar.atlassian.net/browse/AD-1103))
- Added missing VAPAR icon for PDF's ([AD-993](https://teamvapar.atlassian.net/browse/AD-993))
### Changed
- Updated InspectionFilter model based on new Advanced Filtering design ([AD-1017](https://teamvapar.atlassian.net/browse/AD-1017))
### Removed
### Fixed
- Sending back defect id on defect update for correct at joint disabling ([AD-1050](https://teamvapar.atlassian.net/browse/AD-1050))
- Fixed not being able to filter by decision while other status filters are included ([AD-1098](https://teamvapar.atlassian.net/browse/AD-1098))

## [3.0.7] 2023-07-20
### Added
### Changed
- restructure repairs endpoint for easy FE interpretation ([AD-1045](https://teamvapar.atlassian.net/browse/AD-1045))
### Removed
### Fixed
- Fixed user reset errors ([AD-1064](https://teamvapar.atlassian.net/browse/AD-1064))
- Corrected row number on DRT error messages

## [3.0.6] 2023-07-15
### Added
### Changed
### Removed
### Fixed
- Fixed Repair Recommendations email link ([AD-1028](https://teamvapar.atlassian.net/browse/AD-1028))

## [3.0.5] 2023-07-13
### Added
### Changed
### Removed
### Fixed
- Fixed LinksCSV large exports ([AD-1054](https://teamvapar.atlassian.net/browse/AD-1054))
- Fixed inspections table not sortable by Uploaded ([AD-1023](https://teamvapar.atlassian.net/browse/AD-1023))
- Fixed sas token url encoding ([AD-1053](https://teamvapar.atlassian.net/browse/AD-1053))
- Fixed Link CSV cutting off values ([AD-756](https://teamvapar.atlassian.net/browse/AD-756))

## [3.0.4] 2023-07-12
### Added
### Changed
- Allow External API to use new Processing Function ([AD-1047](https://teamvapar.atlassian.net/browse/AD-1047))
### Removed
### Fixed

## [3.0.3] - 2023-07-11

### Added
### Changed
### Removed
### Fixed
- Fixed bulk defect csv download ([AD-1031](https://teamvapar.atlassian.net/browse/AD-1031))
- Fixed deleting a processing file can now be done by the upload user or a user in the asset owner org ([AD-1029](https://teamvapar.atlassian.net/browse/AD-1029))
- Fixed updating frame defect send back defect_class ([AD-1022](https://teamvapar.atlassian.net/browse/AD-1022))
- Fixed asset csv export ([AD-978](https://teamvapar.atlassian.net/browse/AD-978))
- Removed quantity 2 validation on percentage required flag ([AD-1003](https://teamvapar.atlassian.net/browse/AD-1003))

## [3.0.2] - 2023-07-10

### Added
### Changed
### Removed
### Fixed
- Fixed user can't download file uploaded ([AD-1006](https://teamvapar.atlassian.net/browse/AD-1006))
- Fixed showing files for uploaded by current user ([AD-1007](https://teamvapar.atlassian.net/browse/AD-1007))

## [3.1.0] - unreleased
### Changed
- Changed backend storage location for file uploads ([AD-1062](https://teamvapar.atlassian.net/browse/AD-1062))
### Removed
- Any urlencoding of SAS tokens
- References to new vs old PF storage blob uses. Only new PF storage blob in use.
### Fixed
- Optimised standards validation 