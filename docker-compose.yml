# Verion of docker-compose to use 
version: "3"

volumes:
  dbdata:

services:
  redis-service:
    image: "redis:alpine"
  database:
    image: itauedevcrvapar.azurecr.io/vapar-database:1.0.1
    restart: "no"
    ports:
      - "5432:5432"
    expose:
      - 5432
    volumes:
      - dbdata:/var/lib/postgresql/vapardb/data/
    environment:
      - DB_DROPDB_ON_STARTUP=false
      - DB_RUN_INIT_SCRIPT=false
  api:
    image: itauedevcrvapar.azurecr.io/vapar-api:latest
    #build: .
    #command: sh -c "gunicorn --workers=4 --timeout=300 config.wsgi:application -b 0.0.0.0:8000 --limit-request-line 0"
    ports:
      - "8000:8000"
    expose:
      - 8000
    environment:
      - DB_HOST=database
      - DB_NAME=vapardb
      - DB_USER=vaparlocal
      - DB_PASS=vapariseme
      - DB_PORT=5432
    depends_on:
      - redis-service
      - database