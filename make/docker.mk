DOCKER_IMAGE_NAME ?= ""
DOCKER_IMAGE_TAG ?= latest

AZURE_RESOURCE_GROUP=vapar-auei-dev-arg-v3
AKS_CLUSTER_NAME=vaparise


docker-login:
	docker login ${DOCKER_REGISTRY_FQDN} -u ${DOCKER_REGISTRY_USERNAME} -p $(value DOCKER_REGISTRY_PASSWORD)

docker-build:
	docker build -f Dockerfile -t ${DOCKER_REGISTRY_FQDN}/${DOCKER_IMAGE_NAME}:${DOCKER_IMAGE_TAG} --build-arg BITBUCKET_VAPAR_REPO_TOKEN=${BITBUCKET_VAPAR_REPO_TOKEN} .

docker-push: docker-login
	docker push ${DOCKER_REGISTRY_FQDN}/${DOCKER_IMAGE_NAME}:${DOCKER_IMAGE_TAG}

azure-login:
	az login --service-principal -u ${AZURE_CLIENT_ID} -p ${AZURE_SECRET} --tenant ${AZURE_TENANT}
	az account set --subscription ${AZURE_SUBSCRIPTION_ID}

azure-aks-get-credentials:
	az aks get-credentials --resource-group ${AZURE_RESOURCE_GROUP} --name ${AKS_CLUSTER_NAME}

azure-aks-restart:
	kubectl --namespace ${AKS_NAMESPACE} set env deployment/api VERSION=${BUILD_SOURCEVERSION}
	kubectl rollout restart deployment/api --namespace ${AKS_NAMESPACE}

install-az:
	curl -sL https://aka.ms/InstallAzureCLIDeb | bash
	apt-get update && apt-get install gpg wget  -y
	wget -qO- https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor | tee /usr/share/keyrings/microsoft-prod.gpg
	wget -q https://packages.microsoft.com/config/debian/12/prod.list
	mv prod.list /etc/apt/sources.list.d/microsoft-prod.list
	chown root:root /usr/share/keyrings/microsoft-prod.gpg
	chown root:root /etc/apt/sources.list.d/microsoft-prod.list
	apt-get update && apt-get install azure-functions-core-tools-4 libicu-dev  -y

# Source: https://kubernetes.io/docs/tasks/tools/install-kubectl-linux/#install-using-native-package-management
install-kubectl:
	apt-get update && apt-get install -y apt-transport-https ca-certificates curl gnupg
	curl -fsSL https://pkgs.k8s.io/core:/stable:/v1.32/deb/Release.key | gpg --dearmor -o /etc/apt/keyrings/kubernetes-apt-keyring.gpg
	chmod 644 /etc/apt/keyrings/kubernetes-apt-keyring.gpg # allow unprivileged APT programs to read this keyring
	echo 'deb [signed-by=/etc/apt/keyrings/kubernetes-apt-keyring.gpg] https://pkgs.k8s.io/core:/stable:/v1.32/deb/ /' | tee /etc/apt/sources.list.d/kubernetes.list
	chmod 644 /etc/apt/sources.list.d/kubernetes.list
	apt-get update && apt-get install -y kubectl
